import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  CircularProgress,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Divider,
} from '@mui/material';
import {
  PlayArrow,
  Assessment,
  TrendingUp,
  TrendingDown,
  Timeline,
  Speed,
} from '@mui/icons-material';
import axios from 'axios';

interface Strategy {
  name: string;
  description: string;
  category: string;
  risk_level: string;
  validated: boolean;
}

interface BacktestRequest {
  symbol: string;
  timeframe: number;
  start_date: string;
  end_date: string;
  initial_capital: number;
  margin: number;
  commission: number;
  strategy_config: Record<string, any>;
  generate_files: boolean;
}

interface BacktestResults {
  execution_time: number;
  symbol: string;
  timeframe: number;
  data_points: number;
  performance_summary: {
    total_trades: number;
    win_rate: number;
    total_return: number;
    max_drawdown: number;
    sharpe_ratio: number;
  };
  custom_metrics?: {
    profit_factor: number;
    risk_reward_ratio: number;
    avg_trade_duration_minutes: number;
    total_pnl: number;
  };
}

const BacktestingDashboard: React.FC = () => {
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [selectedStrategy, setSelectedStrategy] = useState<string>('');
  const [backtestType, setBacktestType] = useState<'reference' | 'hybrid'>('reference');
  const [backtestParams, setBacktestParams] = useState<BacktestRequest>({
    symbol: 'NIFTY50',
    timeframe: 30,
    start_date: '2024-01-01',
    end_date: '2024-12-31',
    initial_capital: 30000,
    margin: 0.1,
    commission: 0.0,
    strategy_config: {},
    generate_files: false,
  });
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<BacktestResults | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    fetchStrategies();
  }, []);

  const fetchStrategies = async () => {
    try {
      const response = await axios.get('http://localhost:8002/api/v1/backtesting/strategies');
      setStrategies(response.data.strategies);
      if (response.data.strategies.length > 0) {
        setSelectedStrategy(response.data.strategies[0].name);
      }
    } catch (error) {
      console.error('Error fetching strategies:', error);
      setError('Failed to fetch strategies');
    }
  };

  const runBacktest = async () => {
    if (!selectedStrategy) {
      setError('Please select a strategy');
      return;
    }

    setIsRunning(true);
    setError(null);
    setResults(null);
    setProgress(0);

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setProgress((prev) => Math.min(prev + 10, 90));
    }, 500);

    try {
      const endpoint = backtestType === 'reference'
        ? 'http://localhost:8002/api/v1/backtesting/reference/run'
        : 'http://localhost:8002/api/v1/backtesting/run';

      const requestData = {
        ...backtestParams,
        strategy_name: selectedStrategy,
        strategy_params: backtestParams.strategy_config || {}
      };

      const response = await axios.post(endpoint, requestData, { timeout: 120000 });

      if (response.data) {
        setResults(response.data);
        setProgress(100);
      } else {
        setError('Backtest failed - no results returned');
      }
    } catch (error: any) {
      console.error('Error running backtest:', error);
      setError(error.response?.data?.detail || 'Failed to run backtest');
    } finally {
      clearInterval(progressInterval);
      setIsRunning(false);
      setProgress(0);
    }
  };

  const formatNumber = (value: number, decimals: number = 2): string => {
    return value.toFixed(decimals);
  };

  const formatDuration = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes.toFixed(0)}m`;
    }
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins.toFixed(0)}m`;
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Reference Backtesting Framework
      </Typography>
      <Typography variant="subtitle1" color="text.secondary" gutterBottom>
        High-performance backtesting with SimplePriceActionStrategy
      </Typography>

      <Grid container spacing={3}>
        {/* Configuration Panel */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Backtest Configuration
            </Typography>

            <FormControl fullWidth margin="normal">
              <InputLabel>Strategy</InputLabel>
              <Select
                value={selectedStrategy}
                onChange={(e) => setSelectedStrategy(e.target.value)}
                label="Strategy"
              >
                {strategies.map((strategy) => (
                  <MenuItem key={strategy.name} value={strategy.name}>
                    <Box>
                      <Typography variant="body2">{strategy.name}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {strategy.description}
                      </Typography>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth margin="normal">
              <InputLabel>Backtest Type</InputLabel>
              <Select
                value={backtestType}
                onChange={(e) => setBacktestType(e.target.value as 'reference' | 'hybrid')}
                label="Backtest Type"
              >
                <MenuItem value="reference">
                  <Box>
                    <Typography variant="body2">Reference Engine</Typography>
                    <Typography variant="caption" color="text.secondary">
                      Fast, optimized backtesting engine
                    </Typography>
                  </Box>
                </MenuItem>
                <MenuItem value="hybrid">
                  <Box>
                    <Typography variant="body2">Hybrid Engine</Typography>
                    <Typography variant="caption" color="text.secondary">
                      Advanced backtesting with detailed analysis
                    </Typography>
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>

            <TextField
              fullWidth
              margin="normal"
              label="Symbol"
              value={backtestParams.symbol}
              onChange={(e) =>
                setBacktestParams({ ...backtestParams, symbol: e.target.value })
              }
            />

            <TextField
              fullWidth
              margin="normal"
              label="Timeframe (minutes)"
              type="number"
              value={backtestParams.timeframe}
              onChange={(e) =>
                setBacktestParams({ ...backtestParams, timeframe: parseInt(e.target.value) })
              }
            />

            <TextField
              fullWidth
              margin="normal"
              label="Start Date"
              type="date"
              value={backtestParams.start_date}
              onChange={(e) =>
                setBacktestParams({ ...backtestParams, start_date: e.target.value })
              }
              InputLabelProps={{ shrink: true }}
            />

            <TextField
              fullWidth
              margin="normal"
              label="End Date"
              type="date"
              value={backtestParams.end_date}
              onChange={(e) =>
                setBacktestParams({ ...backtestParams, end_date: e.target.value })
              }
              InputLabelProps={{ shrink: true }}
            />

            <TextField
              fullWidth
              margin="normal"
              label="Initial Capital"
              type="number"
              value={backtestParams.initial_capital}
              onChange={(e) =>
                setBacktestParams({ ...backtestParams, initial_capital: parseFloat(e.target.value) })
              }
            />

            <TextField
              fullWidth
              margin="normal"
              label="Margin"
              type="number"
              step="0.01"
              value={backtestParams.margin}
              onChange={(e) =>
                setBacktestParams({ ...backtestParams, margin: parseFloat(e.target.value) })
              }
            />

            <Button
              fullWidth
              variant="contained"
              size="large"
              startIcon={isRunning ? <CircularProgress size={20} /> : <PlayArrow />}
              onClick={runBacktest}
              disabled={isRunning}
              sx={{ mt: 3 }}
            >
              {isRunning ? 'Running Backtest...' : 'Run Backtest'}
            </Button>

            {isRunning && (
              <Box sx={{ mt: 2 }}>
                <LinearProgress variant="determinate" value={progress} />
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                  Progress: {progress}%
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Results Panel */}
        <Grid item xs={12} md={8}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {results && (
            <Grid container spacing={2}>
              {/* Performance Summary Cards */}
              <Grid item xs={12}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Performance Summary
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6} sm={3}>
                      <Card>
                        <CardContent sx={{ textAlign: 'center' }}>
                          <TrendingUp color="primary" sx={{ fontSize: 40 }} />
                          <Typography variant="h6">
                            {formatNumber(results.performance_summary.total_return)}%
                          </Typography>
                          <Typography variant="caption">Total Return</Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Card>
                        <CardContent sx={{ textAlign: 'center' }}>
                          <Assessment color="secondary" sx={{ fontSize: 40 }} />
                          <Typography variant="h6">
                            {formatNumber(results.performance_summary.win_rate)}%
                          </Typography>
                          <Typography variant="caption">Win Rate</Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Card>
                        <CardContent sx={{ textAlign: 'center' }}>
                          <TrendingDown color="error" sx={{ fontSize: 40 }} />
                          <Typography variant="h6">
                            {formatNumber(Math.abs(results.performance_summary.max_drawdown))}%
                          </Typography>
                          <Typography variant="caption">Max Drawdown</Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Card>
                        <CardContent sx={{ textAlign: 'center' }}>
                          <Speed color="info" sx={{ fontSize: 40 }} />
                          <Typography variant="h6">
                            {formatNumber(results.execution_time)}s
                          </Typography>
                          <Typography variant="caption">Execution Time</Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>

              {/* Detailed Metrics Table */}
              <Grid item xs={12}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Detailed Metrics
                  </Typography>
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Metric</TableCell>
                          <TableCell align="right">Value</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        <TableRow>
                          <TableCell>Total Trades</TableCell>
                          <TableCell align="right">{results.performance_summary.total_trades}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Win Rate</TableCell>
                          <TableCell align="right">{formatNumber(results.performance_summary.win_rate)}%</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Total Return</TableCell>
                          <TableCell align="right">{formatNumber(results.performance_summary.total_return)}%</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Max Drawdown</TableCell>
                          <TableCell align="right">{formatNumber(Math.abs(results.performance_summary.max_drawdown))}%</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Sharpe Ratio</TableCell>
                          <TableCell align="right">{formatNumber(results.performance_summary.sharpe_ratio)}</TableCell>
                        </TableRow>
                        {results.custom_metrics && (
                          <>
                            <TableRow>
                              <TableCell>Profit Factor</TableCell>
                              <TableCell align="right">{formatNumber(results.custom_metrics.profit_factor)}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>Risk-to-Reward Ratio</TableCell>
                              <TableCell align="right">{formatNumber(results.custom_metrics.risk_reward_ratio)}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>Avg Trade Duration</TableCell>
                              <TableCell align="right">{formatDuration(results.custom_metrics.avg_trade_duration_minutes)}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>Total P&L</TableCell>
                              <TableCell align="right">₹{formatNumber(results.custom_metrics.total_pnl)}</TableCell>
                            </TableRow>
                          </>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Paper>
              </Grid>

              {/* Execution Info */}
              <Grid item xs={12}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Execution Information
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">
                        Symbol: <strong>{results.symbol}</strong>
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Timeframe: <strong>{results.timeframe} minutes</strong>
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Data Points: <strong>{results.data_points.toLocaleString()}</strong>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">
                        Execution Time: <strong>{formatNumber(results.execution_time)} seconds</strong>
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Processing Rate: <strong>{formatNumber(results.data_points / results.execution_time)} bars/sec</strong>
                      </Typography>
                      <Chip
                        label="Reference Framework"
                        color="primary"
                        size="small"
                        sx={{ mt: 1 }}
                      />
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>
            </Grid>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default BacktestingDashboard;
