"""
Support and resistance technical indicators.
"""

from typing import Dict, Any, Union, List
import pandas as pd
import numpy as np

from .base import BaseIndicator


class PivotPoints(BaseIndicator):
    """Pivot Points indicator."""
    
    def __init__(self):
        """Initialize Pivot Points."""
        super().__init__("PivotPoints", {})
    
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calculate Pivot Points values."""
        df = self._prepare_data(data)
        
        if len(df) < 1:
            return {
                "pivot": [], "r1": [], "r2": [], "r3": [],
                "s1": [], "s2": [], "s3": [], "timestamps": []
            }
        
        # Calculate pivot points for each period
        pivot_points = []
        r1_levels = []
        r2_levels = []
        r3_levels = []
        s1_levels = []
        s2_levels = []
        s3_levels = []
        
        for i in range(len(df)):
            high = df['high'].iloc[i]
            low = df['low'].iloc[i]
            close = df['close'].iloc[i]
            
            # Calculate pivot point
            pivot = (high + low + close) / 3
            
            # Calculate resistance levels
            r1 = (2 * pivot) - low
            r2 = pivot + (high - low)
            r3 = high + 2 * (pivot - low)
            
            # Calculate support levels
            s1 = (2 * pivot) - high
            s2 = pivot - (high - low)
            s3 = low - 2 * (high - pivot)
            
            pivot_points.append(pivot)
            r1_levels.append(r1)
            r2_levels.append(r2)
            r3_levels.append(r3)
            s1_levels.append(s1)
            s2_levels.append(s2)
            s3_levels.append(s3)
        
        # Store values
        self.values = {
            'pivot': pivot_points,
            'r1': r1_levels,
            'r2': r2_levels,
            'r3': r3_levels,
            's1': s1_levels,
            's2': s2_levels,
            's3': s3_levels
        }
        self.timestamps = df['timestamp'].tolist()
        self.is_ready = len(self.values['pivot']) > 0
        
        return {
            "pivot": self.values['pivot'],
            "r1": self.values['r1'],
            "r2": self.values['r2'],
            "r3": self.values['r3'],
            "s1": self.values['s1'],
            "s2": self.values['s2'],
            "s3": self.values['s3'],
            "timestamps": self.timestamps
        }


class FibonacciRetracements(BaseIndicator):
    """Fibonacci Retracements indicator."""
    
    def __init__(self, lookback_period: int = 50):
        """
        Initialize Fibonacci Retracements.
        
        Args:
            lookback_period: Period to look back for high/low calculation
        """
        super().__init__("FibonacciRetracements", {"lookback_period": lookback_period})
        self.lookback_period = lookback_period
    
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calculate Fibonacci Retracements values."""
        df = self._prepare_data(data)
        
        if len(df) < self.lookback_period:
            return {
                "high": [], "low": [], "fib_0": [], "fib_236": [], "fib_382": [],
                "fib_500": [], "fib_618": [], "fib_786": [], "fib_1000": [], "timestamps": []
            }
        
        # Fibonacci ratios
        fib_ratios = {
            'fib_0': 0.0,
            'fib_236': 0.236,
            'fib_382': 0.382,
            'fib_500': 0.500,
            'fib_618': 0.618,
            'fib_786': 0.786,
            'fib_1000': 1.000
        }
        
        # Calculate rolling high and low
        rolling_high = df['high'].rolling(window=self.lookback_period).max()
        rolling_low = df['low'].rolling(window=self.lookback_period).min()
        
        # Calculate Fibonacci levels
        fib_levels = {}
        for level_name, ratio in fib_ratios.items():
            fib_levels[level_name] = rolling_high - (rolling_high - rolling_low) * ratio
        
        # Store values
        self.values = {
            'high': rolling_high.dropna().tolist(),
            'low': rolling_low.dropna().tolist(),
            **{level: values.dropna().tolist() for level, values in fib_levels.items()}
        }
        
        self.timestamps = df['timestamp'][self.lookback_period-1:].tolist()
        self.is_ready = len(self.values['high']) > 0
        
        return {
            "high": self.values['high'],
            "low": self.values['low'],
            **{level: self.values[level] for level in fib_ratios.keys()},
            "timestamps": self.timestamps,
            "lookback_period": self.lookback_period
        }
