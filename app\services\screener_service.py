"""
Symbol screener service for strategy-based symbol selection.
"""

from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
import pandas as pd
import numpy as np

from app.core.logging import get_logger
from app.database.models import Symbol, MarketType, ScreenerResult
from app.services.data_service import DataService
from app.services.indicator_service import IndicatorService
from app.database.repositories.symbol_repository import SymbolRepository

logger = get_logger(__name__)


class ScreenerCriteria:
    """Screener criteria configuration."""
    
    def __init__(self):
        self.price_filters = {}
        self.volume_filters = {}
        self.technical_filters = {}
        self.fundamental_filters = {}
        self.custom_filters = []
    
    def add_price_filter(self, min_price: float = None, max_price: float = None):
        """Add price range filter."""
        if min_price is not None:
            self.price_filters['min_price'] = min_price
        if max_price is not None:
            self.price_filters['max_price'] = max_price
    
    def add_volume_filter(self, min_volume: int = None, avg_volume_days: int = 20):
        """Add volume filter."""
        if min_volume is not None:
            self.volume_filters['min_volume'] = min_volume
            self.volume_filters['avg_volume_days'] = avg_volume_days
    
    def add_technical_filter(self, indicator: str, condition: str, value: float, parameters: Dict = None):
        """Add technical indicator filter."""
        if indicator not in self.technical_filters:
            self.technical_filters[indicator] = []
        
        self.technical_filters[indicator].append({
            'condition': condition,  # 'above', 'below', 'between', 'crossover_above', 'crossover_below'
            'value': value,
            'parameters': parameters or {}
        })
    
    def add_custom_filter(self, filter_function: callable, name: str):
        """Add custom filter function."""
        self.custom_filters.append({
            'name': name,
            'function': filter_function
        })


class ScreenerService:
    """Service for symbol screening based on various criteria."""
    
    def __init__(self, db: Session):
        """
        Initialize screener service.
        
        Args:
            db: Database session
        """
        self.db = db
        self.data_service = DataService(db)
        self.indicator_service = IndicatorService()
        self.symbol_repo = SymbolRepository(db)
    
    def screen_symbols(
        self,
        criteria: ScreenerCriteria,
        market_types: List[str] = None,
        symbols: List[str] = None,
        timeframe: str = "1d",
        lookback_days: int = 30
    ) -> List[Dict[str, Any]]:
        """
        Screen symbols based on criteria.
        
        Args:
            criteria: Screening criteria
            market_types: Filter by market types
            symbols: Specific symbols to screen (if None, screen all)
            timeframe: Data timeframe for analysis
            lookback_days: Number of days to look back
            
        Returns:
            List of symbols that match criteria
        """
        try:
            # Get symbols to screen
            if symbols:
                symbol_objects = [self.symbol_repo.get_by_symbol(s) for s in symbols]
                symbol_objects = [s for s in symbol_objects if s is not None]
            else:
                symbol_objects = self._get_symbols_for_screening(market_types)
            
            if not symbol_objects:
                logger.warning("No symbols found for screening")
                return []
            
            logger.info(f"Screening {len(symbol_objects)} symbols with {timeframe} timeframe")
            
            # Screen each symbol
            results = []
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=lookback_days)
            
            for symbol_obj in symbol_objects:
                try:
                    result = self._screen_single_symbol(
                        symbol_obj, criteria, timeframe, start_date, end_date
                    )
                    if result:
                        results.append(result)
                        
                except Exception as e:
                    logger.error(f"Error screening symbol {symbol_obj.symbol}: {e}")
                    continue
            
            # Sort results by score (if available)
            results.sort(key=lambda x: x.get('score', 0), reverse=True)
            
            logger.info(f"Screening completed: {len(results)} symbols passed criteria")
            return results
            
        except Exception as e:
            logger.error(f"Error in symbol screening: {e}")
            return []
    
    def _get_symbols_for_screening(self, market_types: List[str] = None) -> List[Symbol]:
        """Get symbols for screening based on market types."""
        try:
            filters = {'is_active': True}
            
            if market_types:
                # Convert string market types to enum values
                market_type_enums = []
                for mt in market_types:
                    try:
                        market_type_enums.append(MarketType(mt.upper()))
                    except ValueError:
                        logger.warning(f"Invalid market type: {mt}")
                
                if market_type_enums:
                    filters['market_type'] = market_type_enums
            
            return self.symbol_repo.get_all(filters=filters, limit=1000)
            
        except Exception as e:
            logger.error(f"Error getting symbols for screening: {e}")
            return []
    
    def _screen_single_symbol(
        self,
        symbol_obj: Symbol,
        criteria: ScreenerCriteria,
        timeframe: str,
        start_date: datetime,
        end_date: datetime
    ) -> Optional[Dict[str, Any]]:
        """Screen a single symbol against criteria."""
        try:
            symbol = symbol_obj.symbol
            
            # Get market data
            if timeframe == "1m":
                data = self.data_service.get_ohlcv_data(
                    symbol, start_date, end_date, as_dataframe=True
                )
            else:
                data = self.data_service.get_aggregated_data(
                    symbol, timeframe, start_date, end_date
                )
            
            if data is None or len(data) == 0:
                return None
            
            # Convert to DataFrame if needed
            if not isinstance(data, pd.DataFrame):
                data = pd.DataFrame(data)
            
            # Initialize result
            result = {
                'symbol': symbol,
                'symbol_id': symbol_obj.id,
                'name': symbol_obj.name,
                'market_type': symbol_obj.market_type.value,
                'current_price': float(data['close'].iloc[-1]),
                'volume': int(data['volume'].iloc[-1]),
                'timestamp': data['timestamp'].iloc[-1],
                'passed_filters': [],
                'failed_filters': [],
                'score': 0
            }
            
            # Apply price filters
            if not self._apply_price_filters(data, criteria.price_filters, result):
                return None
            
            # Apply volume filters
            if not self._apply_volume_filters(data, criteria.volume_filters, result):
                return None
            
            # Apply technical filters
            if not self._apply_technical_filters(data, criteria.technical_filters, result):
                return None
            
            # Apply custom filters
            if not self._apply_custom_filters(data, criteria.custom_filters, result):
                return None

            # Calculate overall score
            result['score'] = len(result['passed_filters'])

            # Add additional metrics
            result['price_change_1d'] = self._calculate_price_change(data, 1)
            result['price_change_7d'] = self._calculate_price_change(data, 7)
            result['volatility'] = self._calculate_volatility(data)

            return result
            
        except Exception as e:
            logger.error(f"Error screening symbol {symbol_obj.symbol}: {e}")
            return None
    
    def _apply_price_filters(self, data: pd.DataFrame, filters: Dict, result: Dict) -> bool:
        """Apply price-based filters."""
        if not filters:
            return True
        
        current_price = float(data['close'].iloc[-1])
        
        if 'min_price' in filters:
            if current_price < filters['min_price']:
                result['failed_filters'].append(f"Price below {filters['min_price']}")
                return False
            else:
                result['passed_filters'].append(f"Price above {filters['min_price']}")
        
        if 'max_price' in filters:
            if current_price > filters['max_price']:
                result['failed_filters'].append(f"Price above {filters['max_price']}")
                return False
            else:
                result['passed_filters'].append(f"Price below {filters['max_price']}")
        
        return True
    
    def _apply_volume_filters(self, data: pd.DataFrame, filters: Dict, result: Dict) -> bool:
        """Apply volume-based filters."""
        if not filters:
            return True
        
        if 'min_volume' in filters:
            avg_days = filters.get('avg_volume_days', 20)
            avg_volume = data['volume'].tail(avg_days).mean()
            
            if avg_volume < filters['min_volume']:
                result['failed_filters'].append(f"Avg volume below {filters['min_volume']}")
                return False
            else:
                result['passed_filters'].append(f"Avg volume above {filters['min_volume']}")
        
        return True
    
    def _apply_technical_filters(self, data: pd.DataFrame, filters: Dict, result: Dict) -> bool:
        """Apply technical indicator filters."""
        if not filters:
            return True
        
        for indicator_name, conditions in filters.items():
            for condition in conditions:
                try:
                    # Calculate indicator
                    indicator_result = self.indicator_service.calculate_indicator(
                        indicator_name, data, condition['parameters']
                    )
                    
                    if not indicator_result:
                        result['failed_filters'].append(f"{indicator_name} calculation failed")
                        return False
                    
                    # Check condition
                    if not self._check_technical_condition(
                        indicator_result, condition, indicator_name, result
                    ):
                        return False
                        
                except Exception as e:
                    logger.error(f"Error applying technical filter {indicator_name}: {e}")
                    result['failed_filters'].append(f"{indicator_name} filter error")
                    return False
        
        return True
    
    def _check_technical_condition(
        self, indicator_result: Dict, condition: Dict, indicator_name: str, result: Dict
    ) -> bool:
        """Check if technical condition is met."""
        try:
            condition_type = condition['condition']
            value = condition['value']
            
            # Get latest indicator value
            if 'values' in indicator_result:
                if not indicator_result['values']:
                    return False
                latest_value = indicator_result['values'][-1]
            else:
                # For complex indicators like MACD
                if indicator_name == 'MACD' and 'macd' in indicator_result:
                    latest_value = indicator_result['macd'][-1] if indicator_result['macd'] else None
                else:
                    return False
            
            if latest_value is None:
                return False
            
            # Apply condition
            if condition_type == 'above':
                passed = latest_value > value
            elif condition_type == 'below':
                passed = latest_value < value
            elif condition_type == 'between':
                # Value should be a tuple/list [min, max]
                passed = value[0] <= latest_value <= value[1]
            else:
                logger.warning(f"Unknown condition type: {condition_type}")
                return False
            
            # Update result
            filter_desc = f"{indicator_name} {condition_type} {value}"
            if passed:
                result['passed_filters'].append(filter_desc)
            else:
                result['failed_filters'].append(filter_desc)
            
            return passed
            
        except Exception as e:
            logger.error(f"Error checking technical condition: {e}")
            return False
    
    def _apply_custom_filters(self, data: pd.DataFrame, filters: List, result: Dict) -> bool:
        """Apply custom filter functions."""
        if not filters:
            return True
        
        for filter_config in filters:
            try:
                filter_func = filter_config['function']
                filter_name = filter_config['name']
                
                passed = filter_func(data)
                
                if passed:
                    result['passed_filters'].append(filter_name)
                else:
                    result['failed_filters'].append(filter_name)
                    return False
                    
            except Exception as e:
                logger.error(f"Error applying custom filter {filter_config['name']}: {e}")
                result['failed_filters'].append(f"{filter_config['name']} error")
                return False
        
        return True
    
    def save_screener_results(
        self,
        strategy_id: int,
        results: List[Dict[str, Any]],
        criteria_description: str
    ) -> bool:
        """Save screener results to database."""
        try:
            # Clear previous results for this strategy
            self.db.query(ScreenerResult).filter(
                ScreenerResult.strategy_id == strategy_id
            ).delete()
            
            # Save new results
            for result in results:
                screener_result = ScreenerResult(
                    strategy_id=strategy_id,
                    symbol_id=result['symbol_id'],
                    score=result['score'],
                    criteria_met=len(result['passed_filters']),
                    total_criteria=len(result['passed_filters']) + len(result['failed_filters']),
                    result_data=str(result),  # Store as JSON string
                    screened_at=datetime.utcnow()
                )
                self.db.add(screener_result)
            
            self.db.commit()
            logger.info(f"Saved {len(results)} screener results for strategy {strategy_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving screener results: {e}")
            self.db.rollback()
            return False

    def _calculate_price_change(self, data: pd.DataFrame, days: int) -> float:
        """Calculate price change percentage over specified days."""
        try:
            if len(data) < days + 1:
                return 0.0

            current_price = float(data['close'].iloc[-1])
            past_price = float(data['close'].iloc[-(days + 1)])

            return ((current_price - past_price) / past_price) * 100

        except Exception:
            return 0.0

    def _calculate_volatility(self, data: pd.DataFrame, days: int = 20) -> float:
        """Calculate price volatility (standard deviation of returns)."""
        try:
            if len(data) < days:
                return 0.0

            returns = data['close'].pct_change().tail(days)
            return float(returns.std() * 100)  # Convert to percentage

        except Exception:
            return 0.0
