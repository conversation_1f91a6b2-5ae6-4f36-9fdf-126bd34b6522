"""
Symbol screener API schemas.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field

from app.api.schemas.common import BaseResponse


class PriceFilter(BaseModel):
    """Price filter configuration."""
    min_price: Optional[float] = Field(default=None, description="Minimum price")
    max_price: Optional[float] = Field(default=None, description="Maximum price")


class VolumeFilter(BaseModel):
    """Volume filter configuration."""
    min_volume: Optional[int] = Field(default=None, description="Minimum average volume")
    avg_volume_days: int = Field(default=20, description="Days for average volume calculation")


class TechnicalFilter(BaseModel):
    """Technical indicator filter configuration."""
    indicator: str = Field(description="Indicator name")
    condition: str = Field(description="Condition type (above, below, between)")
    value: Any = Field(description="Condition value")
    parameters: Optional[Dict[str, Any]] = Field(default=None, description="Indicator parameters")


class ScreenerRequest(BaseModel):
    """Symbol screener request."""
    price_filters: Optional[PriceFilter] = Field(default=None, description="Price filters")
    volume_filters: Optional[VolumeFilter] = Field(default=None, description="Volume filters")
    technical_filters: Optional[List[TechnicalFilter]] = Field(default=None, description="Technical filters")
    market_types: Optional[List[str]] = Field(default=None, description="Market types to screen")
    symbols: Optional[List[str]] = Field(default=None, description="Specific symbols to screen")
    timeframe: str = Field(default="1d", description="Data timeframe")
    lookback_days: int = Field(default=30, description="Days to look back")


class ScreenerResult(BaseModel):
    """Individual screener result."""
    symbol: str
    symbol_id: int
    name: Optional[str] = None
    market_type: str
    current_price: float
    volume: int
    timestamp: datetime
    passed_filters: List[str]
    failed_filters: List[str]
    score: int
    price_change_1d: float
    price_change_7d: float
    volatility: float


class ScreenerResponse(BaseResponse):
    """Symbol screener response."""
    results: List[ScreenerResult]
    total_screened: int
    total_passed: int
    criteria_summary: Dict[str, Any]


class PresetScreenerRequest(BaseModel):
    """Preset screener request."""
    preset_name: str = Field(description="Preset screener name")
    market_types: Optional[List[str]] = Field(default=None, description="Market types to screen")
    timeframe: str = Field(default="1d", description="Data timeframe")
    lookback_days: int = Field(default=30, description="Days to look back")


class ScreenerPreset(BaseModel):
    """Screener preset configuration."""
    name: str
    description: str
    criteria: Dict[str, Any]


class ScreenerPresetsResponse(BaseResponse):
    """Available screener presets response."""
    presets: List[ScreenerPreset]


class SaveScreenerRequest(BaseModel):
    """Save screener results request."""
    strategy_id: int = Field(description="Strategy ID")
    criteria_description: str = Field(description="Description of screening criteria")
    results: List[ScreenerResult] = Field(description="Screener results")


class SaveScreenerResponse(BaseResponse):
    """Save screener results response."""
    saved_count: int
    strategy_id: int


class ScreenerHistoryResponse(BaseResponse):
    """Screener history response."""
    history: List[Dict[str, Any]]
    total_runs: int
