"""
Application configuration management.
"""

import os
from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings
import yaml
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""

    host: str = Field(default="localhost", env="DATABASE_HOST")
    port: int = Field(default=5432, env="DATABASE_PORT")
    name: str = Field(default="nse_db", env="DATABASE_NAME")
    user: str = Field(default="postgres", env="DATABASE_USER")
    password: str = Field(default="admin", env="DATABASE_PASSWORD")
    timescale_enabled: bool = True

    model_config = {
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "ignore"
    }

    @property
    def url(self) -> str:
        """Get database URL."""
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"


class FyersSettings(BaseSettings):
    """Fyers API configuration settings."""

    client_id: str = Field(default="", env="FYERS_CLIENT_ID")
    secret_key: str = Field(default="", env="FYERS_SECRET_KEY")
    redirect_uri: str = Field(default="http://localhost:8080/callback", env="FYERS_REDIRECT_URI")
    access_token: Optional[str] = Field(default=None, env="FYERS_ACCESS_TOKEN")

    model_config = {
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "ignore"
    }


class RedisSettings(BaseSettings):
    """Redis configuration settings."""

    url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")

    model_config = {
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "ignore"
    }


class APISettings(BaseSettings):
    """API configuration settings."""

    host: str = Field(default="0.0.0.0", env="API_HOST")
    port: int = Field(default=8000, env="API_PORT")
    debug: bool = Field(default=False, env="DEBUG")
    secret_key: str = Field(default="default_secret_key", env="SECRET_KEY")

    model_config = {
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "ignore"
    }


class Settings(BaseSettings):
    """Main application settings."""
    
    # Sub-configurations
    database: DatabaseSettings = DatabaseSettings()
    fyers: FyersSettings = FyersSettings()
    redis: RedisSettings = RedisSettings()
    api: APISettings = APISettings()
    
    # Application settings
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    frontend_url: str = Field(default="http://localhost:3000", env="FRONTEND_URL")
    
    # Trading configuration
    symbols: List[str] = ["NIFTY", "BANKNIFTY", "FINNIFTY", "RELIANCE"]
    market_types: List[str] = ["EQUITY", "INDEX", "FUTURES", "OPTIONS"]
    
    model_config = {
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "ignore"
    }


def load_yaml_config(config_path: str = "config.yaml") -> dict:
    """Load configuration from YAML file."""
    if os.path.exists(config_path):
        with open(config_path, 'r') as file:
            return yaml.safe_load(file)
    return {}


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings
