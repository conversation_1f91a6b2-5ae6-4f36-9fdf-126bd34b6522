"""
Main API router for Signal Stack Trading Platform v1.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import (
    auth,
    market_data,
    strategies,
    screener,
    backtesting,
    paper_trading,
    indicators,
    data_management
)

api_router = APIRouter()

# Include authentication endpoints (no auth required)
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["Authentication"]
)

# Include all endpoint routers
api_router.include_router(
    market_data.router,
    prefix="/market-data",
    tags=["Market Data"]
)

api_router.include_router(
    strategies.router,
    prefix="/strategies",
    tags=["Strategies"]
)

api_router.include_router(
    screener.router,
    prefix="/screener",
    tags=["Symbol Screener"]
)

api_router.include_router(
    backtesting.router,
    prefix="/backtesting",
    tags=["Backtesting"]
)

api_router.include_router(
    paper_trading.router,
    prefix="/paper-trading",
    tags=["Paper Trading"]
)

api_router.include_router(
    indicators.router,
    prefix="/indicators",
    tags=["Technical Indicators"]
)

api_router.include_router(
    data_management.router,
    prefix="/data-management",
    tags=["Data Management"]
)
