"""
End-to-end tests for complete trading workflows.
"""

import pytest
from fastapi.testclient import TestClient
import time
from datetime import datetime, timedelta


@pytest.mark.e2e
class TestCompleteBacktestingWorkflow:
    """End-to-end test for complete backtesting workflow."""
    
    def test_full_backtesting_workflow(self, client: TestClient, auth_headers: dict, sample_ohlcv_data):
        """Test complete backtesting workflow from data to results."""
        
        # Step 1: Verify data is available
        symbols_response = client.get("/api/v1/market-data/symbols", headers=auth_headers)
        assert symbols_response.status_code == 200
        symbols = [s["symbol"] for s in symbols_response.json()["symbols"]]
        assert "NIFTY" in symbols
        
        # Step 2: Get available strategies
        strategies_response = client.get("/api/v1/backtesting/strategies", headers=auth_headers)
        assert strategies_response.status_code == 200
        strategies = strategies_response.json()["strategies"]
        strategy_names = [s["name"] for s in strategies]
        assert "ma_crossover" in strategy_names
        
        # Step 3: Run backtest
        backtest_payload = {
            "strategy_name": "ma_crossover",
            "symbol": "NIFTY",
            "start_date": "2024-01-01",
            "end_date": "2024-04-10",
            "parameters": {
                "short_window": 10,
                "long_window": 20
            },
            "initial_capital": 100000
        }
        
        run_response = client.post(
            "/api/v1/backtesting/run",
            json=backtest_payload,
            headers=auth_headers
        )
        
        assert run_response.status_code == 200
        backtest_data = run_response.json()
        assert "backtest_id" in backtest_data
        backtest_id = backtest_data["backtest_id"]
        
        # Step 4: Wait for completion and get results
        max_attempts = 10
        for attempt in range(max_attempts):
            results_response = client.get(
                f"/api/v1/backtesting/results/{backtest_id}",
                headers=auth_headers
            )
            
            if results_response.status_code == 200:
                results = results_response.json()
                assert "performance_metrics" in results
                assert "total_return" in results["performance_metrics"]
                break
            elif results_response.status_code == 202:
                # Still processing, wait and retry
                time.sleep(1)
                continue
            else:
                pytest.fail(f"Unexpected status code: {results_response.status_code}")
        else:
            pytest.fail("Backtest did not complete within expected time")
        
        # Step 5: Verify results structure
        performance = results["performance_metrics"]
        required_metrics = [
            "total_return", "annualized_return", "volatility", 
            "sharpe_ratio", "max_drawdown", "total_trades"
        ]
        for metric in required_metrics:
            assert metric in performance
        
        # Step 6: Get trade history
        trades_response = client.get(
            f"/api/v1/backtesting/results/{backtest_id}/trades",
            headers=auth_headers
        )
        
        assert trades_response.status_code == 200
        trades_data = trades_response.json()
        assert "trades" in trades_data


@pytest.mark.e2e
class TestCompletePaperTradingWorkflow:
    """End-to-end test for complete paper trading workflow."""
    
    def test_full_paper_trading_workflow(self, client: TestClient, auth_headers: dict, sample_ohlcv_data):
        """Test complete paper trading workflow."""
        
        # Step 1: Create paper trading session
        session_payload = {
            "name": "E2E Test Session",
            "initial_capital": 100000,
            "symbols": ["NIFTY"],
            "strategy_name": "manual"
        }
        
        session_response = client.post(
            "/api/v1/paper-trading/sessions",
            json=session_payload,
            headers=auth_headers
        )
        
        assert session_response.status_code == 200
        session_data = session_response.json()
        session_id = session_data["session_id"]
        
        # Step 2: Verify session was created
        get_session_response = client.get(
            f"/api/v1/paper-trading/sessions/{session_id}",
            headers=auth_headers
        )
        
        assert get_session_response.status_code == 200
        session_info = get_session_response.json()
        assert session_info["name"] == "E2E Test Session"
        assert session_info["initial_capital"] == 100000
        
        # Step 3: Place a buy order
        buy_order_payload = {
            "symbol": "NIFTY",
            "side": "BUY",
            "quantity": 1,
            "order_type": "MARKET"
        }
        
        buy_response = client.post(
            f"/api/v1/paper-trading/sessions/{session_id}/orders",
            json=buy_order_payload,
            headers=auth_headers
        )
        
        assert buy_response.status_code == 200
        buy_order_data = buy_response.json()
        buy_order_id = buy_order_data["order_id"]
        
        # Step 4: Verify order was placed
        orders_response = client.get(
            f"/api/v1/paper-trading/sessions/{session_id}/orders",
            headers=auth_headers
        )
        
        assert orders_response.status_code == 200
        orders_data = orders_response.json()
        assert len(orders_data["orders"]) >= 1
        
        # Step 5: Check portfolio positions
        portfolio_response = client.get(
            f"/api/v1/paper-trading/sessions/{session_id}/portfolio",
            headers=auth_headers
        )
        
        assert portfolio_response.status_code == 200
        portfolio_data = portfolio_response.json()
        assert "positions" in portfolio_data
        assert "total_value" in portfolio_data
        
        # Step 6: Place a sell order
        sell_order_payload = {
            "symbol": "NIFTY",
            "side": "SELL",
            "quantity": 1,
            "order_type": "MARKET"
        }
        
        sell_response = client.post(
            f"/api/v1/paper-trading/sessions/{session_id}/orders",
            json=sell_order_payload,
            headers=auth_headers
        )
        
        assert sell_response.status_code == 200
        
        # Step 7: Get session performance
        performance_response = client.get(
            f"/api/v1/paper-trading/sessions/{session_id}/performance",
            headers=auth_headers
        )
        
        assert performance_response.status_code == 200
        performance_data = performance_response.json()
        assert "total_pnl" in performance_data
        assert "total_trades" in performance_data
        
        # Step 8: Stop session
        stop_response = client.post(
            f"/api/v1/paper-trading/sessions/{session_id}/stop",
            headers=auth_headers
        )
        
        assert stop_response.status_code == 200


@pytest.mark.e2e
class TestIndicatorCalculationWorkflow:
    """End-to-end test for indicator calculation workflow."""
    
    def test_full_indicator_workflow(self, client: TestClient, auth_headers: dict, sample_ohlcv_data):
        """Test complete indicator calculation workflow."""
        
        # Step 1: Get available indicators
        indicators_response = client.get("/api/v1/indicators/available", headers=auth_headers)
        assert indicators_response.status_code == 200
        indicators = indicators_response.json()["indicators"]
        
        # Step 2: Calculate single indicator
        rsi_payload = {
            "symbol": "NIFTY",
            "indicator": "RSI",
            "parameters": {"period": 14},
            "start_date": "2024-01-01",
            "end_date": "2024-04-10"
        }
        
        rsi_response = client.post(
            "/api/v1/indicators/calculate",
            json=rsi_payload,
            headers=auth_headers
        )
        
        assert rsi_response.status_code == 200
        rsi_data = rsi_response.json()
        assert "values" in rsi_data
        assert "timestamps" in rsi_data
        assert len(rsi_data["values"]) > 0
        
        # Step 3: Calculate multiple indicators
        bulk_payload = {
            "symbol": "NIFTY",
            "indicators": [
                {"name": "RSI", "parameters": {"period": 14}},
                {"name": "SMA", "parameters": {"period": 20}},
                {"name": "EMA", "parameters": {"period": 12}},
                {"name": "MACD", "parameters": {"fast_period": 12, "slow_period": 26, "signal_period": 9}}
            ],
            "start_date": "2024-01-01",
            "end_date": "2024-04-10"
        }
        
        bulk_response = client.post(
            "/api/v1/indicators/bulk-calculate",
            json=bulk_payload,
            headers=auth_headers
        )
        
        assert bulk_response.status_code == 200
        bulk_data = bulk_response.json()
        assert "results" in bulk_data
        assert len(bulk_data["results"]) == 4
        
        # Step 4: Verify each indicator result
        for indicator_name, result in bulk_data["results"].items():
            assert "values" in result
            assert "timestamps" in result
            assert len(result["values"]) > 0
        
        # Step 5: Get indicator signals
        signals_payload = {
            "symbol": "NIFTY",
            "indicators": ["RSI", "SMA"],
            "start_date": "2024-01-01",
            "end_date": "2024-04-10"
        }
        
        signals_response = client.post(
            "/api/v1/indicators/signals",
            json=signals_payload,
            headers=auth_headers
        )
        
        assert signals_response.status_code == 200
        signals_data = signals_response.json()
        assert "signals" in signals_data


@pytest.mark.e2e
class TestDataIngestionWorkflow:
    """End-to-end test for data ingestion workflow."""
    
    def test_full_data_ingestion_workflow(self, client: TestClient, auth_headers: dict):
        """Test complete data ingestion workflow."""
        
        # Step 1: Add a new symbol
        symbol_payload = {
            "symbol": "TESTSTOCK",
            "name": "Test Stock",
            "exchange": "NSE",
            "segment": "EQ",
            "instrument_type": "EQUITY",
            "tick_size": 0.05,
            "lot_size": 1,
            "fyers_symbol": "NSE:TESTSTOCK-EQ"
        }
        
        add_symbol_response = client.post(
            "/api/v1/data-management/symbols",
            json=symbol_payload,
            headers=auth_headers
        )
        
        assert add_symbol_response.status_code == 200
        
        # Step 2: Verify symbol was added
        symbols_response = client.get("/api/v1/market-data/symbols", headers=auth_headers)
        assert symbols_response.status_code == 200
        symbols = [s["symbol"] for s in symbols_response.json()["symbols"]]
        assert "TESTSTOCK" in symbols
        
        # Step 3: Add OHLCV data for the symbol
        ohlcv_payload = {
            "symbol": "TESTSTOCK",
            "data": [
                {
                    "timestamp": "2024-01-01T09:15:00",
                    "open": 100.0,
                    "high": 105.0,
                    "low": 98.0,
                    "close": 103.0,
                    "volume": 1000000
                },
                {
                    "timestamp": "2024-01-01T09:16:00",
                    "open": 103.0,
                    "high": 107.0,
                    "low": 102.0,
                    "close": 106.0,
                    "volume": 1100000
                }
            ]
        }
        
        add_data_response = client.post(
            "/api/v1/data-management/ohlcv",
            json=ohlcv_payload,
            headers=auth_headers
        )
        
        assert add_data_response.status_code == 200
        
        # Step 4: Retrieve the data
        params = {
            "symbol": "TESTSTOCK",
            "start_date": "2024-01-01",
            "end_date": "2024-01-02",
            "timeframe": "1m"
        }
        
        get_data_response = client.get(
            "/api/v1/market-data/ohlcv",
            params=params,
            headers=auth_headers
        )
        
        assert get_data_response.status_code == 200
        data = get_data_response.json()
        assert len(data["data"]) == 2
        
        # Step 5: Get data statistics
        stats_response = client.get(
            f"/api/v1/data-management/symbols/TESTSTOCK/statistics",
            headers=auth_headers
        )
        
        assert stats_response.status_code == 200
        stats = stats_response.json()
        assert stats["total_records"] == 2


@pytest.mark.e2e
@pytest.mark.slow
class TestPerformanceWorkflow:
    """End-to-end performance tests."""
    
    def test_large_dataset_performance(self, client: TestClient, auth_headers: dict):
        """Test performance with large datasets."""
        
        # This test would require a large dataset
        # For now, we'll test with available data
        
        start_time = time.time()
        
        # Get large amount of data
        params = {
            "symbol": "NIFTY",
            "start_date": "2024-01-01",
            "end_date": "2024-04-10",
            "timeframe": "1m"
        }
        
        response = client.get(
            "/api/v1/market-data/ohlcv",
            params=params,
            headers=auth_headers
        )
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        assert response.status_code == 200
        assert elapsed < 5.0  # Should complete within 5 seconds
        
        data = response.json()
        # Performance assertion: should handle reasonable amount of data
        assert len(data["data"]) >= 0  # At least some data
    
    def test_concurrent_requests_performance(self, client: TestClient, auth_headers: dict):
        """Test performance under concurrent requests."""
        import threading
        import time
        
        results = []
        
        def make_request():
            start_time = time.time()
            response = client.get("/api/v1/market-data/symbols", headers=auth_headers)
            end_time = time.time()
            results.append({
                'status_code': response.status_code,
                'elapsed': end_time - start_time
            })
        
        # Create multiple threads
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
        
        # Start all threads
        start_time = time.time()
        for thread in threads:
            thread.start()
        
        # Wait for all threads
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # All requests should succeed
        assert all(result['status_code'] == 200 for result in results)
        
        # Average response time should be reasonable
        avg_response_time = sum(result['elapsed'] for result in results) / len(results)
        assert avg_response_time < 2.0  # Average under 2 seconds
        
        # Total time should be reasonable (concurrent execution)
        assert total_time < 10.0  # Total under 10 seconds
