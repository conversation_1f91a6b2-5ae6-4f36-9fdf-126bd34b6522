"""
Paper trading positions management.
"""

from typing import Dict, Any, Optional
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from app.core.logging import get_logger

logger = get_logger(__name__)


class PositionSide(Enum):
    """Position side."""
    LONG = "long"
    SHORT = "short"


@dataclass
class PaperPosition:
    """Paper trading position."""
    
    id: str
    symbol: str
    side: PositionSide
    quantity: int
    avg_price: float
    current_price: float
    entry_timestamp: datetime
    last_update: datetime
    commission_paid: float = 0.0
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Initialize position after creation."""
        if self.metadata is None:
            self.metadata = {}
        if self.current_price == 0:
            self.current_price = self.avg_price
    
    @property
    def market_value(self) -> float:
        """Get current market value of position."""
        return abs(self.quantity) * self.current_price
    
    @property
    def cost_basis(self) -> float:
        """Get cost basis of position."""
        return abs(self.quantity) * self.avg_price
    
    @property
    def unrealized_pnl(self) -> float:
        """Get unrealized P&L."""
        if self.side == PositionSide.LONG:
            return (self.current_price - self.avg_price) * self.quantity
        else:
            return (self.avg_price - self.current_price) * abs(self.quantity)
    
    @property
    def unrealized_pnl_percent(self) -> float:
        """Get unrealized P&L percentage."""
        if self.cost_basis == 0:
            return 0.0
        return (self.unrealized_pnl / self.cost_basis) * 100
    
    @property
    def is_long(self) -> bool:
        """Check if position is long."""
        return self.side == PositionSide.LONG
    
    @property
    def is_short(self) -> bool:
        """Check if position is short."""
        return self.side == PositionSide.SHORT
    
    def update_price(self, price: float, timestamp: datetime = None) -> None:
        """Update current price."""
        self.current_price = price
        self.last_update = timestamp or datetime.utcnow()
    
    def add_to_position(self, quantity: int, price: float, commission: float = 0.0) -> None:
        """Add to existing position (average down/up)."""
        if self.side == PositionSide.LONG and quantity > 0:
            # Adding to long position
            total_cost = (self.quantity * self.avg_price) + (quantity * price)
            self.quantity += quantity
            self.avg_price = total_cost / self.quantity
            self.commission_paid += commission
        elif self.side == PositionSide.SHORT and quantity < 0:
            # Adding to short position
            total_proceeds = (abs(self.quantity) * self.avg_price) + (abs(quantity) * price)
            self.quantity += quantity  # quantity is negative for short
            self.avg_price = total_proceeds / abs(self.quantity)
            self.commission_paid += commission
        else:
            logger.warning(f"Cannot add {quantity} to {self.side.value} position")
    
    def reduce_position(self, quantity: int, price: float, commission: float = 0.0) -> float:
        """
        Reduce position size and return realized P&L.
        
        Args:
            quantity: Quantity to reduce (positive number)
            price: Exit price
            commission: Commission paid
            
        Returns:
            Realized P&L from the reduction
        """
        if quantity <= 0 or quantity > abs(self.quantity):
            logger.error(f"Invalid quantity {quantity} for position of {self.quantity}")
            return 0.0
        
        # Calculate realized P&L for the portion being closed
        if self.side == PositionSide.LONG:
            realized_pnl = (price - self.avg_price) * quantity - commission
        else:
            realized_pnl = (self.avg_price - price) * quantity - commission
        
        # Update position
        if self.side == PositionSide.LONG:
            self.quantity -= quantity
        else:
            self.quantity += quantity  # quantity is negative for short, so we add to reduce
        
        self.commission_paid += commission
        
        return realized_pnl
    
    def close_position(self, price: float, commission: float = 0.0) -> float:
        """
        Close entire position and return realized P&L.
        
        Args:
            price: Exit price
            commission: Commission paid
            
        Returns:
            Total realized P&L
        """
        return self.reduce_position(abs(self.quantity), price, commission)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert position to dictionary."""
        return {
            'id': self.id,
            'symbol': self.symbol,
            'side': self.side.value,
            'quantity': self.quantity,
            'avg_price': self.avg_price,
            'current_price': self.current_price,
            'market_value': self.market_value,
            'cost_basis': self.cost_basis,
            'unrealized_pnl': self.unrealized_pnl,
            'unrealized_pnl_percent': self.unrealized_pnl_percent,
            'entry_timestamp': self.entry_timestamp.isoformat(),
            'last_update': self.last_update.isoformat(),
            'commission_paid': self.commission_paid,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PaperPosition':
        """Create position from dictionary."""
        return cls(
            id=data['id'],
            symbol=data['symbol'],
            side=PositionSide(data['side']),
            quantity=data['quantity'],
            avg_price=data['avg_price'],
            current_price=data['current_price'],
            entry_timestamp=datetime.fromisoformat(data['entry_timestamp']),
            last_update=datetime.fromisoformat(data['last_update']),
            commission_paid=data.get('commission_paid', 0.0),
            metadata=data.get('metadata', {})
        )
