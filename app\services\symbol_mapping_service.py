"""
Symbol Mapping Service for Fyers API Integration.
Downloads and parses Fyers CSV files to create symbol mappings.
"""

import logging
import pandas as pd
import requests
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import time
from datetime import datetime, timedelta

from app.core.config_loader import get_config

logger = logging.getLogger(__name__)

@dataclass
class SymbolMapping:
    """Symbol mapping data structure."""
    fyers_symbol: str
    db_symbol: str
    name: str
    exchange: str
    segment: str
    instrument_type: str
    lot_size: int
    tick_size: float
    is_index: bool = False

class SymbolMappingService:
    """Service for managing symbol mappings from Fyers CSV files."""
    
    def __init__(self):
        """Initialize the symbol mapping service."""
        config = get_config()
        self.csv_urls = config.general.fyers_api_url
        self.cache_dir = Path("cache/symbol_mappings")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.cache_expiry_hours = 24  # Cache for 24 hours
        
        # NIFTY 50 constituent symbols (as of 2024)
        self.nifty50_symbols = {
            'ADANIENT', 'ADANIPORTS', 'APOLLOHOSP', 'ASIANPAINT', 'AXISBANK',
            'BAJAJ-AUTO', 'BAJFINANCE', 'BAJAJFINSV', 'BPCL', 'BHARTIARTL',
            'BRITANNIA', 'CIPLA', 'COALINDIA', 'DIVISLAB', 'DRREDDY',
            'EICHERMOT', 'GRASIM', 'HCLTECH', 'HDFCBANK', 'HDFCLIFE',
            'HEROMOTOCO', 'HINDALCO', 'HINDUNILVR', 'ICICIBANK', 'ITC',
            'INDUSINDBK', 'INFY', 'JSWSTEEL', 'KOTAKBANK', 'LT',
            'M&M', 'MARUTI', 'NTPC', 'NESTLEIND', 'ONGC',
            'POWERGRID', 'RELIANCE', 'SBILIFE', 'SBIN', 'SUNPHARMA',
            'TCS', 'TATACONSUM', 'TATAMOTORS', 'TATASTEEL', 'TECHM',
            'TITAN', 'ULTRACEMCO', 'UPL', 'WIPRO', 'LTIM'
        }
        
    def download_csv_file(self, url: str, force_refresh: bool = False) -> Optional[Path]:
        """
        Download CSV file from Fyers API with caching.
        
        Args:
            url: URL to download
            force_refresh: Force download even if cached file exists
            
        Returns:
            Path to downloaded file or None if failed
        """
        try:
            # Generate cache filename
            filename = url.split('/')[-1]
            cache_file = self.cache_dir / filename
            
            # Check if cached file exists and is recent
            if not force_refresh and cache_file.exists():
                file_age = datetime.now() - datetime.fromtimestamp(cache_file.stat().st_mtime)
                if file_age < timedelta(hours=self.cache_expiry_hours):
                    logger.info(f"Using cached file: {cache_file}")
                    return cache_file
            
            logger.info(f"Downloading CSV file from: {url}")
            
            # Download with timeout and retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = requests.get(url, timeout=30)
                    response.raise_for_status()
                    
                    # Save to cache
                    with open(cache_file, 'wb') as f:
                        f.write(response.content)
                    
                    logger.info(f"Downloaded and cached: {cache_file}")
                    return cache_file
                    
                except requests.RequestException as e:
                    logger.warning(f"Download attempt {attempt + 1} failed: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)  # Exponential backoff
                    else:
                        logger.error(f"Failed to download after {max_retries} attempts")
                        return None
                        
        except Exception as e:
            logger.error(f"Error downloading CSV file: {e}")
            return None
    
    def parse_csv_file(self, csv_path: Path) -> List[SymbolMapping]:
        """
        Parse Fyers CSV file to extract symbol mappings.

        Args:
            csv_path: Path to CSV file

        Returns:
            List of SymbolMapping objects
        """
        try:
            logger.info(f"Parsing CSV file: {csv_path}")

            # Read CSV file without headers (Fyers CSV format)
            df = pd.read_csv(csv_path, header=None)

            # Define column names based on Fyers CSV format
            # Based on the actual CSV structure we observed
            column_names = [
                'fytoken', 'name', 'instrument_type', 'lot_size', 'tick_size',
                'isin', 'trading_session', 'last_update_date', 'expiry_date',
                'symbol_details', 'exchange_id', 'segment_id', 'symbol_id',
                'symbol_name', 'symbol_id_2', 'strike_price', 'option_type',
                'underlying_fytoken', 'underlying_symbol', 'minimum_lot_size', 'tick_value'
            ]

            # Assign column names (only up to the number of columns we have)
            df.columns = column_names[:len(df.columns)]

            mappings = []

            for _, row in df.iterrows():
                try:
                    # Extract symbol information
                    symbol_details = str(row['symbol_details']) if 'symbol_details' in row else ''
                    instrument_type = str(row['instrument_type']) if 'instrument_type' in row else ''
                    symbol_name = str(row['symbol_name']) if 'symbol_name' in row else ''

                    # Skip if essential fields are missing
                    if not symbol_details or symbol_details == 'nan':
                        continue

                    # Parse symbol details (format: NSE:SYMBOL-EQ or NSE:SYMBOL-INDEX)
                    if ':' in symbol_details and '-' in symbol_details:
                        exchange, symbol_part = symbol_details.split(':', 1)
                        symbol_base, suffix = symbol_part.rsplit('-', 1)

                        # Create database symbol name
                        db_symbol = symbol_base

                        # Determine if it's an index
                        is_index = (suffix == 'INDEX' or
                                  instrument_type == '10' or  # Index instrument type in Fyers
                                  'INDEX' in symbol_details.upper())

                        # Set default values
                        lot_size = 1 if is_index else int(row.get('lot_size', 1))
                        tick_size = float(row.get('tick_size', 0.05))

                        # Special handling for NIFTY indices
                        if 'NIFTY' in symbol_base.upper():
                            if symbol_base.upper() == 'NIFTY50':
                                db_symbol = 'NIFTY'
                            elif symbol_base.upper() == 'NIFTYBANK':
                                db_symbol = 'BANKNIFTY'
                            elif symbol_base.upper() == 'FINNIFTY':
                                db_symbol = 'FINNIFTY'

                        mapping = SymbolMapping(
                            fyers_symbol=symbol_details,
                            db_symbol=db_symbol,
                            name=symbol_base,
                            exchange=exchange,
                            segment=str(row.get('segment_id', '')),
                            instrument_type=str(instrument_type),
                            lot_size=lot_size,
                            tick_size=tick_size,
                            is_index=is_index
                        )

                        mappings.append(mapping)

                except Exception as e:
                    logger.debug(f"Error parsing row: {e}")
                    continue

            logger.info(f"Parsed {len(mappings)} symbol mappings from {csv_path}")
            return mappings

        except Exception as e:
            logger.error(f"Error parsing CSV file: {e}")
            return []
    
    def get_nifty50_mappings(self, force_refresh: bool = False) -> Dict[str, SymbolMapping]:
        """
        Get symbol mappings for NIFTY 50 constituents.
        
        Args:
            force_refresh: Force refresh of cached data
            
        Returns:
            Dictionary mapping DB symbol to SymbolMapping
        """
        try:
            all_mappings = {}
            
            # Download and parse each CSV file
            for url in self.csv_urls:
                csv_path = self.download_csv_file(url, force_refresh)
                if csv_path:
                    mappings = self.parse_csv_file(csv_path)
                    
                    # Filter for NIFTY 50 constituents and indices
                    for mapping in mappings:
                        # Include NIFTY indices
                        if mapping.is_index and 'NIFTY' in mapping.db_symbol.upper():
                            all_mappings[mapping.db_symbol] = mapping
                        
                        # Include NIFTY 50 constituent stocks
                        elif mapping.db_symbol in self.nifty50_symbols:
                            all_mappings[mapping.db_symbol] = mapping
            
            logger.info(f"Found {len(all_mappings)} NIFTY 50 related mappings")
            return all_mappings
            
        except Exception as e:
            logger.error(f"Error getting NIFTY 50 mappings: {e}")
            return {}
    
    def get_index_mappings(self, force_refresh: bool = False) -> Dict[str, SymbolMapping]:
        """
        Get symbol mappings for all indices.
        
        Args:
            force_refresh: Force refresh of cached data
            
        Returns:
            Dictionary mapping DB symbol to SymbolMapping
        """
        try:
            index_mappings = {}
            
            # Download and parse each CSV file
            for url in self.csv_urls:
                csv_path = self.download_csv_file(url, force_refresh)
                if csv_path:
                    mappings = self.parse_csv_file(csv_path)
                    
                    # Filter for indices only
                    for mapping in mappings:
                        if mapping.is_index:
                            index_mappings[mapping.db_symbol] = mapping
            
            logger.info(f"Found {len(index_mappings)} index mappings")
            return index_mappings
            
        except Exception as e:
            logger.error(f"Error getting index mappings: {e}")
            return {}
    
    def get_symbol_mapping(self, db_symbol: str, force_refresh: bool = False) -> Optional[SymbolMapping]:
        """
        Get mapping for a specific symbol.
        
        Args:
            db_symbol: Database symbol name
            force_refresh: Force refresh of cached data
            
        Returns:
            SymbolMapping object or None if not found
        """
        try:
            # First try NIFTY 50 mappings
            nifty_mappings = self.get_nifty50_mappings(force_refresh)
            if db_symbol in nifty_mappings:
                return nifty_mappings[db_symbol]
            
            # Then try index mappings
            index_mappings = self.get_index_mappings(force_refresh)
            if db_symbol in index_mappings:
                return index_mappings[db_symbol]
            
            logger.warning(f"Symbol mapping not found for: {db_symbol}")
            return None
            
        except Exception as e:
            logger.error(f"Error getting symbol mapping for {db_symbol}: {e}")
            return None
