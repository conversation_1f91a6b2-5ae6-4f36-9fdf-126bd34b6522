"""
Base class for technical indicators.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
import pandas as pd
import numpy as np
from datetime import datetime

from app.core.logging import get_logger

logger = get_logger(__name__)


class BaseIndicator(ABC):
    """Base class for all technical indicators."""
    
    def __init__(self, name: str, parameters: Dict[str, Any] = None):
        """
        Initialize indicator.
        
        Args:
            name: Indicator name
            parameters: Indicator parameters
        """
        self.name = name
        self.parameters = parameters or {}
        self.values = []
        self.timestamps = []
        self.is_ready = False
        
    @abstractmethod
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        Calculate indicator values.
        
        Args:
            data: OHLCV data as DataFrame or list of dictionaries
            
        Returns:
            Dictionary with calculated values
        """
        pass
    
    def _prepare_data(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> pd.DataFrame:
        """
        Prepare data for calculation.
        
        Args:
            data: Input data
            
        Returns:
            Prepared DataFrame
        """
        if isinstance(data, list):
            df = pd.DataFrame(data)
        else:
            df = data.copy()
        
        # Ensure required columns exist
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"Missing required column: {col}")
        
        # Convert timestamp to datetime if needed
        if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
            df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Sort by timestamp
        df = df.sort_values('timestamp').reset_index(drop=True)
        
        # Convert price columns to float
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Convert volume to int
        df['volume'] = pd.to_numeric(df['volume'], errors='coerce').fillna(0).astype(int)
        
        return df
    
    def _validate_parameters(self, required_params: List[str]) -> bool:
        """
        Validate required parameters.
        
        Args:
            required_params: List of required parameter names
            
        Returns:
            True if all parameters are present
        """
        for param in required_params:
            if param not in self.parameters:
                logger.error(f"Missing required parameter: {param}")
                return False
        return True
    
    def get_latest_value(self) -> Optional[float]:
        """Get the latest calculated value."""
        return self.values[-1] if self.values else None
    
    def get_values(self, count: Optional[int] = None) -> List[float]:
        """
        Get calculated values.
        
        Args:
            count: Number of latest values to return
            
        Returns:
            List of values
        """
        if count is None:
            return self.values.copy()
        return self.values[-count:] if len(self.values) >= count else self.values.copy()
    
    def get_signals(self, data: pd.DataFrame) -> Dict[str, List[bool]]:
        """
        Generate trading signals based on indicator values.
        
        Args:
            data: OHLCV data
            
        Returns:
            Dictionary with buy/sell signals
        """
        # Default implementation - override in specific indicators
        return {
            'buy_signals': [False] * len(data),
            'sell_signals': [False] * len(data)
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert indicator to dictionary format."""
        return {
            'name': self.name,
            'parameters': self.parameters,
            'values': self.values,
            'timestamps': [ts.isoformat() if isinstance(ts, datetime) else ts for ts in self.timestamps],
            'is_ready': self.is_ready,
            'latest_value': self.get_latest_value()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseIndicator':
        """Create indicator from dictionary format."""
        indicator = cls(data['name'], data['parameters'])
        indicator.values = data['values']
        indicator.timestamps = data['timestamps']
        indicator.is_ready = data['is_ready']
        return indicator
