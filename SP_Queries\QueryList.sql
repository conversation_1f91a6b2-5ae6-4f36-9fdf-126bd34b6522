SELECT * FROM public.stock_ohlcv WHERE datetime='2025-07-14 15:22';

SELECT * FROM public.stock_ohlcv
ORDER BY symbol DESC, "interval" DESC, datetime DESC LIMIT 750


SELECT symbol, COUNT(*) 
FROM stock_ohlcv 
GROUP BY symbol
HAVING COUNT(*) > 1;

SELECT symbol, COUNT(*) 
FROM stock_ohlcv
GROUP BY symbol;

SELECT datetime::TIMESTAMP from stock_ohlcv;
SELECT now() AT TIME ZONE current_setting('TimeZone');
SELECT now() AT TIME ZONE 'UTC';
SELECT LOCALTIMESTAMP;



-- Check gaps in time series (1min expected)
WITH sorted AS (
  SELECT datetime, 
         LAG(datetime) OVER (ORDER BY datetime) AS prev_time
  FROM stock_ohlcv
  WHERE symbol = 'NIFTY' AND interval = '1min'
)
SELECT * 
FROM sorted 
WHERE datetime - prev_time > INTERVAL '1 minute';


DELETE FROM stock_ohlcv;


SELECT * FROM public.stock_ohlcv
ORDER BY datetime DESC, "datetime" DESC LIMIT 10




-- DROPING RELATED
DROP TABLE IF EXISTS public.stock_ohlcv CASCADE;

-- Unblock the given blocked_pid
SELECT pg_terminate_backend(23376);


SELECT 
  blocked.pid AS blocked_pid,
  blocked.query AS blocked_query,
  blocking.pid AS blocking_pid,
  blocking.query AS blocking_query,
  blocking.application_name,
  blocking.state,
  now() - blocking.query_start AS blocking_duration
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_stat_activity blocked ON blocked_locks.pid = blocked.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocked_locks.locktype = blocking_locks.locktype
  AND blocked_locks.database IS NOT DISTINCT FROM blocking_locks.database
  AND blocked_locks.relation IS NOT DISTINCT FROM blocking_locks.relation
  AND blocked_locks.page IS NOT DISTINCT FROM blocking_locks.page
  AND blocked_locks.tuple IS NOT DISTINCT FROM blocking_locks.tuple
  AND blocked_locks.virtualxid IS NOT DISTINCT FROM blocking_locks.virtualxid
  AND blocked_locks.transactionid IS NOT DISTINCT FROM blocking_locks.transactionid
  AND blocked_locks.classid IS NOT DISTINCT FROM blocking_locks.classid
  AND blocked_locks.objid IS NOT DISTINCT FROM blocking_locks.objid
  AND blocked_locks.objsubid IS NOT DISTINCT FROM blocking_locks.objsubid
  AND blocked_locks.pid != blocking_locks.pid
JOIN pg_stat_activity blocking ON blocking_locks.pid = blocking.pid
WHERE NOT blocked_locks.granted;
