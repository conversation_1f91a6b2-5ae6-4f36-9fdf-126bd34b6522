{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Python\\\\signal_stack\\\\frontend\\\\src\\\\components\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Box, Grid, Paper, Typography, AppBar, Toolbar, Container, Card, CardContent, Chip, IconButton, Drawer, List, ListItem, ListItemIcon, ListItemText, Divider } from '@mui/material';\nimport { TrendingUp, TrendingDown, ShowChart, Assessment, Settings, Menu as MenuIcon, Dashboard as DashboardIcon, AccountBalance, Timeline } from '@mui/icons-material';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';\nimport io from 'socket.io-client';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const navigate = useNavigate();\n  const [marketData, setMarketData] = useState([]);\n  const [chartData, setChartData] = useState([]);\n  const [drawerOpen, setDrawerOpen] = useState(false);\n  const [selectedSymbol, setSelectedSymbol] = useState('NIFTY');\n  const [connectionStatus, setConnectionStatus] = useState('Disconnected');\n  useEffect(() => {\n    // Initialize WebSocket connection\n    const socket = io('http://localhost:8000');\n    socket.on('connect', () => {\n      setConnectionStatus('Connected');\n      console.log('Connected to WebSocket server');\n    });\n    socket.on('disconnect', () => {\n      setConnectionStatus('Disconnected');\n      console.log('Disconnected from WebSocket server');\n    });\n    socket.on('market_data', data => {\n      setMarketData(prev => {\n        const updated = prev.filter(item => item.symbol !== data.symbol);\n        return [...updated, data];\n      });\n    });\n    socket.on('ohlcv_data', data => {\n      setChartData(data);\n    });\n\n    // Cleanup on unmount\n    return () => {\n      socket.disconnect();\n    };\n  }, []);\n  const toggleDrawer = () => {\n    setDrawerOpen(!drawerOpen);\n  };\n  const formatPrice = price => {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 2\n    }).format(price);\n  };\n  const formatVolume = volume => {\n    if (volume >= 10000000) {\n      return `${(volume / 10000000).toFixed(1)}Cr`;\n    } else if (volume >= 100000) {\n      return `${(volume / 100000).toFixed(1)}L`;\n    } else if (volume >= 1000) {\n      return `${(volume / 1000).toFixed(1)}K`;\n    }\n    return volume.toString();\n  };\n  const sidebarItems = [{\n    text: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 32\n    }, this),\n    active: true,\n    path: '/dashboard'\n  }, {\n    text: 'Market Data',\n    icon: /*#__PURE__*/_jsxDEV(ShowChart, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 34\n    }, this),\n    path: '/market-data'\n  }, {\n    text: 'Strategies',\n    icon: /*#__PURE__*/_jsxDEV(Assessment, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 33\n    }, this),\n    path: '/strategies'\n  }, {\n    text: 'Backtesting',\n    icon: /*#__PURE__*/_jsxDEV(Timeline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 34\n    }, this),\n    path: '/backtesting'\n  }, {\n    text: 'Paper Trading',\n    icon: /*#__PURE__*/_jsxDEV(AccountBalance, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 36\n    }, this),\n    path: '/paper-trading'\n  }, {\n    text: 'Settings',\n    icon: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 31\n    }, this),\n    path: '/settings'\n  }];\n  const handleNavigation = path => {\n    navigate(path);\n    setDrawerOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      sx: {\n        zIndex: theme => theme.zIndex.drawer + 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"inherit\",\n          \"aria-label\": \"open drawer\",\n          onClick: toggleDrawer,\n          edge: \"start\",\n          sx: {\n            mr: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          noWrap: true,\n          component: \"div\",\n          sx: {\n            flexGrow: 1\n          },\n          children: \"Signal Stack Trading Platform\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: connectionStatus,\n          color: connectionStatus === 'Connected' ? 'success' : 'error',\n          variant: \"outlined\",\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      variant: \"temporary\",\n      open: drawerOpen,\n      onClose: toggleDrawer,\n      sx: {\n        width: 240,\n        flexShrink: 0,\n        '& .MuiDrawer-paper': {\n          width: 240,\n          boxSizing: 'border-box'\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(Toolbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          overflow: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(List, {\n          children: sidebarItems.map((item, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n            button: true,\n            selected: item.active,\n            onClick: () => handleNavigation(item.path),\n            sx: {\n              '&.Mui-selected': {\n                backgroundColor: 'primary.main',\n                color: 'white',\n                '& .MuiListItemIcon-root': {\n                  color: 'white'\n                }\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: item.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this)]\n          }, item.text, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"main\",\n      sx: {\n        flexGrow: 1,\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Toolbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"xl\",\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          gutterBottom: true,\n          children: \"Trading Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          sx: {\n            mb: 3\n          },\n          children: marketData.slice(0, 4).map(data => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  component: \"div\",\n                  children: data.symbol\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"text.primary\",\n                  children: formatPrice(data.price)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mt: 1\n                  },\n                  children: [data.change >= 0 ? /*#__PURE__*/_jsxDEV(TrendingUp, {\n                    color: \"success\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(TrendingDown, {\n                    color: \"error\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: data.change >= 0 ? 'success.main' : 'error.main',\n                    sx: {\n                      ml: 1\n                    },\n                    children: [data.change >= 0 ? '+' : '', data.change.toFixed(2), \" (\", data.changePercent.toFixed(2), \"%)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: [\"Volume: \", formatVolume(data.volume)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this)\n          }, data.symbol, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            lg: 8,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 2,\n                height: 400\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: [selectedSymbol, \" Price Chart\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"90%\",\n                children: /*#__PURE__*/_jsxDEV(LineChart, {\n                  data: chartData,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"timestamp\",\n                    tickFormatter: value => new Date(value).toLocaleTimeString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                    domain: ['dataMin - 10', 'dataMax + 10']\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    labelFormatter: value => new Date(value).toLocaleString(),\n                    formatter: value => [formatPrice(value), 'Price']\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"close\",\n                    stroke: \"#2196f3\",\n                    strokeWidth: 2,\n                    dot: false\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 2,\n                height: 400\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Market Summary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 2\n                },\n                children: marketData.map(data => /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    py: 1,\n                    borderBottom: '1px solid #eee'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: data.symbol\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'right'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: formatPrice(data.price)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 292,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: data.change >= 0 ? 'success.main' : 'error.main',\n                      children: [data.change >= 0 ? '+' : '', data.changePercent.toFixed(2), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 295,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 23\n                  }, this)]\n                }, data.symbol, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"gjm8ldIN/HEawM6I9qEnqb35pww=\", false, function () {\n  return [useNavigate];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Box", "Grid", "Paper", "Typography", "AppBar", "<PERSON><PERSON><PERSON>", "Container", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Chip", "IconButton", "Drawer", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "TrendingUp", "TrendingDown", "ShowChart", "Assessment", "Settings", "<PERSON><PERSON>", "MenuIcon", "Dashboard", "DashboardIcon", "AccountBalance", "Timeline", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "io", "jsxDEV", "_jsxDEV", "_s", "navigate", "marketData", "setMarketData", "chartData", "setChartData", "drawerOpen", "setDrawerOpen", "selectedSymbol", "setSelectedSymbol", "connectionStatus", "setConnectionStatus", "socket", "on", "console", "log", "data", "prev", "updated", "filter", "item", "symbol", "disconnect", "toggle<PERSON>rawer", "formatPrice", "price", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "formatVolume", "volume", "toFixed", "toString", "sidebarItems", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "active", "path", "handleNavigation", "sx", "display", "children", "position", "zIndex", "theme", "drawer", "color", "onClick", "edge", "mr", "variant", "noWrap", "component", "flexGrow", "label", "size", "open", "onClose", "width", "flexShrink", "boxSizing", "overflow", "map", "index", "button", "selected", "backgroundColor", "primary", "p", "max<PERSON><PERSON><PERSON>", "gutterBottom", "container", "spacing", "mb", "slice", "xs", "sm", "md", "alignItems", "mt", "change", "ml", "changePercent", "lg", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "tick<PERSON><PERSON><PERSON><PERSON>", "value", "Date", "toLocaleTimeString", "domain", "labelFormatter", "toLocaleString", "formatter", "type", "stroke", "strokeWidth", "dot", "justifyContent", "py", "borderBottom", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/src/components/Dashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Box,\n  Grid,\n  Paper,\n  Typography,\n  AppBar,\n  Toolbar,\n  Container,\n  Card,\n  CardContent,\n  Chip,\n  IconButton,\n  Drawer,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  TrendingDown,\n  ShowChart,\n  Assessment,\n  Settings,\n  Menu as MenuIcon,\n  Dashboard as DashboardIcon,\n  AccountBalance,\n  Timeline,\n} from '@mui/icons-material';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';\nimport io from 'socket.io-client';\n\ninterface MarketData {\n  symbol: string;\n  price: number;\n  change: number;\n  changePercent: number;\n  volume: number;\n  timestamp: string;\n}\n\ninterface OHLCVData {\n  timestamp: string;\n  open: number;\n  high: number;\n  low: number;\n  close: number;\n  volume: number;\n}\n\nconst Dashboard: React.FC = () => {\n  const navigate = useNavigate();\n  const [marketData, setMarketData] = useState<MarketData[]>([]);\n  const [chartData, setChartData] = useState<OHLCVData[]>([]);\n  const [drawerOpen, setDrawerOpen] = useState(false);\n  const [selectedSymbol, setSelectedSymbol] = useState('NIFTY');\n  const [connectionStatus, setConnectionStatus] = useState('Disconnected');\n\n  useEffect(() => {\n    // Initialize WebSocket connection\n    const socket = io('http://localhost:8000');\n\n    socket.on('connect', () => {\n      setConnectionStatus('Connected');\n      console.log('Connected to WebSocket server');\n    });\n\n    socket.on('disconnect', () => {\n      setConnectionStatus('Disconnected');\n      console.log('Disconnected from WebSocket server');\n    });\n\n    socket.on('market_data', (data: MarketData) => {\n      setMarketData(prev => {\n        const updated = prev.filter(item => item.symbol !== data.symbol);\n        return [...updated, data];\n      });\n    });\n\n    socket.on('ohlcv_data', (data: OHLCVData[]) => {\n      setChartData(data);\n    });\n\n    // Cleanup on unmount\n    return () => {\n      socket.disconnect();\n    };\n  }, []);\n\n  const toggleDrawer = () => {\n    setDrawerOpen(!drawerOpen);\n  };\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 2,\n    }).format(price);\n  };\n\n  const formatVolume = (volume: number) => {\n    if (volume >= 10000000) {\n      return `${(volume / 10000000).toFixed(1)}Cr`;\n    } else if (volume >= 100000) {\n      return `${(volume / 100000).toFixed(1)}L`;\n    } else if (volume >= 1000) {\n      return `${(volume / 1000).toFixed(1)}K`;\n    }\n    return volume.toString();\n  };\n\n  const sidebarItems = [\n    { text: 'Dashboard', icon: <DashboardIcon />, active: true, path: '/dashboard' },\n    { text: 'Market Data', icon: <ShowChart />, path: '/market-data' },\n    { text: 'Strategies', icon: <Assessment />, path: '/strategies' },\n    { text: 'Backtesting', icon: <Timeline />, path: '/backtesting' },\n    { text: 'Paper Trading', icon: <AccountBalance />, path: '/paper-trading' },\n    { text: 'Settings', icon: <Settings />, path: '/settings' },\n  ];\n\n  const handleNavigation = (path: string) => {\n    navigate(path);\n    setDrawerOpen(false);\n  };\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      {/* App Bar */}\n      <AppBar position=\"fixed\" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>\n        <Toolbar>\n          <IconButton\n            color=\"inherit\"\n            aria-label=\"open drawer\"\n            onClick={toggleDrawer}\n            edge=\"start\"\n            sx={{ mr: 2 }}\n          >\n            <MenuIcon />\n          </IconButton>\n          <Typography variant=\"h6\" noWrap component=\"div\" sx={{ flexGrow: 1 }}>\n            Signal Stack Trading Platform\n          </Typography>\n          <Chip\n            label={connectionStatus}\n            color={connectionStatus === 'Connected' ? 'success' : 'error'}\n            variant=\"outlined\"\n            size=\"small\"\n          />\n        </Toolbar>\n      </AppBar>\n\n      {/* Sidebar */}\n      <Drawer\n        variant=\"temporary\"\n        open={drawerOpen}\n        onClose={toggleDrawer}\n        sx={{\n          width: 240,\n          flexShrink: 0,\n          '& .MuiDrawer-paper': {\n            width: 240,\n            boxSizing: 'border-box',\n          },\n        }}\n      >\n        <Toolbar />\n        <Box sx={{ overflow: 'auto' }}>\n          <List>\n            {sidebarItems.map((item, index) => (\n              <ListItem\n                button\n                key={item.text}\n                selected={item.active}\n                onClick={() => handleNavigation(item.path)}\n                sx={{\n                  '&.Mui-selected': {\n                    backgroundColor: 'primary.main',\n                    color: 'white',\n                    '& .MuiListItemIcon-root': {\n                      color: 'white',\n                    },\n                  },\n                }}\n              >\n                <ListItemIcon>{item.icon}</ListItemIcon>\n                <ListItemText primary={item.text} />\n              </ListItem>\n            ))}\n          </List>\n          <Divider />\n        </Box>\n      </Drawer>\n\n      {/* Main Content */}\n      <Box component=\"main\" sx={{ flexGrow: 1, p: 3 }}>\n        <Toolbar />\n        <Container maxWidth=\"xl\">\n          <Typography variant=\"h4\" gutterBottom>\n            Trading Dashboard\n          </Typography>\n\n          {/* Market Overview Cards */}\n          <Grid container spacing={3} sx={{ mb: 3 }}>\n            {marketData.slice(0, 4).map((data) => (\n              <Grid item xs={12} sm={6} md={3} key={data.symbol}>\n                <Card>\n                  <CardContent>\n                    <Typography variant=\"h6\" component=\"div\">\n                      {data.symbol}\n                    </Typography>\n                    <Typography variant=\"h4\" color=\"text.primary\">\n                      {formatPrice(data.price)}\n                    </Typography>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n                      {data.change >= 0 ? (\n                        <TrendingUp color=\"success\" />\n                      ) : (\n                        <TrendingDown color=\"error\" />\n                      )}\n                      <Typography\n                        variant=\"body2\"\n                        color={data.change >= 0 ? 'success.main' : 'error.main'}\n                        sx={{ ml: 1 }}\n                      >\n                        {data.change >= 0 ? '+' : ''}{data.change.toFixed(2)} ({data.changePercent.toFixed(2)}%)\n                      </Typography>\n                    </Box>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Volume: {formatVolume(data.volume)}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            ))}\n          </Grid>\n\n          {/* Chart Section */}\n          <Grid container spacing={3}>\n            <Grid item xs={12} lg={8}>\n              <Paper sx={{ p: 2, height: 400 }}>\n                <Typography variant=\"h6\" gutterBottom>\n                  {selectedSymbol} Price Chart\n                </Typography>\n                <ResponsiveContainer width=\"100%\" height=\"90%\">\n                  <LineChart data={chartData}>\n                    <CartesianGrid strokeDasharray=\"3 3\" />\n                    <XAxis \n                      dataKey=\"timestamp\" \n                      tickFormatter={(value) => new Date(value).toLocaleTimeString()}\n                    />\n                    <YAxis domain={['dataMin - 10', 'dataMax + 10']} />\n                    <Tooltip \n                      labelFormatter={(value) => new Date(value).toLocaleString()}\n                      formatter={(value: number) => [formatPrice(value), 'Price']}\n                    />\n                    <Line \n                      type=\"monotone\" \n                      dataKey=\"close\" \n                      stroke=\"#2196f3\" \n                      strokeWidth={2}\n                      dot={false}\n                    />\n                  </LineChart>\n                </ResponsiveContainer>\n              </Paper>\n            </Grid>\n\n            {/* Market Summary */}\n            <Grid item xs={12} lg={4}>\n              <Paper sx={{ p: 2, height: 400 }}>\n                <Typography variant=\"h6\" gutterBottom>\n                  Market Summary\n                </Typography>\n                <Box sx={{ mt: 2 }}>\n                  {marketData.map((data) => (\n                    <Box\n                      key={data.symbol}\n                      sx={{\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        py: 1,\n                        borderBottom: '1px solid #eee',\n                      }}\n                    >\n                      <Typography variant=\"body2\">{data.symbol}</Typography>\n                      <Box sx={{ textAlign: 'right' }}>\n                        <Typography variant=\"body2\">\n                          {formatPrice(data.price)}\n                        </Typography>\n                        <Typography\n                          variant=\"caption\"\n                          color={data.change >= 0 ? 'success.main' : 'error.main'}\n                        >\n                          {data.change >= 0 ? '+' : ''}{data.changePercent.toFixed(2)}%\n                        </Typography>\n                      </Box>\n                    </Box>\n                  ))}\n                </Box>\n              </Paper>\n            </Grid>\n          </Grid>\n        </Container>\n      </Box>\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,QACF,eAAe;AACtB,SACEC,UAAU,EACVC,YAAY,EACZC,SAAS,EACTC,UAAU,EACVC,QAAQ,EACRC,IAAI,IAAIC,QAAQ,EAChBC,SAAS,IAAIC,aAAa,EAC1BC,cAAc,EACdC,QAAQ,QACH,qBAAqB;AAC5B,SAASC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,mBAAmB,QAAQ,UAAU;AACrG,OAAOC,EAAE,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBlC,MAAMb,SAAmB,GAAGA,CAAA,KAAM;EAAAc,EAAA;EAChC,MAAMC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAe,EAAE,CAAC;EAC9D,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAc,EAAE,CAAC;EAC3D,MAAM,CAAC+C,UAAU,EAAEC,aAAa,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,OAAO,CAAC;EAC7D,MAAM,CAACmD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,cAAc,CAAC;EAExEC,SAAS,CAAC,MAAM;IACd;IACA,MAAMoD,MAAM,GAAGf,EAAE,CAAC,uBAAuB,CAAC;IAE1Ce,MAAM,CAACC,EAAE,CAAC,SAAS,EAAE,MAAM;MACzBF,mBAAmB,CAAC,WAAW,CAAC;MAChCG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC9C,CAAC,CAAC;IAEFH,MAAM,CAACC,EAAE,CAAC,YAAY,EAAE,MAAM;MAC5BF,mBAAmB,CAAC,cAAc,CAAC;MACnCG,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IACnD,CAAC,CAAC;IAEFH,MAAM,CAACC,EAAE,CAAC,aAAa,EAAGG,IAAgB,IAAK;MAC7Cb,aAAa,CAACc,IAAI,IAAI;QACpB,MAAMC,OAAO,GAAGD,IAAI,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAKL,IAAI,CAACK,MAAM,CAAC;QAChE,OAAO,CAAC,GAAGH,OAAO,EAAEF,IAAI,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFJ,MAAM,CAACC,EAAE,CAAC,YAAY,EAAGG,IAAiB,IAAK;MAC7CX,YAAY,CAACW,IAAI,CAAC;IACpB,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM;MACXJ,MAAM,CAACU,UAAU,CAAC,CAAC;IACrB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBhB,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMkB,WAAW,GAAIC,KAAa,IAAK;IACrC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACN,KAAK,CAAC;EAClB,CAAC;EAED,MAAMO,YAAY,GAAIC,MAAc,IAAK;IACvC,IAAIA,MAAM,IAAI,QAAQ,EAAE;MACtB,OAAO,GAAG,CAACA,MAAM,GAAG,QAAQ,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI;IAC9C,CAAC,MAAM,IAAID,MAAM,IAAI,MAAM,EAAE;MAC3B,OAAO,GAAG,CAACA,MAAM,GAAG,MAAM,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG;IAC3C,CAAC,MAAM,IAAID,MAAM,IAAI,IAAI,EAAE;MACzB,OAAO,GAAG,CAACA,MAAM,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG;IACzC;IACA,OAAOD,MAAM,CAACE,QAAQ,CAAC,CAAC;EAC1B,CAAC;EAED,MAAMC,YAAY,GAAG,CACnB;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,eAAEvC,OAAA,CAACZ,aAAa;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAE,IAAI;IAAEC,IAAI,EAAE;EAAa,CAAC,EAChF;IAAEP,IAAI,EAAE,aAAa;IAAEC,IAAI,eAAEvC,OAAA,CAAClB,SAAS;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEE,IAAI,EAAE;EAAe,CAAC,EAClE;IAAEP,IAAI,EAAE,YAAY;IAAEC,IAAI,eAAEvC,OAAA,CAACjB,UAAU;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEE,IAAI,EAAE;EAAc,CAAC,EACjE;IAAEP,IAAI,EAAE,aAAa;IAAEC,IAAI,eAAEvC,OAAA,CAACV,QAAQ;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEE,IAAI,EAAE;EAAe,CAAC,EACjE;IAAEP,IAAI,EAAE,eAAe;IAAEC,IAAI,eAAEvC,OAAA,CAACX,cAAc;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEE,IAAI,EAAE;EAAiB,CAAC,EAC3E;IAAEP,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAEvC,OAAA,CAAChB,QAAQ;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEE,IAAI,EAAE;EAAY,CAAC,CAC5D;EAED,MAAMC,gBAAgB,GAAID,IAAY,IAAK;IACzC3C,QAAQ,CAAC2C,IAAI,CAAC;IACdrC,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,oBACER,OAAA,CAACrC,GAAG;IAACoF,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAE3BjD,OAAA,CAACjC,MAAM;MAACmF,QAAQ,EAAC,OAAO;MAACH,EAAE,EAAE;QAAEI,MAAM,EAAGC,KAAK,IAAKA,KAAK,CAACD,MAAM,CAACE,MAAM,GAAG;MAAE,CAAE;MAAAJ,QAAA,eAC1EjD,OAAA,CAAChC,OAAO;QAAAiF,QAAA,gBACNjD,OAAA,CAAC3B,UAAU;UACTiF,KAAK,EAAC,SAAS;UACf,cAAW,aAAa;UACxBC,OAAO,EAAE/B,YAAa;UACtBgC,IAAI,EAAC,OAAO;UACZT,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,eAEdjD,OAAA,CAACd,QAAQ;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACb3C,OAAA,CAAClC,UAAU;UAAC4F,OAAO,EAAC,IAAI;UAACC,MAAM;UAACC,SAAS,EAAC,KAAK;UAACb,EAAE,EAAE;YAAEc,QAAQ,EAAE;UAAE,CAAE;UAAAZ,QAAA,EAAC;QAErE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3C,OAAA,CAAC5B,IAAI;UACH0F,KAAK,EAAEnD,gBAAiB;UACxB2C,KAAK,EAAE3C,gBAAgB,KAAK,WAAW,GAAG,SAAS,GAAG,OAAQ;UAC9D+C,OAAO,EAAC,UAAU;UAClBK,IAAI,EAAC;QAAO;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGT3C,OAAA,CAAC1B,MAAM;MACLoF,OAAO,EAAC,WAAW;MACnBM,IAAI,EAAEzD,UAAW;MACjB0D,OAAO,EAAEzC,YAAa;MACtBuB,EAAE,EAAE;QACFmB,KAAK,EAAE,GAAG;QACVC,UAAU,EAAE,CAAC;QACb,oBAAoB,EAAE;UACpBD,KAAK,EAAE,GAAG;UACVE,SAAS,EAAE;QACb;MACF,CAAE;MAAAnB,QAAA,gBAEFjD,OAAA,CAAChC,OAAO;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACX3C,OAAA,CAACrC,GAAG;QAACoF,EAAE,EAAE;UAAEsB,QAAQ,EAAE;QAAO,CAAE;QAAApB,QAAA,gBAC5BjD,OAAA,CAACzB,IAAI;UAAA0E,QAAA,EACFZ,YAAY,CAACiC,GAAG,CAAC,CAACjD,IAAI,EAAEkD,KAAK,kBAC5BvE,OAAA,CAACxB,QAAQ;YACPgG,MAAM;YAENC,QAAQ,EAAEpD,IAAI,CAACuB,MAAO;YACtBW,OAAO,EAAEA,CAAA,KAAMT,gBAAgB,CAACzB,IAAI,CAACwB,IAAI,CAAE;YAC3CE,EAAE,EAAE;cACF,gBAAgB,EAAE;gBAChB2B,eAAe,EAAE,cAAc;gBAC/BpB,KAAK,EAAE,OAAO;gBACd,yBAAyB,EAAE;kBACzBA,KAAK,EAAE;gBACT;cACF;YACF,CAAE;YAAAL,QAAA,gBAEFjD,OAAA,CAACvB,YAAY;cAAAwE,QAAA,EAAE5B,IAAI,CAACkB;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACxC3C,OAAA,CAACtB,YAAY;cAACiG,OAAO,EAAEtD,IAAI,CAACiB;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAd/BtB,IAAI,CAACiB,IAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeN,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP3C,OAAA,CAACrB,OAAO;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGT3C,OAAA,CAACrC,GAAG;MAACiG,SAAS,EAAC,MAAM;MAACb,EAAE,EAAE;QAAEc,QAAQ,EAAE,CAAC;QAAEe,CAAC,EAAE;MAAE,CAAE;MAAA3B,QAAA,gBAC9CjD,OAAA,CAAChC,OAAO;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACX3C,OAAA,CAAC/B,SAAS;QAAC4G,QAAQ,EAAC,IAAI;QAAA5B,QAAA,gBACtBjD,OAAA,CAAClC,UAAU;UAAC4F,OAAO,EAAC,IAAI;UAACoB,YAAY;UAAA7B,QAAA,EAAC;QAEtC;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGb3C,OAAA,CAACpC,IAAI;UAACmH,SAAS;UAACC,OAAO,EAAE,CAAE;UAACjC,EAAE,EAAE;YAAEkC,EAAE,EAAE;UAAE,CAAE;UAAAhC,QAAA,EACvC9C,UAAU,CAAC+E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACZ,GAAG,CAAErD,IAAI,iBAC/BjB,OAAA,CAACpC,IAAI;YAACyD,IAAI;YAAC8D,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAApC,QAAA,eAC9BjD,OAAA,CAAC9B,IAAI;cAAA+E,QAAA,eACHjD,OAAA,CAAC7B,WAAW;gBAAA8E,QAAA,gBACVjD,OAAA,CAAClC,UAAU;kBAAC4F,OAAO,EAAC,IAAI;kBAACE,SAAS,EAAC,KAAK;kBAAAX,QAAA,EACrChC,IAAI,CAACK;gBAAM;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACb3C,OAAA,CAAClC,UAAU;kBAAC4F,OAAO,EAAC,IAAI;kBAACJ,KAAK,EAAC,cAAc;kBAAAL,QAAA,EAC1CxB,WAAW,CAACR,IAAI,CAACS,KAAK;gBAAC;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACb3C,OAAA,CAACrC,GAAG;kBAACoF,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEsC,UAAU,EAAE,QAAQ;oBAAEC,EAAE,EAAE;kBAAE,CAAE;kBAAAtC,QAAA,GACvDhC,IAAI,CAACuE,MAAM,IAAI,CAAC,gBACfxF,OAAA,CAACpB,UAAU;oBAAC0E,KAAK,EAAC;kBAAS;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAE9B3C,OAAA,CAACnB,YAAY;oBAACyE,KAAK,EAAC;kBAAO;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAC9B,eACD3C,OAAA,CAAClC,UAAU;oBACT4F,OAAO,EAAC,OAAO;oBACfJ,KAAK,EAAErC,IAAI,CAACuE,MAAM,IAAI,CAAC,GAAG,cAAc,GAAG,YAAa;oBACxDzC,EAAE,EAAE;sBAAE0C,EAAE,EAAE;oBAAE,CAAE;oBAAAxC,QAAA,GAEbhC,IAAI,CAACuE,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEvE,IAAI,CAACuE,MAAM,CAACrD,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAAClB,IAAI,CAACyE,aAAa,CAACvD,OAAO,CAAC,CAAC,CAAC,EAAC,IACxF;kBAAA;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN3C,OAAA,CAAClC,UAAU;kBAAC4F,OAAO,EAAC,SAAS;kBAACJ,KAAK,EAAC,gBAAgB;kBAAAL,QAAA,GAAC,UAC3C,EAAChB,YAAY,CAAChB,IAAI,CAACiB,MAAM,CAAC;gBAAA;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GA3B6B1B,IAAI,CAACK,MAAM;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4B3C,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGP3C,OAAA,CAACpC,IAAI;UAACmH,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA/B,QAAA,gBACzBjD,OAAA,CAACpC,IAAI;YAACyD,IAAI;YAAC8D,EAAE,EAAE,EAAG;YAACQ,EAAE,EAAE,CAAE;YAAA1C,QAAA,eACvBjD,OAAA,CAACnC,KAAK;cAACkF,EAAE,EAAE;gBAAE6B,CAAC,EAAE,CAAC;gBAAEgB,MAAM,EAAE;cAAI,CAAE;cAAA3C,QAAA,gBAC/BjD,OAAA,CAAClC,UAAU;gBAAC4F,OAAO,EAAC,IAAI;gBAACoB,YAAY;gBAAA7B,QAAA,GAClCxC,cAAc,EAAC,cAClB;cAAA;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3C,OAAA,CAACH,mBAAmB;gBAACqE,KAAK,EAAC,MAAM;gBAAC0B,MAAM,EAAC,KAAK;gBAAA3C,QAAA,eAC5CjD,OAAA,CAACT,SAAS;kBAAC0B,IAAI,EAAEZ,SAAU;kBAAA4C,QAAA,gBACzBjD,OAAA,CAACL,aAAa;oBAACkG,eAAe,EAAC;kBAAK;oBAAArD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvC3C,OAAA,CAACP,KAAK;oBACJqG,OAAO,EAAC,WAAW;oBACnBC,aAAa,EAAGC,KAAK,IAAK,IAAIC,IAAI,CAACD,KAAK,CAAC,CAACE,kBAAkB,CAAC;kBAAE;oBAAA1D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACF3C,OAAA,CAACN,KAAK;oBAACyG,MAAM,EAAE,CAAC,cAAc,EAAE,cAAc;kBAAE;oBAAA3D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnD3C,OAAA,CAACJ,OAAO;oBACNwG,cAAc,EAAGJ,KAAK,IAAK,IAAIC,IAAI,CAACD,KAAK,CAAC,CAACK,cAAc,CAAC,CAAE;oBAC5DC,SAAS,EAAGN,KAAa,IAAK,CAACvE,WAAW,CAACuE,KAAK,CAAC,EAAE,OAAO;kBAAE;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACF3C,OAAA,CAACR,IAAI;oBACH+G,IAAI,EAAC,UAAU;oBACfT,OAAO,EAAC,OAAO;oBACfU,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfC,GAAG,EAAE;kBAAM;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGP3C,OAAA,CAACpC,IAAI;YAACyD,IAAI;YAAC8D,EAAE,EAAE,EAAG;YAACQ,EAAE,EAAE,CAAE;YAAA1C,QAAA,eACvBjD,OAAA,CAACnC,KAAK;cAACkF,EAAE,EAAE;gBAAE6B,CAAC,EAAE,CAAC;gBAAEgB,MAAM,EAAE;cAAI,CAAE;cAAA3C,QAAA,gBAC/BjD,OAAA,CAAClC,UAAU;gBAAC4F,OAAO,EAAC,IAAI;gBAACoB,YAAY;gBAAA7B,QAAA,EAAC;cAEtC;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3C,OAAA,CAACrC,GAAG;gBAACoF,EAAE,EAAE;kBAAEwC,EAAE,EAAE;gBAAE,CAAE;gBAAAtC,QAAA,EAChB9C,UAAU,CAACmE,GAAG,CAAErD,IAAI,iBACnBjB,OAAA,CAACrC,GAAG;kBAEFoF,EAAE,EAAE;oBACFC,OAAO,EAAE,MAAM;oBACf2D,cAAc,EAAE,eAAe;oBAC/BrB,UAAU,EAAE,QAAQ;oBACpBsB,EAAE,EAAE,CAAC;oBACLC,YAAY,EAAE;kBAChB,CAAE;kBAAA5D,QAAA,gBAEFjD,OAAA,CAAClC,UAAU;oBAAC4F,OAAO,EAAC,OAAO;oBAAAT,QAAA,EAAEhC,IAAI,CAACK;kBAAM;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,eACtD3C,OAAA,CAACrC,GAAG;oBAACoF,EAAE,EAAE;sBAAE+D,SAAS,EAAE;oBAAQ,CAAE;oBAAA7D,QAAA,gBAC9BjD,OAAA,CAAClC,UAAU;sBAAC4F,OAAO,EAAC,OAAO;sBAAAT,QAAA,EACxBxB,WAAW,CAACR,IAAI,CAACS,KAAK;oBAAC;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC,eACb3C,OAAA,CAAClC,UAAU;sBACT4F,OAAO,EAAC,SAAS;sBACjBJ,KAAK,EAAErC,IAAI,CAACuE,MAAM,IAAI,CAAC,GAAG,cAAc,GAAG,YAAa;sBAAAvC,QAAA,GAEvDhC,IAAI,CAACuE,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEvE,IAAI,CAACyE,aAAa,CAACvD,OAAO,CAAC,CAAC,CAAC,EAAC,GAC9D;oBAAA;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA,GApBD1B,IAAI,CAACK,MAAM;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqBb,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1C,EAAA,CAlQId,SAAmB;EAAA,QACNzB,WAAW;AAAA;AAAAqJ,EAAA,GADxB5H,SAAmB;AAoQzB,eAAeA,SAAS;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}