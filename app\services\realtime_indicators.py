"""
Real-time technical indicator calculation service.
"""

from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from collections import deque
import asyncio
from sqlalchemy.orm import Session

from app.core.logging import get_logger
from app.services.indicator_service import IndicatorService
from app.services.data_service import DataService
from app.database.connection import get_db

logger = get_logger(__name__)


class RealTimeIndicatorEngine:
    """Real-time indicator calculation engine."""
    
    def __init__(self, db: Session):
        """Initialize the real-time indicator engine."""
        self.db = db
        self.data_service = DataService(db)
        self.indicator_service = IndicatorService()
        
        # Store real-time data buffers for each symbol
        self.data_buffers: Dict[str, deque] = {}
        
        # Store active indicators for each symbol
        self.active_indicators: Dict[str, Dict[str, Any]] = {}
        
        # Callbacks for indicator updates
        self.update_callbacks: List[Callable] = []
        
        # Maximum buffer size (keep last 1000 candles)
        self.max_buffer_size = 1000
        
        # Indicator calculation intervals
        self.calculation_intervals = {
            'fast': 1,    # Every tick
            'medium': 5,  # Every 5 ticks
            'slow': 10    # Every 10 ticks
        }
        
        self.tick_count = 0
    
    def add_symbol(self, symbol: str, indicators: List[Dict[str, Any]]) -> bool:
        """
        Add a symbol for real-time indicator calculation.
        
        Args:
            symbol: Symbol to track
            indicators: List of indicator configurations
            
        Returns:
            True if successful
        """
        try:
            # Initialize data buffer
            self.data_buffers[symbol] = deque(maxlen=self.max_buffer_size)
            
            # Load historical data to initialize indicators
            historical_data = self.data_service.get_ohlcv_data(
                symbol=symbol,
                start_time=datetime.now() - timedelta(days=30),
                end_time=datetime.now(),
                as_dataframe=True
            )
            
            if historical_data is not None and not historical_data.empty:
                # Convert to list of dicts for buffer
                for _, row in historical_data.iterrows():
                    self.data_buffers[symbol].append({
                        'timestamp': row.name,
                        'open': float(row['open']),
                        'high': float(row['high']),
                        'low': float(row['low']),
                        'close': float(row['close']),
                        'volume': int(row['volume'])
                    })
            
            # Initialize indicators
            self.active_indicators[symbol] = {}
            for indicator_config in indicators:
                indicator_name = indicator_config['name']
                indicator_params = indicator_config.get('params', {})
                
                indicator = self.indicator_service.create_indicator(
                    indicator_name, **indicator_params
                )
                
                if indicator:
                    self.active_indicators[symbol][indicator_name] = {
                        'indicator': indicator,
                        'config': indicator_config,
                        'last_values': {},
                        'update_frequency': indicator_config.get('frequency', 'medium')
                    }
            
            logger.info(f"Added symbol {symbol} with {len(indicators)} indicators")
            return True
            
        except Exception as e:
            logger.error(f"Error adding symbol {symbol}: {e}")
            return False
    
    def update_data(self, symbol: str, ohlcv_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update real-time data and calculate indicators.
        
        Args:
            symbol: Symbol to update
            ohlcv_data: New OHLCV data point
            
        Returns:
            Updated indicator values
        """
        try:
            if symbol not in self.data_buffers:
                logger.warning(f"Symbol {symbol} not initialized")
                return {}
            
            # Add new data to buffer
            self.data_buffers[symbol].append(ohlcv_data)
            self.tick_count += 1
            
            # Calculate indicators based on update frequency
            updated_indicators = {}
            
            for indicator_name, indicator_info in self.active_indicators[symbol].items():
                frequency = indicator_info['update_frequency']
                
                # Check if we should update this indicator
                should_update = False
                if frequency == 'fast':
                    should_update = True
                elif frequency == 'medium' and self.tick_count % 5 == 0:
                    should_update = True
                elif frequency == 'slow' and self.tick_count % 10 == 0:
                    should_update = True
                
                if should_update:
                    # Calculate indicator
                    indicator = indicator_info['indicator']
                    buffer_data = list(self.data_buffers[symbol])
                    
                    if len(buffer_data) >= indicator.min_periods:
                        result = indicator.calculate(buffer_data)
                        
                        if result and result.get('values'):
                            # Store latest values
                            latest_value = result['values'][-1]
                            latest_timestamp = result['timestamps'][-1]
                            
                            indicator_info['last_values'] = {
                                'value': latest_value,
                                'timestamp': latest_timestamp,
                                'all_values': result['values'][-10:],  # Keep last 10 values
                                'signals': indicator.get_signals(pd.DataFrame(buffer_data)) if hasattr(indicator, 'get_signals') else {}
                            }
                            
                            updated_indicators[indicator_name] = indicator_info['last_values']
            
            # Notify callbacks
            if updated_indicators:
                self._notify_callbacks(symbol, updated_indicators)
            
            return updated_indicators
            
        except Exception as e:
            logger.error(f"Error updating data for {symbol}: {e}")
            return {}
    
    def get_current_values(self, symbol: str) -> Dict[str, Any]:
        """Get current indicator values for a symbol."""
        if symbol not in self.active_indicators:
            return {}
        
        current_values = {}
        for indicator_name, indicator_info in self.active_indicators[symbol].items():
            current_values[indicator_name] = indicator_info['last_values']
        
        return current_values
    
    def add_update_callback(self, callback: Callable) -> None:
        """Add a callback function for indicator updates."""
        self.update_callbacks.append(callback)
    
    def remove_update_callback(self, callback: Callable) -> None:
        """Remove a callback function."""
        if callback in self.update_callbacks:
            self.update_callbacks.remove(callback)
    
    def _notify_callbacks(self, symbol: str, updated_indicators: Dict[str, Any]) -> None:
        """Notify all registered callbacks of indicator updates."""
        for callback in self.update_callbacks:
            try:
                callback(symbol, updated_indicators)
            except Exception as e:
                logger.error(f"Error in callback: {e}")
    
    def get_indicator_summary(self, symbol: str) -> Dict[str, Any]:
        """Get a summary of all indicators for a symbol."""
        if symbol not in self.active_indicators:
            return {}
        
        summary = {
            'symbol': symbol,
            'data_points': len(self.data_buffers.get(symbol, [])),
            'indicators': {},
            'last_update': datetime.now().isoformat()
        }
        
        for indicator_name, indicator_info in self.active_indicators[symbol].items():
            last_values = indicator_info['last_values']
            summary['indicators'][indicator_name] = {
                'current_value': last_values.get('value'),
                'timestamp': last_values.get('timestamp'),
                'config': indicator_info['config'],
                'signals': last_values.get('signals', {})
            }
        
        return summary
    
    def remove_symbol(self, symbol: str) -> bool:
        """Remove a symbol from real-time tracking."""
        try:
            if symbol in self.data_buffers:
                del self.data_buffers[symbol]
            
            if symbol in self.active_indicators:
                del self.active_indicators[symbol]
            
            logger.info(f"Removed symbol {symbol} from real-time tracking")
            return True
            
        except Exception as e:
            logger.error(f"Error removing symbol {symbol}: {e}")
            return False
    
    def get_active_symbols(self) -> List[str]:
        """Get list of actively tracked symbols."""
        return list(self.active_indicators.keys())
    
    def calculate_batch_indicators(self, symbol: str, timeframe: str = '1m') -> Dict[str, Any]:
        """
        Calculate indicators for historical data in batch.
        
        Args:
            symbol: Symbol to calculate for
            timeframe: Data timeframe
            
        Returns:
            Calculated indicator values
        """
        try:
            # Get historical data
            historical_data = self.data_service.get_ohlcv_data(
                symbol=symbol,
                start_time=datetime.now() - timedelta(days=30),
                end_time=datetime.now(),
                as_dataframe=True
            )
            
            if historical_data is None or historical_data.empty:
                return {}
            
            # Calculate all indicators
            results = {}
            
            if symbol in self.active_indicators:
                for indicator_name, indicator_info in self.active_indicators[symbol].items():
                    indicator = indicator_info['indicator']
                    result = indicator.calculate(historical_data)
                    
                    if result and result.get('values'):
                        results[indicator_name] = result
            
            return results
            
        except Exception as e:
            logger.error(f"Error calculating batch indicators for {symbol}: {e}")
            return {}


class IndicatorWebSocketHandler:
    """WebSocket handler for real-time indicator updates."""
    
    def __init__(self, indicator_engine: RealTimeIndicatorEngine):
        """Initialize WebSocket handler."""
        self.indicator_engine = indicator_engine
        self.connected_clients = set()
        
        # Register as callback for indicator updates
        self.indicator_engine.add_update_callback(self.broadcast_update)
    
    async def connect_client(self, websocket) -> None:
        """Connect a new WebSocket client."""
        self.connected_clients.add(websocket)
        logger.info(f"WebSocket client connected. Total clients: {len(self.connected_clients)}")
    
    async def disconnect_client(self, websocket) -> None:
        """Disconnect a WebSocket client."""
        self.connected_clients.discard(websocket)
        logger.info(f"WebSocket client disconnected. Total clients: {len(self.connected_clients)}")
    
    def broadcast_update(self, symbol: str, updated_indicators: Dict[str, Any]) -> None:
        """Broadcast indicator updates to all connected clients."""
        if not self.connected_clients:
            return
        
        message = {
            'type': 'indicator_update',
            'symbol': symbol,
            'indicators': updated_indicators,
            'timestamp': datetime.now().isoformat()
        }
        
        # Schedule broadcast (since this might be called from sync context)
        asyncio.create_task(self._send_to_all_clients(message))
    
    async def _send_to_all_clients(self, message: Dict[str, Any]) -> None:
        """Send message to all connected clients."""
        if not self.connected_clients:
            return
        
        disconnected_clients = set()
        
        for client in self.connected_clients:
            try:
                await client.send_json(message)
            except Exception as e:
                logger.warning(f"Failed to send to client: {e}")
                disconnected_clients.add(client)
        
        # Remove disconnected clients
        for client in disconnected_clients:
            self.connected_clients.discard(client)
