"""
Technical indicators API schemas.
"""

from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field

from app.api.schemas.common import BaseResponse, DateRangeParams


class IndicatorRequest(BaseModel):
    """Indicator calculation request."""
    symbol: str = Field(description="Trading symbol")
    indicator: str = Field(description="Indicator name")
    parameters: Optional[Dict[str, Any]] = Field(default=None, description="Indicator parameters")
    start_date: Optional[datetime] = Field(default=None, description="Start date")
    end_date: Optional[datetime] = Field(default=None, description="End date")
    timeframe: str = Field(default="1m", description="Data timeframe")


class MultipleIndicatorRequest(BaseModel):
    """Multiple indicators calculation request."""
    symbol: str = Field(description="Trading symbol")
    indicators: List[Dict[str, Any]] = Field(description="List of indicator configurations")
    start_date: Optional[datetime] = Field(default=None, description="Start date")
    end_date: Optional[datetime] = Field(default=None, description="End date")
    timeframe: str = Field(default="1m", description="Data timeframe")


class IndicatorResponse(BaseResponse):
    """Indicator calculation response."""
    symbol: str
    indicator: str
    timeframe: str
    parameters: Dict[str, Any]
    data: Dict[str, Any]
    data_points: int


class MultipleIndicatorResponse(BaseResponse):
    """Multiple indicators response."""
    symbol: str
    timeframe: str
    indicators: Dict[str, Dict[str, Any]]
    data_points: int


class IndicatorListResponse(BaseResponse):
    """Available indicators list response."""
    indicators: List[str]


class IndicatorInfoResponse(BaseResponse):
    """Indicator information response."""
    name: str
    class_name: str
    default_parameters: Dict[str, Any]
    description: str


class SignalsRequest(BaseModel):
    """Trading signals request."""
    symbol: str = Field(description="Trading symbol")
    indicator: str = Field(description="Indicator name")
    parameters: Optional[Dict[str, Any]] = Field(default=None, description="Indicator parameters")
    start_date: Optional[datetime] = Field(default=None, description="Start date")
    end_date: Optional[datetime] = Field(default=None, description="End date")
    timeframe: str = Field(default="1m", description="Data timeframe")


class SignalsResponse(BaseResponse):
    """Trading signals response."""
    symbol: str
    indicator: str
    timeframe: str
    buy_signals: List[bool]
    sell_signals: List[bool]
    timestamps: List[datetime]
    signal_count: Dict[str, int]


class IndicatorValidationResponse(BaseResponse):
    """Indicator validation response."""
    is_valid: bool
    errors: List[str] = []
