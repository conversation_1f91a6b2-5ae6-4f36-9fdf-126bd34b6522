"""
Symbol screener API endpoints.
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.dependencies import get_data_service
from app.api.schemas.screener import (
    ScreenerRequest,
    ScreenerResponse,
    ScreenerResult,
    PresetScreenerRequest,
    ScreenerPresetsResponse,
    ScreenerPreset,
    SaveScreenerRequest,
    SaveScreenerResponse,
    ScreenerHistoryResponse
)
from app.services.data_service import DataService
from app.services.screener_service import ScreenerService, ScreenerCriteria
from app.database.connection import get_db
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


def get_screener_service(db: Session = Depends(get_db)) -> ScreenerService:
    """Get screener service instance."""
    return ScreenerService(db)


@router.post("/run", response_model=ScreenerResponse)
async def run_screener(
    request: ScreenerRequest,
    screener_service: ScreenerService = Depends(get_screener_service)
):
    """Run symbol screener with custom criteria."""
    try:
        # Build screener criteria
        criteria = ScreenerCriteria()
        
        # Add price filters
        if request.price_filters:
            criteria.add_price_filter(
                min_price=request.price_filters.min_price,
                max_price=request.price_filters.max_price
            )
        
        # Add volume filters
        if request.volume_filters:
            criteria.add_volume_filter(
                min_volume=request.volume_filters.min_volume,
                avg_volume_days=request.volume_filters.avg_volume_days
            )
        
        # Add technical filters
        if request.technical_filters:
            for tech_filter in request.technical_filters:
                criteria.add_technical_filter(
                    indicator=tech_filter.indicator,
                    condition=tech_filter.condition,
                    value=tech_filter.value,
                    parameters=tech_filter.parameters
                )
        
        # Run screening
        results = screener_service.screen_symbols(
            criteria=criteria,
            market_types=request.market_types,
            symbols=request.symbols,
            timeframe=request.timeframe,
            lookback_days=request.lookback_days
        )
        
        # Convert results to response format
        screener_results = [
            ScreenerResult(**result) for result in results
        ]
        
        # Build criteria summary
        criteria_summary = {
            'price_filters': request.price_filters.dict() if request.price_filters else None,
            'volume_filters': request.volume_filters.dict() if request.volume_filters else None,
            'technical_filters_count': len(request.technical_filters) if request.technical_filters else 0,
            'market_types': request.market_types,
            'timeframe': request.timeframe,
            'lookback_days': request.lookback_days
        }
        
        return ScreenerResponse(
            results=screener_results,
            total_screened=len(screener_results) + sum(1 for r in results if r.get('failed_filters')),
            total_passed=len(screener_results),
            criteria_summary=criteria_summary
        )
        
    except Exception as e:
        logger.error(f"Error running screener: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to run symbol screener"
        )


@router.get("/presets", response_model=ScreenerPresetsResponse)
async def get_screener_presets():
    """Get available screener presets."""
    try:
        # Define common screener presets
        presets = [
            ScreenerPreset(
                name="momentum_breakout",
                description="Stocks breaking out with high momentum",
                criteria={
                    "price_filters": {"min_price": 10.0},
                    "volume_filters": {"min_volume": 100000},
                    "technical_filters": [
                        {"indicator": "RSI", "condition": "above", "value": 60},
                        {"indicator": "SMA", "condition": "above", "value": "current_price", "parameters": {"period": 20}}
                    ]
                }
            ),
            ScreenerPreset(
                name="oversold_bounce",
                description="Oversold stocks ready for bounce",
                criteria={
                    "price_filters": {"min_price": 5.0},
                    "volume_filters": {"min_volume": 50000},
                    "technical_filters": [
                        {"indicator": "RSI", "condition": "below", "value": 30},
                        {"indicator": "BollingerBands", "condition": "below", "value": "lower_band"}
                    ]
                }
            ),
            ScreenerPreset(
                name="high_volume_breakout",
                description="High volume breakout candidates",
                criteria={
                    "volume_filters": {"min_volume": 500000},
                    "technical_filters": [
                        {"indicator": "ATR", "condition": "above", "value": 2.0},
                        {"indicator": "MACD", "condition": "crossover_above", "value": 0}
                    ]
                }
            ),
            ScreenerPreset(
                name="trend_following",
                description="Strong trending stocks",
                criteria={
                    "price_filters": {"min_price": 20.0},
                    "technical_filters": [
                        {"indicator": "ADX", "condition": "above", "value": 25},
                        {"indicator": "EMA", "condition": "above", "value": "current_price", "parameters": {"period": 50}}
                    ]
                }
            )
        ]
        
        return ScreenerPresetsResponse(presets=presets)
        
    except Exception as e:
        logger.error(f"Error getting screener presets: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve screener presets"
        )


@router.post("/run-preset", response_model=ScreenerResponse)
async def run_preset_screener(
    request: PresetScreenerRequest,
    screener_service: ScreenerService = Depends(get_screener_service)
):
    """Run a preset screener configuration."""
    try:
        # Get preset configuration
        presets_response = await get_screener_presets()
        preset_config = None
        
        for preset in presets_response.presets:
            if preset.name == request.preset_name:
                preset_config = preset.criteria
                break
        
        if not preset_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Preset '{request.preset_name}' not found"
            )
        
        # Build screener criteria from preset
        criteria = ScreenerCriteria()
        
        # Add price filters
        if 'price_filters' in preset_config:
            pf = preset_config['price_filters']
            criteria.add_price_filter(
                min_price=pf.get('min_price'),
                max_price=pf.get('max_price')
            )
        
        # Add volume filters
        if 'volume_filters' in preset_config:
            vf = preset_config['volume_filters']
            criteria.add_volume_filter(
                min_volume=vf.get('min_volume'),
                avg_volume_days=vf.get('avg_volume_days', 20)
            )
        
        # Add technical filters
        if 'technical_filters' in preset_config:
            for tf in preset_config['technical_filters']:
                criteria.add_technical_filter(
                    indicator=tf['indicator'],
                    condition=tf['condition'],
                    value=tf['value'],
                    parameters=tf.get('parameters', {})
                )
        
        # Run screening
        results = screener_service.screen_symbols(
            criteria=criteria,
            market_types=request.market_types,
            timeframe=request.timeframe,
            lookback_days=request.lookback_days
        )
        
        # Convert results to response format
        screener_results = [
            ScreenerResult(**result) for result in results
        ]
        
        # Build criteria summary
        criteria_summary = {
            'preset_name': request.preset_name,
            'market_types': request.market_types,
            'timeframe': request.timeframe,
            'lookback_days': request.lookback_days,
            'preset_criteria': preset_config
        }
        
        return ScreenerResponse(
            results=screener_results,
            total_screened=len(screener_results) + sum(1 for r in results if r.get('failed_filters')),
            total_passed=len(screener_results),
            criteria_summary=criteria_summary
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error running preset screener: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to run preset screener"
        )


@router.post("/save", response_model=SaveScreenerResponse)
async def save_screener_results(
    request: SaveScreenerRequest,
    screener_service: ScreenerService = Depends(get_screener_service)
):
    """Save screener results to database."""
    try:
        # Convert ScreenerResult objects to dictionaries
        results_data = [result.dict() for result in request.results]
        
        success = screener_service.save_screener_results(
            strategy_id=request.strategy_id,
            results=results_data,
            criteria_description=request.criteria_description
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to save screener results"
            )
        
        return SaveScreenerResponse(
            saved_count=len(request.results),
            strategy_id=request.strategy_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error saving screener results: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to save screener results"
        )


@router.get("/history/{strategy_id}", response_model=ScreenerHistoryResponse)
async def get_screener_history(
    strategy_id: int,
    limit: int = 10,
    screener_service: ScreenerService = Depends(get_screener_service)
):
    """Get screener history for a strategy."""
    try:
        # This would typically query the database for historical screener results
        # For now, return a placeholder response
        history = [
            {
                "run_id": 1,
                "timestamp": "2025-07-12T10:00:00Z",
                "symbols_screened": 500,
                "symbols_passed": 25,
                "criteria_description": "Momentum breakout strategy"
            }
        ]
        
        return ScreenerHistoryResponse(
            history=history,
            total_runs=len(history)
        )
        
    except Exception as e:
        logger.error(f"Error getting screener history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve screener history"
        )
