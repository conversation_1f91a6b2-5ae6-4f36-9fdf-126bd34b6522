"""
Paper trading engine for live strategy execution.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor

from .portfolio import PaperPortfolio
from app.services.market_data_service import MarketDataService
from app.services.backtesting.strategy import BaseStrategy, TradingSignal, SignalType
from app.core.logging import get_logger

logger = get_logger(__name__)


class PaperTradingEngine:
    """Paper trading engine for live strategy execution."""
    
    def __init__(
        self,
        portfolio: PaperPortfolio,
        market_data_service: MarketDataService,
        update_interval: int = 60  # seconds
    ):
        """
        Initialize paper trading engine.
        
        Args:
            portfolio: Paper trading portfolio
            market_data_service: Market data service
            update_interval: Price update interval in seconds
        """
        self.portfolio = portfolio
        self.market_data_service = market_data_service
        self.update_interval = update_interval
        
        # Engine state
        self.is_running = False
        self.strategies: Dict[str, BaseStrategy] = {}
        self.watched_symbols: set = set()
        
        # Threading
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.update_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        
        # Performance tracking
        self.last_update = None
        self.update_count = 0
        self.error_count = 0
    
    def add_strategy(self, strategy: BaseStrategy, symbols: List[str]) -> None:
        """
        Add a strategy to the engine.
        
        Args:
            strategy: Trading strategy
            symbols: Symbols to watch for this strategy
        """
        strategy_id = f"{strategy.name}_{len(self.strategies)}"
        self.strategies[strategy_id] = strategy
        self.watched_symbols.update(symbols)
        
        logger.info(f"Added strategy: {strategy.name} watching {symbols}")
    
    def remove_strategy(self, strategy_id: str) -> bool:
        """Remove a strategy from the engine."""
        if strategy_id in self.strategies:
            del self.strategies[strategy_id]
            logger.info(f"Removed strategy: {strategy_id}")
            return True
        return False
    
    def start(self) -> None:
        """Start the paper trading engine."""
        if self.is_running:
            logger.warning("Paper trading engine is already running")
            return
        
        if not self.strategies:
            logger.error("No strategies added to engine")
            return
        
        self.is_running = True
        self.stop_event.clear()
        
        # Start update thread
        self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
        self.update_thread.start()
        
        logger.info(f"Paper trading engine started with {len(self.strategies)} strategies")
    
    def stop(self) -> None:
        """Stop the paper trading engine."""
        if not self.is_running:
            return
        
        self.is_running = False
        self.stop_event.set()
        
        if self.update_thread:
            self.update_thread.join(timeout=10)
        
        logger.info("Paper trading engine stopped")
    
    def _update_loop(self) -> None:
        """Main update loop running in separate thread."""
        while self.is_running and not self.stop_event.is_set():
            try:
                self._update_cycle()
                self.update_count += 1
                self.last_update = datetime.utcnow()
                
            except Exception as e:
                self.error_count += 1
                logger.error(f"Error in update cycle: {e}")
            
            # Wait for next update
            self.stop_event.wait(self.update_interval)
    
    def _update_cycle(self) -> None:
        """Single update cycle."""
        # Get current prices for watched symbols
        current_prices = self._get_current_prices()
        
        if not current_prices:
            logger.warning("No price data available")
            return
        
        # Update portfolio with current prices
        self.portfolio.update_prices(current_prices)
        
        # Process strategies
        for strategy_id, strategy in self.strategies.items():
            try:
                self._process_strategy(strategy, current_prices)
            except Exception as e:
                logger.error(f"Error processing strategy {strategy_id}: {e}")
    
    def _get_current_prices(self) -> Dict[str, float]:
        """Get current prices for watched symbols."""
        prices = {}
        
        try:
            for symbol in self.watched_symbols:
                quote = self.market_data_service.get_live_quote(symbol)
                if quote:
                    prices[symbol] = quote.ltp
                else:
                    logger.warning(f"No quote available for {symbol}")
            
        except Exception as e:
            logger.error(f"Error getting current prices: {e}")
        
        return prices
    
    def _process_strategy(self, strategy: BaseStrategy, current_prices: Dict[str, float]) -> None:
        """Process a single strategy."""
        # For live trading, we need to simulate the bar-by-bar approach
        # This is a simplified version - in practice, you'd maintain historical data
        
        # Create a simple current bar from current prices
        current_time = datetime.utcnow()
        
        for symbol in self.watched_symbols:
            if symbol not in current_prices:
                continue
            
            # Generate signals (this would need historical data in practice)
            # For now, we'll skip signal generation and focus on order management
            
            # Update strategy positions with current prices
            strategy.update_positions(symbol, current_prices[symbol])
    
    def place_order(
        self,
        symbol: str,
        side: str,
        order_type: str,
        quantity: int,
        price: Optional[float] = None,
        strategy_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Place an order through the engine.
        
        Args:
            symbol: Symbol to trade
            side: Order side (buy/sell)
            order_type: Order type (market/limit/stop)
            quantity: Order quantity
            price: Order price (for limit orders)
            strategy_id: Strategy ID placing the order
            
        Returns:
            Order ID if successful, None otherwise
        """
        try:
            order = self.portfolio.place_order(
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=quantity,
                price=price
            )
            
            if order:
                # Add strategy metadata
                if strategy_id:
                    order.metadata['strategy_id'] = strategy_id
                
                logger.info(f"Placed order: {order.id} - {side} {quantity} {symbol}")
                return order.id
            
        except Exception as e:
            logger.error(f"Error placing order: {e}")
        
        return None
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order."""
        return self.portfolio.cancel_order(order_id)
    
    def close_position(self, symbol: str, quantity: Optional[int] = None) -> bool:
        """Close a position."""
        return self.portfolio.close_position(symbol, quantity)
    
    def get_portfolio_status(self) -> Dict[str, Any]:
        """Get current portfolio status."""
        return self.portfolio.to_dict()
    
    def get_engine_status(self) -> Dict[str, Any]:
        """Get engine status."""
        return {
            'is_running': self.is_running,
            'strategies_count': len(self.strategies),
            'watched_symbols': list(self.watched_symbols),
            'update_interval': self.update_interval,
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'update_count': self.update_count,
            'error_count': self.error_count,
            'portfolio_value': self.portfolio.total_value,
            'open_positions': len(self.portfolio.positions),
            'pending_orders': len(self.portfolio.order_manager.get_pending_orders())
        }
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report."""
        portfolio_performance = self.portfolio.get_performance_summary()
        engine_status = self.get_engine_status()
        
        # Strategy performance
        strategy_performance = {}
        for strategy_id, strategy in self.strategies.items():
            strategy_performance[strategy_id] = strategy.get_performance_summary()
        
        return {
            'engine_status': engine_status,
            'portfolio_performance': portfolio_performance,
            'strategy_performance': strategy_performance,
            'recent_trades': [trade.__dict__ for trade in self.portfolio.trades[-10:]],
            'value_history': self.portfolio.value_history[-100:]  # Last 100 updates
        }
    

    
    def reset_portfolio(self, initial_cash: float = None) -> None:
        """Reset portfolio to initial state."""
        if initial_cash is None:
            initial_cash = self.portfolio.initial_cash
        
        # Stop engine if running
        was_running = self.is_running
        if was_running:
            self.stop()
        
        # Reset portfolio
        self.portfolio = PaperPortfolio(initial_cash, self.portfolio.commission_rate)
        
        # Reset strategies
        for strategy in self.strategies.values():
            strategy.positions = []
            strategy.trades = []
            strategy.signals = []
            strategy.is_initialized = False
        
        # Restart if it was running
        if was_running:
            self.start()
        
        logger.info(f"Portfolio reset with {initial_cash} initial cash")
    
    def __enter__(self):
        """Context manager entry."""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop()
