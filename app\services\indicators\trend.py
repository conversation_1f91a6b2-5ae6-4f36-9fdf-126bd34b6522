"""
Trend-following technical indicators.
"""

from typing import Dict, Any, Union, List
import pandas as pd
import numpy as np

from .base import BaseIndicator


class SMA(BaseIndicator):
    """Simple Moving Average indicator."""
    
    def __init__(self, period: int = 20):
        """
        Initialize SMA.
        
        Args:
            period: Moving average period
        """
        super().__init__("SMA", {"period": period})
        self.period = period
    
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calculate SMA values."""
        df = self._prepare_data(data)
        
        if len(df) < self.period:
            return {"values": [], "timestamps": []}
        
        # Calculate SMA
        sma_values = df['close'].rolling(window=self.period).mean()
        
        # Store values
        self.values = sma_values.dropna().tolist()
        self.timestamps = df['timestamp'][self.period-1:].tolist()
        self.is_ready = len(self.values) > 0
        
        return {
            "values": self.values,
            "timestamps": self.timestamps,
            "period": self.period
        }


class EMA(BaseIndicator):
    """Exponential Moving Average indicator."""
    
    def __init__(self, period: int = 20):
        """
        Initialize EMA.
        
        Args:
            period: Moving average period
        """
        super().__init__("EMA", {"period": period})
        self.period = period
    
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calculate EMA values."""
        df = self._prepare_data(data)
        
        if len(df) < self.period:
            return {"values": [], "timestamps": []}
        
        # Calculate EMA
        ema_values = df['close'].ewm(span=self.period).mean()
        
        # Store values
        self.values = ema_values.tolist()
        self.timestamps = df['timestamp'].tolist()
        self.is_ready = len(self.values) > 0
        
        return {
            "values": self.values,
            "timestamps": self.timestamps,
            "period": self.period
        }


class MACD(BaseIndicator):
    """MACD (Moving Average Convergence Divergence) indicator."""
    
    def __init__(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9):
        """
        Initialize MACD.
        
        Args:
            fast_period: Fast EMA period
            slow_period: Slow EMA period
            signal_period: Signal line EMA period
        """
        super().__init__("MACD", {
            "fast_period": fast_period,
            "slow_period": slow_period,
            "signal_period": signal_period
        })
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
    
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calculate MACD values."""
        df = self._prepare_data(data)
        
        if len(df) < self.slow_period:
            return {"macd": [], "signal": [], "histogram": [], "timestamps": []}
        
        # Calculate EMAs
        fast_ema = df['close'].ewm(span=self.fast_period).mean()
        slow_ema = df['close'].ewm(span=self.slow_period).mean()
        
        # Calculate MACD line
        macd_line = fast_ema - slow_ema
        
        # Calculate signal line
        signal_line = macd_line.ewm(span=self.signal_period).mean()
        
        # Calculate histogram
        histogram = macd_line - signal_line
        
        # Store values
        self.values = {
            'macd': macd_line.tolist(),
            'signal': signal_line.tolist(),
            'histogram': histogram.tolist()
        }
        self.timestamps = df['timestamp'].tolist()
        self.is_ready = len(macd_line) > 0
        
        return {
            "macd": self.values['macd'],
            "signal": self.values['signal'],
            "histogram": self.values['histogram'],
            "timestamps": self.timestamps,
            "fast_period": self.fast_period,
            "slow_period": self.slow_period,
            "signal_period": self.signal_period
        }
    
    def get_signals(self, data: pd.DataFrame) -> Dict[str, List[bool]]:
        """Generate MACD trading signals."""
        result = self.calculate(data)
        
        if not result['macd']:
            return {'buy_signals': [], 'sell_signals': []}
        
        macd = np.array(result['macd'])
        signal = np.array(result['signal'])
        
        # Buy when MACD crosses above signal line
        buy_signals = []
        sell_signals = []
        
        for i in range(1, len(macd)):
            if macd[i] > signal[i] and macd[i-1] <= signal[i-1]:
                buy_signals.append(True)
                sell_signals.append(False)
            elif macd[i] < signal[i] and macd[i-1] >= signal[i-1]:
                buy_signals.append(False)
                sell_signals.append(True)
            else:
                buy_signals.append(False)
                sell_signals.append(False)
        
        # Add False for first element
        buy_signals.insert(0, False)
        sell_signals.insert(0, False)
        
        return {
            'buy_signals': buy_signals,
            'sell_signals': sell_signals
        }


class ADX(BaseIndicator):
    """Average Directional Index indicator."""
    
    def __init__(self, period: int = 14):
        """
        Initialize ADX.
        
        Args:
            period: ADX calculation period
        """
        super().__init__("ADX", {"period": period})
        self.period = period
    
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calculate ADX values."""
        df = self._prepare_data(data)
        
        if len(df) < self.period * 2:
            return {"adx": [], "di_plus": [], "di_minus": [], "timestamps": []}
        
        # Calculate True Range
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift(1))
        low_close = np.abs(df['low'] - df['close'].shift(1))
        tr = np.maximum(high_low, np.maximum(high_close, low_close))
        
        # Calculate Directional Movement
        plus_dm = np.where((df['high'] - df['high'].shift(1)) > (df['low'].shift(1) - df['low']),
                          np.maximum(df['high'] - df['high'].shift(1), 0), 0)
        minus_dm = np.where((df['low'].shift(1) - df['low']) > (df['high'] - df['high'].shift(1)),
                           np.maximum(df['low'].shift(1) - df['low'], 0), 0)
        
        # Calculate smoothed values
        atr = pd.Series(tr).rolling(window=self.period).mean()
        plus_di = 100 * (pd.Series(plus_dm).rolling(window=self.period).mean() / atr)
        minus_di = 100 * (pd.Series(minus_dm).rolling(window=self.period).mean() / atr)
        
        # Calculate ADX
        dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(window=self.period).mean()
        
        # Store values (ensure all arrays have same length)
        valid_length = len(adx.dropna())
        self.values = {
            'adx': adx.dropna().tolist(),
            'di_plus': plus_di.iloc[-valid_length:].tolist() if valid_length > 0 else [],
            'di_minus': minus_di.iloc[-valid_length:].tolist() if valid_length > 0 else []
        }

        # Adjust timestamps for the dropped NaN values
        start_idx = len(df) - valid_length if valid_length > 0 else len(df)
        self.timestamps = df['timestamp'][start_idx:].tolist()
        self.is_ready = len(self.values['adx']) > 0

        return {
            "adx": self.values['adx'],
            "di_plus": self.values['di_plus'],
            "di_minus": self.values['di_minus'],
            "timestamps": self.timestamps,
            "period": self.period
        }
