"""
Paper trading orders management.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import uuid

from app.core.logging import get_logger

logger = get_logger(__name__)


class PaperOrderType(Enum):
    """Paper order types."""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class PaperOrderSide(Enum):
    """Paper order side."""
    BUY = "buy"
    SELL = "sell"


class PaperOrderStatus(Enum):
    """Paper order status."""
    PENDING = "pending"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"


# Aliases for compatibility
OrderType = PaperOrderType
OrderSide = PaperOrderSide
OrderStatus = PaperOrderStatus


@dataclass
class PaperOrder:
    """Paper trading order."""
    
    id: str
    symbol: str
    side: PaperOrderSide
    order_type: PaperOrderType
    quantity: int
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: str = "GTC"  # Good Till Cancelled
    status: PaperOrderStatus = PaperOrderStatus.PENDING
    filled_quantity: int = 0
    avg_fill_price: float = 0.0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    filled_at: Optional[datetime] = None
    commission: float = 0.0
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Initialize order after creation."""
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = self.created_at
        if self.metadata is None:
            self.metadata = {}
    
    @property
    def is_buy(self) -> bool:
        """Check if order is a buy order."""
        return self.side == PaperOrderSide.BUY
    
    @property
    def is_sell(self) -> bool:
        """Check if order is a sell order."""
        return self.side == PaperOrderSide.SELL
    
    @property
    def is_filled(self) -> bool:
        """Check if order is completely filled."""
        return self.status == PaperOrderStatus.FILLED
    
    @property
    def is_pending(self) -> bool:
        """Check if order is pending."""
        return self.status == PaperOrderStatus.PENDING
    
    @property
    def remaining_quantity(self) -> int:
        """Get remaining quantity to fill."""
        return self.quantity - self.filled_quantity
    
    @property
    def fill_percentage(self) -> float:
        """Get fill percentage."""
        if self.quantity == 0:
            return 0.0
        return (self.filled_quantity / self.quantity) * 100
    
    @property
    def total_value(self) -> float:
        """Get total order value."""
        if self.filled_quantity > 0 and self.avg_fill_price > 0:
            return self.filled_quantity * self.avg_fill_price
        elif self.price:
            return self.quantity * self.price
        return 0.0
    
    def fill(self, quantity: int, price: float, timestamp: datetime = None, commission: float = 0.0) -> bool:
        """
        Fill order partially or completely.
        
        Args:
            quantity: Quantity to fill
            price: Fill price
            timestamp: Fill timestamp
            commission: Commission for this fill
            
        Returns:
            True if order is completely filled
        """
        if self.status not in [PaperOrderStatus.PENDING, PaperOrderStatus.PARTIALLY_FILLED]:
            return False
        
        if quantity <= 0 or quantity > self.remaining_quantity:
            return False
        
        # Update filled quantities and average price
        if self.filled_quantity == 0:
            # First fill
            self.avg_fill_price = price
            self.filled_at = timestamp or datetime.utcnow()
        else:
            # Subsequent fills - calculate weighted average price
            total_value = (self.filled_quantity * self.avg_fill_price) + (quantity * price)
            self.filled_quantity += quantity
            self.avg_fill_price = total_value / self.filled_quantity
            self.filled_quantity -= quantity  # Temporarily subtract to recalculate
        
        self.filled_quantity += quantity
        self.commission += commission
        self.updated_at = timestamp or datetime.utcnow()
        
        # Update status
        if self.filled_quantity >= self.quantity:
            self.status = PaperOrderStatus.FILLED
            return True
        else:
            self.status = PaperOrderStatus.PARTIALLY_FILLED
            return False
    
    def cancel(self, timestamp: datetime = None) -> bool:
        """Cancel order."""
        if self.status in [PaperOrderStatus.PENDING, PaperOrderStatus.PARTIALLY_FILLED]:
            self.status = PaperOrderStatus.CANCELLED
            self.updated_at = timestamp or datetime.utcnow()
            return True
        return False
    
    def reject(self, reason: str = None, timestamp: datetime = None) -> bool:
        """Reject order."""
        if self.status == PaperOrderStatus.PENDING:
            self.status = PaperOrderStatus.REJECTED
            self.updated_at = timestamp or datetime.utcnow()
            if reason:
                self.metadata['rejection_reason'] = reason
            return True
        return False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert order to dictionary."""
        return {
            'id': self.id,
            'symbol': self.symbol,
            'side': self.side.value,
            'order_type': self.order_type.value,
            'quantity': self.quantity,
            'price': self.price,
            'stop_price': self.stop_price,
            'time_in_force': self.time_in_force,
            'status': self.status.value,
            'filled_quantity': self.filled_quantity,
            'avg_fill_price': self.avg_fill_price,
            'remaining_quantity': self.remaining_quantity,
            'fill_percentage': self.fill_percentage,
            'total_value': self.total_value,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'filled_at': self.filled_at.isoformat() if self.filled_at else None,
            'commission': self.commission,
            'metadata': self.metadata
        }


class PaperOrderManager:
    """Manages paper trading orders."""
    
    def __init__(self, commission_rate: float = 0.001):
        """
        Initialize order manager.
        
        Args:
            commission_rate: Commission rate (as decimal)
        """
        self.commission_rate = commission_rate
        self.orders: Dict[str, PaperOrder] = {}
        self.order_history: List[PaperOrder] = []
    
    def create_order(
        self,
        symbol: str,
        side: PaperOrderSide,
        order_type: PaperOrderType,
        quantity: int,
        price: Optional[float] = None,
        stop_price: Optional[float] = None,
        time_in_force: str = "GTC"
    ) -> PaperOrder:
        """Create a new paper order."""
        order_id = str(uuid.uuid4())
        
        order = PaperOrder(
            id=order_id,
            symbol=symbol,
            side=side,
            order_type=order_type,
            quantity=quantity,
            price=price,
            stop_price=stop_price,
            time_in_force=time_in_force
        )
        
        self.orders[order_id] = order
        logger.info(f"Created paper order: {order_id} - {side.value} {quantity} {symbol}")
        
        return order
    
    def get_order(self, order_id: str) -> Optional[PaperOrder]:
        """Get order by ID."""
        return self.orders.get(order_id)
    
    def get_pending_orders(self, symbol: str = None) -> List[PaperOrder]:
        """Get pending orders."""
        pending = [order for order in self.orders.values() 
                  if order.status in [PaperOrderStatus.PENDING, PaperOrderStatus.PARTIALLY_FILLED]]
        
        if symbol:
            pending = [order for order in pending if order.symbol == symbol]
        
        return pending
    
    def get_filled_orders(self, symbol: str = None) -> List[PaperOrder]:
        """Get filled orders."""
        filled = [order for order in self.orders.values() if order.is_filled]
        
        if symbol:
            filled = [order for order in filled if order.symbol == symbol]
        
        return filled
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order."""
        order = self.get_order(order_id)
        if order:
            success = order.cancel()
            if success:
                logger.info(f"Cancelled paper order: {order_id}")
            return success
        return False
    
    def cancel_all_orders(self, symbol: str = None) -> int:
        """Cancel all pending orders."""
        cancelled_count = 0
        
        for order in self.get_pending_orders(symbol):
            if order.cancel():
                cancelled_count += 1
        
        logger.info(f"Cancelled {cancelled_count} paper orders")
        return cancelled_count
    
    def process_market_data(self, symbol: str, price: float, timestamp: datetime = None) -> List[PaperOrder]:
        """
        Process market data and execute eligible orders.
        
        Args:
            symbol: Symbol
            price: Current market price
            timestamp: Current timestamp
            
        Returns:
            List of filled orders
        """
        filled_orders = []
        timestamp = timestamp or datetime.utcnow()
        
        for order in self.get_pending_orders(symbol):
            if self._should_execute_order(order, price):
                execution_price = self._get_execution_price(order, price)
                commission = self._calculate_commission(order, execution_price)
                
                if order.fill(order.remaining_quantity, execution_price, timestamp, commission):
                    filled_orders.append(order)
                    self.order_history.append(order)
                    logger.info(f"Filled paper order: {order.id} at {execution_price}")
        
        return filled_orders
    
    def _should_execute_order(self, order: PaperOrder, current_price: float) -> bool:
        """Check if order should be executed at current price."""
        if order.order_type == PaperOrderType.MARKET:
            return True
        
        elif order.order_type == PaperOrderType.LIMIT:
            if order.is_buy:
                return current_price <= order.price
            else:
                return current_price >= order.price
        
        elif order.order_type == PaperOrderType.STOP:
            if order.is_buy:
                return current_price >= order.stop_price
            else:
                return current_price <= order.stop_price
        
        return False
    
    def _get_execution_price(self, order: PaperOrder, current_price: float) -> float:
        """Get execution price for order."""
        if order.order_type == PaperOrderType.MARKET:
            return current_price
        elif order.order_type == PaperOrderType.LIMIT:
            return order.price
        elif order.order_type == PaperOrderType.STOP:
            return current_price
        
        return current_price
    
    def _calculate_commission(self, order: PaperOrder, price: float) -> float:
        """Calculate commission for order."""
        return order.remaining_quantity * price * self.commission_rate
    
    def get_order_statistics(self) -> Dict[str, Any]:
        """Get order statistics."""
        all_orders = list(self.orders.values())
        
        return {
            'total_orders': len(all_orders),
            'pending_orders': len([o for o in all_orders if o.is_pending]),
            'filled_orders': len([o for o in all_orders if o.is_filled]),
            'cancelled_orders': len([o for o in all_orders if o.status == PaperOrderStatus.CANCELLED]),
            'rejected_orders': len([o for o in all_orders if o.status == PaperOrderStatus.REJECTED]),
            'total_commission': sum(o.commission for o in all_orders),
            'avg_fill_time': self._calculate_avg_fill_time()
        }
    
    def _calculate_avg_fill_time(self) -> float:
        """Calculate average time to fill orders."""
        filled_orders = [o for o in self.orders.values() if o.is_filled and o.filled_at]
        
        if not filled_orders:
            return 0.0
        
        total_time = sum((o.filled_at - o.created_at).total_seconds() for o in filled_orders)
        return total_time / len(filled_orders)
