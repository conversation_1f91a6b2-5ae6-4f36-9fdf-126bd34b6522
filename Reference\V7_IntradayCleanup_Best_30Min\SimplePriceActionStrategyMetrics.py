"""
Metrics Calculator Module

This module provides accurate performance metrics calculation for trading strategies,
independent of the backtesting library's internal calculations.
"""

import pandas as pd
import numpy as np
from datetime import datetime


def calculate_metrics(trades_df, initial_capital=10000, margin=1.0):
    """
    Calculate comprehensive performance metrics from a trade log DataFrame.
    
    Parameters:
    -----------
    trades_df : pandas.DataFrame
        DataFrame containing trade data with columns:
        - Entry DateTime
        - Exit DateTime
        - Entry Price
        - Exit Price
        - Profit Points
        - Profit Percent
        - Position (LONG/SHORT)
        - Exit Reason
    initial_capital : float
        Initial capital to start with
    margin : float
        Margin requirement (e.g., 0.1 for 10:1 leverage)
        
    Returns:
    --------
    dict
        Dictionary containing calculated performance metrics
    """
    # Basic statistics
    total_trades = len(trades_df)
    winning_trades = len(trades_df[trades_df['Profit Points'] > 0])
    losing_trades = len(trades_df[trades_df['Profit Points'] < 0])
    breakeven_trades = len(trades_df[trades_df['Profit Points'] == 0])
    
    win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
    
    # Calculate profit factor
    total_profit = trades_df[trades_df['Profit Points'] > 0]['Profit Points'].sum()
    total_loss = abs(trades_df[trades_df['Profit Points'] < 0]['Profit Points'].sum())
    profit_factor = total_profit / total_loss if total_loss != 0 else float('inf')
    
    # Calculate average profit per trade
    avg_profit_per_trade = trades_df['Profit Points'].mean()
    avg_profit_percent = trades_df['Profit Percent'].mean()
    
    # Calculate average profit for winning and losing trades
    avg_win = trades_df[trades_df['Profit Points'] > 0]['Profit Points'].mean() if winning_trades > 0 else 0
    avg_loss = trades_df[trades_df['Profit Points'] < 0]['Profit Points'].mean() if losing_trades > 0 else 0
    
    # Calculate max consecutive wins and losses
    trades_df['Win'] = trades_df['Profit Points'] > 0
    win_streak = 0
    max_win_streak = 0
    loss_streak = 0
    max_loss_streak = 0
    
    for win in trades_df['Win']:
        if win:
            win_streak += 1
            loss_streak = 0
        else:
            loss_streak += 1
            win_streak = 0
        
        max_win_streak = max(max_win_streak, win_streak)
        max_loss_streak = max(max_loss_streak, loss_streak)
    
    # Calculate equity curve with leverage
    trades_df['Profit with Leverage'] = trades_df['Profit Percent'] * (1/margin)
    trades_df['Equity'] = initial_capital * (1 + trades_df['Profit with Leverage'].cumsum() / 100)
    
    final_equity = trades_df['Equity'].iloc[-1]
    peak_equity = trades_df['Equity'].max()
    total_return_pct = ((final_equity / initial_capital) - 1) * 100
    
    # Calculate max drawdown from equity curve
    equity_peak = 0
    equity_drawdown = []
    max_equity_drawdown_pct = 0
    
    for equity in trades_df['Equity']:
        equity_peak = max(equity_peak, equity)
        dd = (equity_peak - equity) / equity_peak * 100 if equity_peak > 0 else 0
        equity_drawdown.append(dd)
        max_equity_drawdown_pct = max(max_equity_drawdown_pct, dd)
    
    trades_df['Equity Drawdown %'] = equity_drawdown
    
    # Calculate annualized return
    start_date = pd.to_datetime(trades_df['Entry DateTime'].iloc[0])
    end_date = pd.to_datetime(trades_df['Exit DateTime'].iloc[-1])
    years = (end_date - start_date).days / 365.25
    
    annualized_return = ((1 + total_return_pct/100) ** (1/years) - 1) * 100 if years > 0 else 0
    
    # Calculate position distribution
    long_trades = len(trades_df[trades_df['Position'] == 'LONG'])
    short_trades = len(trades_df[trades_df['Position'] == 'SHORT'])
    long_win_rate = (len(trades_df[(trades_df['Position'] == 'LONG') & (trades_df['Profit Points'] > 0)]) / long_trades) * 100 if long_trades > 0 else 0
    short_win_rate = (len(trades_df[(trades_df['Position'] == 'SHORT') & (trades_df['Profit Points'] > 0)]) / short_trades) * 100 if short_trades > 0 else 0
    
    # Calculate exit reason distribution
    exit_reasons = trades_df['Exit Reason'].value_counts().to_dict()
    
    # Calculate trade duration statistics
    if 'Trade Duration' in trades_df.columns:
        # Parse duration in format "0 days 00:05:00"
        def parse_duration(duration_str):
            if not isinstance(duration_str, str):
                return 0
            
            try:
                # Handle format like "0 days 00:05:00"
                if 'days' in duration_str:
                    days_part, time_part = duration_str.split('days')
                    days = int(days_part.strip())
                    hours, minutes, seconds = map(int, time_part.strip().split(':'))
                    return days * 86400 + hours * 3600 + minutes * 60 + seconds
                # Handle format like "00:05:00"
                else:
                    parts = duration_str.split(':')
                    if len(parts) == 3:
                        hours, minutes, seconds = map(int, parts)
                        return hours * 3600 + minutes * 60 + seconds
                    elif len(parts) == 2:
                        minutes, seconds = map(int, parts)
                        return minutes * 60 + seconds
                    else:
                        return int(parts[0])
            except Exception:
                print(f"Warning: Could not parse duration string: {duration_str}")
                return 0
        
        trades_df['Duration_Seconds'] = trades_df['Trade Duration'].apply(parse_duration)
        avg_duration_seconds = trades_df['Duration_Seconds'].mean()
        avg_duration_minutes = avg_duration_seconds / 60
    else:
        avg_duration_minutes = 0
    
    # Calculate Sharpe Ratio (assuming risk-free rate of 0%)
    if years > 0:
        # Convert to daily returns for Sharpe calculation
        trades_df['Date'] = pd.to_datetime(trades_df['Exit DateTime']).dt.date
        daily_returns = trades_df.groupby('Date')['Profit with Leverage'].sum() / 100
        
        # Annualized Sharpe Ratio
        sharpe_ratio = (daily_returns.mean() * 252) / (daily_returns.std() * np.sqrt(252)) if daily_returns.std() > 0 else 0
        
        # Sortino Ratio (only considering negative returns for denominator)
        negative_returns = daily_returns[daily_returns < 0]
        sortino_ratio = (daily_returns.mean() * 252) / (negative_returns.std() * np.sqrt(252)) if len(negative_returns) > 0 and negative_returns.std() > 0 else 0
        
        # Calmar Ratio
        calmar_ratio = annualized_return / max_equity_drawdown_pct if max_equity_drawdown_pct > 0 else 0
    else:
        sharpe_ratio = 0
        sortino_ratio = 0
        calmar_ratio = 0

    # Calculate Risk-to-Reward Ratio
    risk_reward_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0

    # Compile all metrics into a dictionary
    metrics = {
        'Total Trades': total_trades,
        'Winning Trades': winning_trades,
        'Losing Trades': losing_trades,
        'Breakeven Trades': breakeven_trades,
        'Win Rate (%)': win_rate,
        'Profit Factor': profit_factor,
        'Average Profit per Trade (Points)': avg_profit_per_trade,
        'Average Profit per Trade (%)': avg_profit_percent,
        'Average Winning Trade (Points)': avg_win,
        'Average Losing Trade (Points)': avg_loss,
        'Max Consecutive Wins': max_win_streak,
        'Max Consecutive Losses': max_loss_streak,
        'Initial Capital': initial_capital,
        'Final Equity': final_equity,
        'Peak Equity': peak_equity,
        'Total Return (%)': total_return_pct,
        'Annualized Return (%)': annualized_return,
        'Max Drawdown (%)': max_equity_drawdown_pct,
        'Sharpe Ratio': sharpe_ratio,
        'Sortino Ratio': sortino_ratio,
        'Calmar Ratio': calmar_ratio,
        'Risk-to-Reward Ratio': risk_reward_ratio,
        'Long Trades': long_trades,
        'Short Trades': short_trades,
        'Long Win Rate (%)': long_win_rate,
        'Short Win Rate (%)': short_win_rate,
        'Exit Reasons': exit_reasons,
        'Average Trade Duration (minutes)': avg_duration_minutes,
        'Trading Period (years)': years
    }
    
    return metrics


def print_metrics_comparison(custom_metrics, backtest_stats):
    """
    Print a comparison between custom calculated metrics and backtest library metrics.
    
    Parameters:
    -----------
    custom_metrics : dict
        Dictionary of custom calculated metrics
    backtest_stats : pandas.Series
        Series containing backtest statistics from the backtesting library
    """
    print("\n" + "="*80)
    print(" "*30 + "METRICS COMPARISON")
    print("="*80)
    
    # Format the comparison table
    print(f"{'Metric':<30} | {'Custom Calculation':<20} | {'Backtest Library':<20}")
    print("-"*80)
    
    # Map custom metrics to backtest stats keys
    comparison_map = {
        'Total Return (%)': 'Return [%]',
        'Annualized Return (%)': 'Return (Ann.) [%]',
        'Max Drawdown (%)': 'Max. Drawdown [%]',
        'Win Rate (%)': 'Win Rate [%]',
        'Profit Factor': 'Profit Factor',
        'Sharpe Ratio': 'Sharpe Ratio',
        'Sortino Ratio': 'Sortino Ratio',
        'Calmar Ratio': 'Calmar Ratio',
        'Total Trades': '# Trades'
    }
    
    # Print the comparison
    for custom_key, backtest_key in comparison_map.items():
        custom_value = custom_metrics.get(custom_key, 'N/A')
        backtest_value = backtest_stats.get(backtest_key, 'N/A')
        
        # Format values as strings with appropriate precision
        if isinstance(custom_value, (int, float)) and not isinstance(custom_value, bool):
            if custom_key in ['Total Trades']:
                custom_value_str = f"{custom_value:.0f}"
            else:
                custom_value_str = f"{custom_value:.2f}"
        else:
            custom_value_str = str(custom_value)
            
        if isinstance(backtest_value, (int, float)) and not isinstance(backtest_value, bool):
            if backtest_key in ['# Trades']:
                backtest_value_str = f"{backtest_value:.0f}"
            else:
                backtest_value_str = f"{backtest_value:.2f}"
        else:
            backtest_value_str = str(backtest_value)
        
        print(f"{custom_key:<30} | {custom_value_str:<20} | {backtest_value_str:<20}")
    
    print("="*80)
    print("NOTE: Significant discrepancies may indicate calculation differences or issues.")
    print("      Custom metrics are calculated directly from trade data and are more reliable.")
    print("="*80)


def print_detailed_custom_metrics(metrics):
    """
    Print detailed custom metrics in a formatted way.
    
    Parameters:
    -----------
    metrics : dict
        Dictionary of calculated metrics
    """
    print("\n" + "="*80)
    print(" "*30 + "DETAILED CUSTOM METRICS")
    print("="*80)
    
    # Trade Statistics
    print("\n--- Trade Statistics ---")
    print(f"Total Trades:                       {metrics['Total Trades']}")
    print(f"Winning Trades:                     {metrics['Winning Trades']} ({metrics['Win Rate (%)']:.2f}%)")
    print(f"Losing Trades:                      {metrics['Losing Trades']} ({100 - metrics['Win Rate (%)']:.2f}%)")
    print(f"Breakeven Trades:                   {metrics['Breakeven Trades']}")
    print(f"Profit Factor:                      {metrics['Profit Factor']:.2f}")
    print(f"Average Profit per Trade:           {metrics['Average Profit per Trade (Points)']:.2f} points ({metrics['Average Profit per Trade (%)']:.2f}%)")
    print(f"Average Winning Trade:              {metrics['Average Winning Trade (Points)']:.2f} points")
    print(f"Average Losing Trade:               {metrics['Average Losing Trade (Points)']:.2f} points")
    print(f"Max Consecutive Wins:               {metrics['Max Consecutive Wins']}")
    print(f"Max Consecutive Losses:             {metrics['Max Consecutive Losses']}")
    
    # Performance Metrics
    print("\n--- Performance Metrics ---")
    print(f"Initial Capital:                    ${metrics['Initial Capital']:.2f}")
    print(f"Final Equity:                       ${metrics['Final Equity']:.2f}")
    print(f"Peak Equity:                        ${metrics['Peak Equity']:.2f}")
    print(f"Total Return:                       {metrics['Total Return (%)']:.2f}%")
    print(f"Annualized Return:                  {metrics['Annualized Return (%)']:.2f}%")
    print(f"Max Drawdown:                       {metrics['Max Drawdown (%)']:.2f}%")
    print(f"Sharpe Ratio:                       {metrics['Sharpe Ratio']:.2f}")
    print(f"Sortino Ratio:                      {metrics['Sortino Ratio']:.2f}")
    print(f"Calmar Ratio:                       {metrics['Calmar Ratio']:.2f}")
    print(f"Risk-to-Reward Ratio:               {metrics['Risk-to-Reward Ratio']:.2f}")
    
    # Position Analysis
    print("\n--- Position Analysis ---")
    print(f"Long Trades:                        {metrics['Long Trades']} ({metrics['Long Trades'] / metrics['Total Trades'] * 100:.2f}%)")
    print(f"Short Trades:                       {metrics['Short Trades']} ({metrics['Short Trades'] / metrics['Total Trades'] * 100:.2f}%)")
    print(f"Long Win Rate:                      {metrics['Long Win Rate (%)']:.2f}%")
    print(f"Short Win Rate:                     {metrics['Short Win Rate (%)']:.2f}%")
    
    # Exit Reason Analysis
    print("\n--- Exit Reason Analysis ---")
    for reason, count in metrics['Exit Reasons'].items():
        percentage = (count / metrics['Total Trades']) * 100
        print(f"{reason}:                 {count} trades ({percentage:.2f}%)")
    
    # Time Analysis
    print("\n--- Time Analysis ---")
    print(f"Average Trade Duration:             {metrics['Average Trade Duration (minutes)']:.2f} minutes")
    print(f"Trading Period:                     {metrics['Trading Period (years)']:.2f} years")
    
    print("="*80)


def verify_trade_data_integrity(trades_df):
    """
    Verify the integrity of trade data by checking for inconsistencies in profit calculation.
    
    Parameters:
    -----------
    trades_df : pandas.DataFrame
        DataFrame containing trade data
        
    Returns:
    --------
    bool
        True if no inconsistencies found, False otherwise
    """
    print("\n" + "="*80)
    print(" "*30 + "TRADE DATA INTEGRITY CHECK")
    print("="*80)
    
    inconsistencies = 0
    
    # Check for inconsistencies in profit calculation
    print("\nChecking for inconsistencies in profit calculation...")
    for i, row in trades_df.iterrows():
        entry_price = row['Entry Price']
        exit_price = row['Exit Price']
        position = row['Position']
        reported_profit = row['Profit Points']
        
        if position == 'LONG':
            calculated_profit = exit_price - entry_price
        else:  # SHORT
            calculated_profit = entry_price - exit_price
        
        # Allow for small floating point differences
        if abs(calculated_profit - reported_profit) > 0.01:
            print(f"Inconsistency found in trade {i+1}:")
            print(f"  Position: {position}")
            print(f"  Entry Price: {entry_price}")
            print(f"  Exit Price: {exit_price}")
            print(f"  Reported Profit: {reported_profit}")
            print(f"  Calculated Profit: {calculated_profit}")
            print(f"  Difference: {abs(calculated_profit - reported_profit)}")
            inconsistencies += 1
    
    # Check for inconsistencies in profit percentage calculation
    print("\nChecking for inconsistencies in profit percentage calculation...")
    inconsistencies_pct = 0
    for i, row in trades_df.iterrows():
        entry_price = row['Entry Price']
        profit_points = row['Profit Points']
        reported_profit_pct = row['Profit Percent']
        
        calculated_profit_pct = (profit_points / entry_price) * 100 if entry_price != 0 else 0
        
        # Allow for small floating point differences and rounding
        if abs(calculated_profit_pct - reported_profit_pct) > 0.01:
            print(f"Inconsistency found in trade {i+1}:")
            print(f"  Entry Price: {entry_price}")
            print(f"  Profit Points: {profit_points}")
            print(f"  Reported Profit %: {reported_profit_pct}")
            print(f"  Calculated Profit %: {calculated_profit_pct}")
            print(f"  Difference: {abs(calculated_profit_pct - reported_profit_pct)}")
            inconsistencies_pct += 1
    
    if inconsistencies == 0 and inconsistencies_pct == 0:
        print("\nNo inconsistencies found in trade data. Data integrity verified.")
        integrity_verified = True
    else:
        print(f"\nFound {inconsistencies} inconsistencies in profit calculation.")
        print(f"Found {inconsistencies_pct} inconsistencies in profit percentage calculation.")
        integrity_verified = False
    
    print("="*80)
    return integrity_verified