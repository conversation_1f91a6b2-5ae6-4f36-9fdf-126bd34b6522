#!/usr/bin/env python3
"""
Reset database script - drops all tables and recreates them.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text
from app.database.connection import engine
from app.database.models import Base
from app.core.logging import setup_logging

logger = setup_logging(level="INFO")


def drop_all_tables():
    """Drop all tables including hypertables."""
    logger.info("Dropping all tables...")
    
    try:
        with engine.connect() as conn:
            # Drop hypertables first
            tables_to_drop = [
                'stock_ohlcv',
                'stock_ohlcv_agg', 
                'screener_results',
                'paper_trades',
                'backtest_trades',
                'backtest_results',
                'strategies',
                'symbols'
            ]
            
            for table in tables_to_drop:
                try:
                    conn.execute(text(f"DROP TABLE IF EXISTS {table} CASCADE;"))
                    logger.info(f"Dropped table: {table}")
                except Exception as e:
                    logger.warning(f"Could not drop table {table}: {e}")
            
            conn.commit()
            logger.info("All tables dropped successfully")
            
    except Exception as e:
        logger.error(f"Failed to drop tables: {e}")
        raise


def recreate_tables():
    """Recreate all tables."""
    logger.info("Recreating all tables...")
    
    try:
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("All tables recreated successfully")
        
    except Exception as e:
        logger.error(f"Failed to recreate tables: {e}")
        raise


def main():
    """Main function to reset database."""
    logger.info("=" * 60)
    logger.info("DATABASE RESET")
    logger.info("=" * 60)
    
    try:
        # Drop all tables
        drop_all_tables()
        
        # Recreate tables
        recreate_tables()
        
        logger.info("Database reset completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Database reset failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
