Collecting pytest-xdist
  Downloading pytest_xdist-3.8.0-py3-none-any.whl.metadata (3.0 kB)
Collecting execnet>=2.1 (from pytest-xdist)
  Using cached execnet-2.1.1-py3-none-any.whl.metadata (2.9 kB)
Requirement already satisfied: pytest>=7.0.0 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from pytest-xdist) (7.4.3)
Requirement already satisfied: iniconfig in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from pytest>=7.0.0->pytest-xdist) (2.1.0)
Requirement already satisfied: packaging in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from pytest>=7.0.0->pytest-xdist) (24.2)
Requirement already satisfied: pluggy<2.0,>=0.12 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from pytest>=7.0.0->pytest-xdist) (1.5.0)
Requirement already satisfied: exceptiongroup>=1.0.0rc8 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from pytest>=7.0.0->pytest-xdist) (1.2.2)
Requirement already satisfied: tomli>=1.0.0 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from pytest>=7.0.0->pytest-xdist) (2.2.1)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from pytest>=7.0.0->pytest-xdist) (0.4.6)
Downloading pytest_xdist-3.8.0-py3-none-any.whl (46 kB)
Using cached execnet-2.1.1-py3-none-any.whl (40 kB)
Installing collected packages: execnet, pytest-xdist

Successfully installed execnet-2.1.1 pytest-xdist-3.8.0
