"""
Common dependencies for API endpoints.
"""

from typing import Generator
from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.database.connection import get_db
from app.services.data_service import DataService
from app.services.market_data_service import MarketDataService
from app.services.aggregation_service import AggregationService


def get_data_service(db: Session = Depends(get_db)) -> DataService:
    """Get data service instance."""
    return DataService(db)


def get_market_data_service(db: Session = Depends(get_db)) -> MarketDataService:
    """Get market data service instance."""
    return MarketDataService(db)


def get_aggregation_service(db: Session = Depends(get_db)) -> AggregationService:
    """Get aggregation service instance."""
    return AggregationService(db)


def validate_symbol(symbol: str) -> str:
    """Validate symbol format."""
    if not symbol or len(symbol.strip()) == 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Symbol cannot be empty"
        )
    return symbol.upper().strip()


def validate_timeframe(timeframe: str) -> str:
    """Validate timeframe format."""
    valid_timeframes = ["1m", "5m", "10m", "15m", "30m", "1h", "2h", "4h", "1d"]
    if timeframe not in valid_timeframes:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid timeframe. Must be one of: {', '.join(valid_timeframes)}"
        )
    return timeframe


def validate_pagination(skip: int = 0, limit: int = 100) -> tuple[int, int]:
    """Validate pagination parameters."""
    if skip < 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Skip must be non-negative"
        )
    
    if limit <= 0 or limit > 1000:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Limit must be between 1 and 1000"
        )
    
    return skip, limit
