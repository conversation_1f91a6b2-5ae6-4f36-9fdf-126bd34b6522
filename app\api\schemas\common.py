"""
Common Pydantic schemas for API models.
"""

from typing import Optional, Any, Dict, List
from datetime import datetime
from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """Base response model."""
    success: bool = True
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ErrorResponse(BaseResponse):
    """Error response model."""
    success: bool = False
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class PaginationParams(BaseModel):
    """Pagination parameters."""
    skip: int = Field(default=0, ge=0, description="Number of records to skip")
    limit: int = Field(default=100, ge=1, le=1000, description="Maximum number of records to return")


class PaginatedResponse(BaseResponse):
    """Paginated response model."""
    total: int = Field(description="Total number of records")
    skip: int = Field(description="Number of records skipped")
    limit: int = Field(description="Maximum number of records returned")
    has_more: bool = Field(description="Whether there are more records available")


class DateRangeParams(BaseModel):
    """Date range parameters."""
    start_date: datetime = Field(description="Start date and time")
    end_date: datetime = Field(description="End date and time")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class SymbolInfo(BaseModel):
    """Symbol information model."""
    id: int
    symbol: str
    name: Optional[str] = None
    market_type: str
    exchange: Optional[str] = None
    is_active: bool = True
    
    class Config:
        from_attributes = True


class OHLCVData(BaseModel):
    """OHLCV data model."""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class QuoteData(BaseModel):
    """Real-time quote data model."""
    symbol: str
    ltp: float
    change: Optional[float] = None
    change_percent: Optional[float] = None
    volume: Optional[int] = None
    timestamp: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
