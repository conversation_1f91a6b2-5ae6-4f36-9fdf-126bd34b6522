#!/usr/bin/env python3
"""
Database setup script for Signal Stack Trading Platform.
This script initializes the PostgreSQL database with TimescaleDB extension,
creates all necessary tables, hypertables, indexes, and stored procedures.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import setup_logging
from app.database.init_db import setup_database
from app.database.connection import check_database_connection
from app.core.config import settings

def main():
    """Main function to setup the database."""
    logger = setup_logging(level="INFO")
    
    logger.info("=" * 60)
    logger.info("SIGNAL STACK TRADING PLATFORM - DATABASE SETUP")
    logger.info("=" * 60)
    
    # Display configuration
    logger.info(f"Database Host: {settings.database.host}")
    logger.info(f"Database Port: {settings.database.port}")
    logger.info(f"Database Name: {settings.database.name}")
    logger.info(f"Database User: {settings.database.user}")
    logger.info(f"TimescaleDB Enabled: {settings.database.timescale_enabled}")
    
    # Check if .env file exists
    env_file = project_root / ".env"
    if not env_file.exists():
        logger.warning(f".env file not found at {env_file}")
        logger.warning("Please copy .env.example to .env and update the configuration")
        return False
    
    # Check database connection
    logger.info("Checking database connection...")
    if not check_database_connection():
        logger.error("Database connection failed!")
        logger.error("Please ensure:")
        logger.error("1. PostgreSQL is running")
        logger.error("2. Database credentials are correct in .env file")
        logger.error("3. Database 'nse_db' exists")
        return False
    
    logger.info("Database connection successful!")
    
    # Setup database
    try:
        logger.info("Starting database setup...")
        setup_database()
        logger.info("Database setup completed successfully!")
        
        logger.info("=" * 60)
        logger.info("DATABASE SETUP SUMMARY")
        logger.info("=" * 60)
        logger.info("✓ Tables created")
        if settings.database.timescale_enabled:
            logger.info("✓ TimescaleDB hypertables created")
            logger.info("✓ Indexes created")
            logger.info("✓ Stored procedures created")
        logger.info("✓ Database ready for use")
        
        return True
        
    except Exception as e:
        logger.error(f"Database setup failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
