"""
Integration tests for API endpoints.
"""

import pytest
from fastapi.testclient import TestClient
import json
from datetime import datetime


@pytest.mark.integration
class TestMarketDataAPI:
    """Integration tests for market data API endpoints."""
    
    def test_get_symbols(self, client: TestClient, auth_headers: dict, sample_symbol):
        """Test getting symbols list."""
        response = client.get("/api/v1/market-data/symbols", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "symbols" in data
        assert len(data["symbols"]) > 0
        
        # Check if our sample symbol is in the list
        symbols = [s["symbol"] for s in data["symbols"]]
        assert "NIFTY" in symbols
    
    def test_get_symbol_info(self, client: TestClient, auth_headers: dict, sample_symbol):
        """Test getting specific symbol information."""
        response = client.get("/api/v1/market-data/symbols/NIFTY", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["symbol"] == "NIFTY"
        assert data["name"] == "NIFTY 50 Index"
        assert data["exchange"] == "NSE"
    
    def test_get_ohlcv_data(self, client: TestClient, auth_headers: dict, sample_ohlcv_data):
        """Test getting OHLCV data."""
        params = {
            "symbol": "NIFTY",
            "start_date": "2024-01-01",
            "end_date": "2024-04-10",
            "timeframe": "1D"
        }
        
        response = client.get("/api/v1/market-data/ohlcv", params=params, headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert len(data["data"]) > 0
        
        # Check data structure
        first_record = data["data"][0]
        required_fields = ["timestamp", "open", "high", "low", "close", "volume"]
        for field in required_fields:
            assert field in first_record
    
    def test_get_latest_quotes(self, client: TestClient, auth_headers: dict, sample_ohlcv_data):
        """Test getting latest quotes."""
        response = client.get("/api/v1/market-data/quotes", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_market_status(self, client: TestClient, auth_headers: dict):
        """Test getting market status."""
        response = client.get("/api/v1/market-data/status", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "market_status" in data
        assert "timestamp" in data


@pytest.mark.integration
class TestIndicatorsAPI:
    """Integration tests for indicators API endpoints."""
    
    def test_get_available_indicators(self, client: TestClient, auth_headers: dict):
        """Test getting available indicators."""
        response = client.get("/api/v1/indicators/available", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "indicators" in data
        assert len(data["indicators"]) > 0
        
        # Check for common indicators
        indicator_names = [ind["name"] for ind in data["indicators"]]
        assert "RSI" in indicator_names
        assert "SMA" in indicator_names
        assert "EMA" in indicator_names
    
    def test_calculate_indicator(self, client: TestClient, auth_headers: dict, sample_ohlcv_data):
        """Test calculating an indicator."""
        payload = {
            "symbol": "NIFTY",
            "indicator": "RSI",
            "parameters": {"period": 14},
            "start_date": "2024-01-01",
            "end_date": "2024-04-10"
        }
        
        response = client.post(
            "/api/v1/indicators/calculate",
            json=payload,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "values" in data
        assert "timestamps" in data
        assert len(data["values"]) > 0
    
    def test_bulk_indicator_calculation(self, client: TestClient, auth_headers: dict, sample_ohlcv_data):
        """Test bulk indicator calculation."""
        payload = {
            "symbol": "NIFTY",
            "indicators": [
                {"name": "RSI", "parameters": {"period": 14}},
                {"name": "SMA", "parameters": {"period": 20}},
                {"name": "EMA", "parameters": {"period": 12}}
            ],
            "start_date": "2024-01-01",
            "end_date": "2024-04-10"
        }
        
        response = client.post(
            "/api/v1/indicators/bulk-calculate",
            json=payload,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "results" in data
        assert len(data["results"]) == 3  # Three indicators


@pytest.mark.integration
class TestBacktestingAPI:
    """Integration tests for backtesting API endpoints."""
    
    def test_get_available_strategies(self, client: TestClient, auth_headers: dict):
        """Test getting available strategies."""
        response = client.get("/api/v1/backtesting/strategies", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "strategies" in data
        assert len(data["strategies"]) > 0
    
    def test_run_backtest(self, client: TestClient, auth_headers: dict, sample_ohlcv_data):
        """Test running a backtest."""
        payload = {
            "strategy_name": "ma_crossover",
            "symbol": "NIFTY",
            "start_date": "2024-01-01",
            "end_date": "2024-04-10",
            "parameters": {
                "short_window": 10,
                "long_window": 20
            },
            "initial_capital": 100000
        }
        
        response = client.post(
            "/api/v1/backtesting/run",
            json=payload,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "backtest_id" in data
        assert "status" in data
    
    def test_get_backtest_results(self, client: TestClient, auth_headers: dict):
        """Test getting backtest results."""
        # First run a backtest
        payload = {
            "strategy_name": "ma_crossover",
            "symbol": "NIFTY",
            "start_date": "2024-01-01",
            "end_date": "2024-04-10",
            "parameters": {
                "short_window": 5,
                "long_window": 10
            },
            "initial_capital": 100000
        }
        
        run_response = client.post(
            "/api/v1/backtesting/run",
            json=payload,
            headers=auth_headers
        )
        
        assert run_response.status_code == 200
        backtest_id = run_response.json()["backtest_id"]
        
        # Get results
        response = client.get(
            f"/api/v1/backtesting/results/{backtest_id}",
            headers=auth_headers
        )
        
        # Results might be pending, so accept both 200 and 202
        assert response.status_code in [200, 202]


@pytest.mark.integration
class TestPaperTradingAPI:
    """Integration tests for paper trading API endpoints."""
    
    def test_create_session(self, client: TestClient, auth_headers: dict):
        """Test creating a paper trading session."""
        payload = {
            "name": "Test Session",
            "initial_capital": 100000,
            "symbols": ["NIFTY"],
            "strategy_name": "ma_crossover",
            "strategy_parameters": {
                "short_window": 5,
                "long_window": 10
            }
        }
        
        response = client.post(
            "/api/v1/paper-trading/sessions",
            json=payload,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "session_id" in data
        assert "success" in data
        assert data["success"] is True
    
    def test_get_sessions(self, client: TestClient, auth_headers: dict):
        """Test getting paper trading sessions."""
        response = client.get("/api/v1/paper-trading/sessions", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "sessions" in data
        assert isinstance(data["sessions"], list)
    
    def test_place_order(self, client: TestClient, auth_headers: dict):
        """Test placing an order in paper trading."""
        # First create a session
        session_payload = {
            "name": "Order Test Session",
            "initial_capital": 100000,
            "symbols": ["NIFTY"],
            "strategy_name": "manual"
        }
        
        session_response = client.post(
            "/api/v1/paper-trading/sessions",
            json=session_payload,
            headers=auth_headers
        )
        
        assert session_response.status_code == 200
        session_id = session_response.json()["session_id"]
        
        # Place an order
        order_payload = {
            "symbol": "NIFTY",
            "side": "BUY",
            "quantity": 1,
            "order_type": "MARKET"
        }
        
        response = client.post(
            f"/api/v1/paper-trading/sessions/{session_id}/orders",
            json=order_payload,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "order_id" in data
        assert "success" in data
        assert data["success"] is True


@pytest.mark.integration
class TestAuthenticationAPI:
    """Integration tests for authentication API endpoints."""
    
    def test_get_demo_token(self, client: TestClient):
        """Test getting a demo token."""
        response = client.post("/api/v1/auth/demo-token")
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "token_type" in data
        assert data["token_type"] == "bearer"
    
    def test_login_with_demo_credentials(self, client: TestClient):
        """Test login with demo credentials."""
        payload = {
            "username": "demo",
            "password": "demo123"
        }
        
        response = client.post("/api/v1/auth/login", data=payload)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "token_type" in data
    
    def test_get_user_info(self, client: TestClient):
        """Test getting user information."""
        # First get a token
        token_response = client.post("/api/v1/auth/demo-token")
        token = token_response.json()["access_token"]
        
        headers = {"Authorization": f"Bearer {token}"}
        response = client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "user_id" in data
        assert "permissions" in data
    
    def test_validate_token(self, client: TestClient):
        """Test token validation."""
        # First get a token
        token_response = client.post("/api/v1/auth/demo-token")
        token = token_response.json()["access_token"]
        
        headers = {"Authorization": f"Bearer {token}"}
        response = client.get("/api/v1/auth/validate", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "valid" in data
        assert data["valid"] is True


@pytest.mark.integration
class TestErrorHandling:
    """Integration tests for error handling."""
    
    def test_unauthorized_access(self, client: TestClient):
        """Test unauthorized access to protected endpoints."""
        response = client.get("/api/v1/market-data/symbols")
        
        assert response.status_code == 401
    
    def test_invalid_symbol(self, client: TestClient, auth_headers: dict):
        """Test handling of invalid symbol."""
        response = client.get(
            "/api/v1/market-data/symbols/INVALID",
            headers=auth_headers
        )
        
        assert response.status_code == 404
    
    def test_invalid_date_range(self, client: TestClient, auth_headers: dict):
        """Test handling of invalid date range."""
        params = {
            "symbol": "NIFTY",
            "start_date": "2024-12-31",  # Future date
            "end_date": "2024-01-01",    # Past date (invalid range)
            "timeframe": "1D"
        }
        
        response = client.get(
            "/api/v1/market-data/ohlcv",
            params=params,
            headers=auth_headers
        )
        
        assert response.status_code == 400
    
    def test_malformed_request(self, client: TestClient, auth_headers: dict):
        """Test handling of malformed requests."""
        # Send invalid JSON
        response = client.post(
            "/api/v1/indicators/calculate",
            data="invalid json",
            headers={**auth_headers, "Content-Type": "application/json"}
        )
        
        assert response.status_code == 422
