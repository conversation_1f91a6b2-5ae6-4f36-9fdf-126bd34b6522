"""
Paper trading service for managing virtual trading sessions.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from sqlalchemy.orm import Session
import uuid

from app.core.logging import get_logger
from app.services.paper_trading import PaperTradingEngine, PaperPortfolio
from app.services.market_data_service import MarketDataService
from app.services.backtesting.sample_strategies import (
    MovingAverageCrossoverStrategy,
    RSIStrategy,
    BollingerBandsStrategy
)
from app.database.models import PaperTradingSession, Symbol
from app.database.repositories.symbol_repository import SymbolRepository

logger = get_logger(__name__)


class PaperTradingService:
    """Service for managing paper trading sessions."""
    
    # Available strategy classes (same as backtesting)
    STRATEGY_CLASSES = {
        'ma_crossover': MovingAverageCrossoverStrategy,
        'rsi_strategy': RSIStrategy,
        'bollinger_bands': BollingerBandsStrategy
    }
    
    def __init__(self, db: Session):
        """
        Initialize paper trading service.
        
        Args:
            db: Database session
        """
        self.db = db
        self.symbol_repo = SymbolRepository(db)
        self.market_data_service = MarketDataService(db)
        
        # Active trading engines
        self.active_engines: Dict[str, PaperTradingEngine] = {}
    
    def create_session(
        self,
        name: str,
        initial_cash: float = 100000.0,
        commission_rate: float = 0.001,
        update_interval: int = 60
    ) -> str:
        """
        Create a new paper trading session.
        
        Args:
            name: Session name
            initial_cash: Initial cash amount
            commission_rate: Commission rate
            update_interval: Price update interval in seconds
            
        Returns:
            Session ID
        """
        try:
            session_id = str(uuid.uuid4())
            
            # Create portfolio
            portfolio = PaperPortfolio(initial_cash, commission_rate)
            
            # Create trading engine
            engine = PaperTradingEngine(
                portfolio=portfolio,
                market_data_service=self.market_data_service,
                update_interval=update_interval
            )
            
            # Store engine
            self.active_engines[session_id] = engine
            
            # Save session to database
            session = PaperTradingSession(
                id=session_id,
                name=name,
                initial_cash=initial_cash,
                current_cash=initial_cash,
                commission_rate=commission_rate,
                is_active=True,
                created_at=datetime.utcnow()
            )
            
            self.db.add(session)
            self.db.commit()
            
            logger.info(f"Created paper trading session: {session_id} - {name}")
            return session_id
            
        except Exception as e:
            logger.error(f"Error creating paper trading session: {e}")
            self.db.rollback()
            raise
    
    def get_session(self, session_id: str) -> Optional[PaperTradingEngine]:
        """Get paper trading session."""
        return self.active_engines.get(session_id)
    
    def start_session(self, session_id: str) -> bool:
        """Start a paper trading session."""
        engine = self.get_session(session_id)
        if engine:
            engine.start()
            logger.info(f"Started paper trading session: {session_id}")
            return True
        return False
    
    def stop_session(self, session_id: str) -> bool:
        """Stop a paper trading session."""
        engine = self.get_session(session_id)
        if engine:
            engine.stop()
            logger.info(f"Stopped paper trading session: {session_id}")
            return True
        return False
    
    def delete_session(self, session_id: str) -> bool:
        """Delete a paper trading session."""
        try:
            # Stop engine if running
            if session_id in self.active_engines:
                self.active_engines[session_id].stop()
                del self.active_engines[session_id]
            
            # Update database
            session = self.db.query(PaperTradingSession).filter(
                PaperTradingSession.id == session_id
            ).first()
            
            if session:
                session.is_active = False
                session.ended_at = datetime.utcnow()
                self.db.commit()
            
            logger.info(f"Deleted paper trading session: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting session {session_id}: {e}")
            self.db.rollback()
            return False
    
    def add_strategy_to_session(
        self,
        session_id: str,
        strategy_type: str,
        symbols: List[str],
        parameters: Dict[str, Any] = None
    ) -> bool:
        """Add a strategy to a paper trading session."""
        try:
            engine = self.get_session(session_id)
            if not engine:
                logger.error(f"Session not found: {session_id}")
                return False
            
            # Create strategy
            if strategy_type not in self.STRATEGY_CLASSES:
                logger.error(f"Unknown strategy type: {strategy_type}")
                return False
            
            strategy_class = self.STRATEGY_CLASSES[strategy_type]
            
            if parameters:
                strategy = strategy_class(**parameters)
            else:
                strategy = strategy_class()
            
            # Add strategy to engine
            engine.add_strategy(strategy, symbols)
            
            logger.info(f"Added strategy {strategy_type} to session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding strategy to session: {e}")
            return False
    
    def place_order(
        self,
        session_id: str,
        symbol: str,
        side: str,
        order_type: str,
        quantity: int,
        price: Optional[float] = None
    ) -> Optional[str]:
        """Place an order in a paper trading session."""
        engine = self.get_session(session_id)
        if engine:
            return engine.place_order(symbol, side, order_type, quantity, price)
        return None
    
    def cancel_order(self, session_id: str, order_id: str) -> bool:
        """Cancel an order in a paper trading session."""
        engine = self.get_session(session_id)
        if engine:
            return engine.cancel_order(order_id)
        return False
    
    def close_position(self, session_id: str, symbol: str, quantity: Optional[int] = None) -> bool:
        """Close a position in a paper trading session."""
        engine = self.get_session(session_id)
        if engine:
            return engine.close_position(symbol, quantity)
        return False
    
    def get_session_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session status."""
        engine = self.get_session(session_id)
        if engine:
            return {
                'session_id': session_id,
                'engine_status': engine.get_engine_status(),
                'portfolio_status': engine.get_portfolio_status()
            }
        return None
    
    def get_session_performance(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session performance report."""
        engine = self.get_session(session_id)
        if engine:
            return engine.get_performance_report()
        return None
    

    
    def reset_session(self, session_id: str, initial_cash: float = None) -> bool:
        """Reset a paper trading session."""
        engine = self.get_session(session_id)
        if engine:
            engine.reset_portfolio(initial_cash)
            return True
        return False
    
    def get_active_sessions(self) -> List[Dict[str, Any]]:
        """Get list of active paper trading sessions."""
        try:
            sessions = self.db.query(PaperTradingSession).filter(
                PaperTradingSession.is_active == True
            ).all()
            
            session_list = []
            for session in sessions:
                session_data = {
                    'id': session.id,
                    'name': session.name,
                    'initial_cash': session.initial_cash,
                    'current_cash': session.current_cash,
                    'commission_rate': session.commission_rate,
                    'created_at': session.created_at.isoformat(),
                    'is_running': session.id in self.active_engines and self.active_engines[session.id].is_running
                }
                
                # Add current performance if engine is active
                if session.id in self.active_engines:
                    engine = self.active_engines[session.id]
                    performance = engine.portfolio.get_performance_summary()
                    session_data.update({
                        'current_value': performance['current_value'],
                        'total_return': performance['total_return'],
                        'total_trades': performance['total_trades'],
                        'open_positions': performance['open_positions']
                    })
                
                session_list.append(session_data)
            
            return session_list
            
        except Exception as e:
            logger.error(f"Error getting active sessions: {e}")
            return []
    
    def get_session_history(self, session_id: str) -> Dict[str, Any]:
        """Get session trading history."""
        engine = self.get_session(session_id)
        if not engine:
            return {'error': 'Session not found'}
        
        portfolio = engine.portfolio
        
        return {
            'session_id': session_id,
            'trades': [trade.__dict__ for trade in portfolio.trades],
            'order_history': [order.to_dict() for order in portfolio.order_manager.order_history],
            'value_history': portfolio.value_history,
            'performance_summary': portfolio.get_performance_summary()
        }
    
    def export_session_data(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Export complete session data."""
        engine = self.get_session(session_id)
        if not engine:
            return None
        
        return {
            'session_id': session_id,
            'export_timestamp': datetime.utcnow().isoformat(),
            'engine_status': engine.get_engine_status(),
            'portfolio_data': engine.get_portfolio_status(),
            'performance_report': engine.get_performance_report(),
            'trading_history': self.get_session_history(session_id)
        }
    
    def cleanup_inactive_sessions(self) -> int:
        """Clean up inactive sessions."""
        cleaned_count = 0
        
        try:
            # Get inactive sessions from database
            inactive_sessions = self.db.query(PaperTradingSession).filter(
                PaperTradingSession.is_active == False
            ).all()
            
            for session in inactive_sessions:
                if session.id in self.active_engines:
                    self.active_engines[session.id].stop()
                    del self.active_engines[session.id]
                    cleaned_count += 1
            
            logger.info(f"Cleaned up {cleaned_count} inactive sessions")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Error cleaning up sessions: {e}")
            return 0
