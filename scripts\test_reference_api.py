#!/usr/bin/env python3
"""
Test script for the Reference Backtesting API endpoints.
This script validates the API integration with the new reference framework.
"""

import sys
import time
import requests
import json
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger

logger = get_logger(__name__)

# API base URL
BASE_URL = "http://localhost:8000/api/v1/backtesting"


def test_api_connection():
    """Test basic API connection."""
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            logger.info("✅ API server is running")
            return True
        else:
            logger.error(f"❌ API server returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        logger.error("❌ Cannot connect to API server. Make sure it's running on localhost:8000")
        return False
    except Exception as e:
        logger.error(f"❌ API connection test failed: {e}")
        return False


def test_strategies_endpoint():
    """Test the strategies list endpoint."""
    logger.info("🧪 Testing /strategies endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/strategies")
        
        if response.status_code == 200:
            data = response.json()
            strategies = data.get('strategies', [])
            
            logger.info(f"✅ Strategies endpoint returned {len(strategies)} strategies")
            
            for strategy in strategies:
                logger.info(f"  - {strategy['name']}: {strategy['description']}")
            
            # Check if SimplePriceActionStrategy is available
            spa_strategy = next((s for s in strategies if s['name'] == 'SimplePriceActionStrategy'), None)
            if spa_strategy:
                logger.info("✅ SimplePriceActionStrategy found in strategies list")
                return True
            else:
                logger.error("❌ SimplePriceActionStrategy not found in strategies list")
                return False
        else:
            logger.error(f"❌ Strategies endpoint returned status {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Strategies endpoint test failed: {e}")
        return False


def test_reference_backtest_endpoint():
    """Test the reference backtest endpoint."""
    logger.info("🧪 Testing /reference/run endpoint...")
    
    try:
        # Prepare request data
        request_data = {
            "symbol": "NIFTY50",
            "timeframe": 30,
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "initial_capital": 30000,
            "margin": 0.1,
            "commission": 0.0,
            "strategy_config": {},
            "generate_files": False
        }
        
        logger.info(f"📤 Sending request: {json.dumps(request_data, indent=2)}")
        
        start_time = time.time()
        response = requests.post(f"{BASE_URL}/reference/run", json=request_data, timeout=120)
        execution_time = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                results = data.get('results', {})
                
                logger.info(f"✅ Reference backtest completed in {execution_time:.2f}s")
                logger.info(f"📊 Engine execution time: {results.get('execution_time', 0):.2f}s")
                logger.info(f"📈 Performance summary:")
                
                performance = results.get('performance_summary', {})
                logger.info(f"  - Total trades: {performance.get('total_trades', 0)}")
                logger.info(f"  - Win rate: {performance.get('win_rate', 0):.1f}%")
                logger.info(f"  - Total return: {performance.get('total_return', 0):.2f}%")
                logger.info(f"  - Max drawdown: {performance.get('max_drawdown', 0):.2f}%")
                logger.info(f"  - Sharpe ratio: {performance.get('sharpe_ratio', 0):.2f}")
                
                # Validate performance
                if performance.get('total_trades', 0) > 0:
                    logger.info("✅ Reference backtest produced trades")
                    return True
                else:
                    logger.warning("⚠️ Reference backtest produced no trades")
                    return False
            else:
                logger.error(f"❌ Reference backtest failed: {data.get('message', 'Unknown error')}")
                return False
        else:
            logger.error(f"❌ Reference endpoint returned status {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        logger.error("❌ Reference backtest request timed out")
        return False
    except Exception as e:
        logger.error(f"❌ Reference backtest endpoint test failed: {e}")
        return False


def test_standard_backtest_endpoint():
    """Test the standard backtest endpoint."""
    logger.info("🧪 Testing /run endpoint...")
    
    try:
        # Prepare request data
        request_data = {
            "strategy_type": "SimplePriceActionStrategy",
            "symbol": "NIFTY50",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "timeframe": 30,
            "initial_cash": 30000,
            "commission_rate": 0.0,
            "strategy_parameters": {},
            "position_size_method": "fixed",
            "position_size_value": 1
        }
        
        logger.info(f"📤 Sending request: {json.dumps(request_data, indent=2)}")
        
        start_time = time.time()
        response = requests.post(f"{BASE_URL}/run", json=request_data, timeout=120)
        execution_time = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            results = data.get('results', {})
            
            logger.info(f"✅ Standard backtest completed in {execution_time:.2f}s")
            logger.info(f"📊 Engine execution time: {results.get('execution_time', 0):.2f}s")
            logger.info(f"📈 Performance summary:")
            logger.info(f"  - Total trades: {results.get('total_trades', 0)}")
            logger.info(f"  - Win rate: {results.get('win_rate', 0):.1f}%")
            logger.info(f"  - Total return: {results.get('total_return', 0):.2f}%")
            logger.info(f"  - Max drawdown: {results.get('max_drawdown', 0):.2f}%")
            logger.info(f"  - Sharpe ratio: {results.get('sharpe_ratio', 0):.2f}")
            
            # Validate performance
            if results.get('total_trades', 0) > 0:
                logger.info("✅ Standard backtest produced trades")
                return True
            else:
                logger.warning("⚠️ Standard backtest produced no trades")
                return False
        else:
            logger.error(f"❌ Standard endpoint returned status {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        logger.error("❌ Standard backtest request timed out")
        return False
    except Exception as e:
        logger.error(f"❌ Standard backtest endpoint test failed: {e}")
        return False


def main():
    """Main test function."""
    logger.info("🚀 Starting Reference Backtesting API Tests")
    logger.info("="*60)
    
    success_count = 0
    total_tests = 4
    
    # Test 1: API Connection
    if test_api_connection():
        success_count += 1
    
    # Test 2: Strategies Endpoint
    if test_strategies_endpoint():
        success_count += 1
    
    # Test 3: Reference Backtest Endpoint
    if test_reference_backtest_endpoint():
        success_count += 1
    
    # Test 4: Standard Backtest Endpoint
    if test_standard_backtest_endpoint():
        success_count += 1
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("📋 API TEST SUMMARY")
    logger.info("="*60)
    logger.info(f"Tests Passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        logger.info("🎉 ALL API TESTS PASSED!")
        return 0
    else:
        logger.error("❌ SOME API TESTS FAILED!")
        return 1


if __name__ == "__main__":
    exit(main())
