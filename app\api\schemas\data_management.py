"""
Data management API schemas.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field

from app.api.schemas.common import BaseResponse


class IngestSymbolsRequest(BaseModel):
    """Ingest symbols request."""
    market_types: Optional[List[str]] = Field(default=None, description="Market types to ingest")
    force_update: bool = Field(default=False, description="Force update existing symbols")


class IngestSymbolsResponse(BaseResponse):
    """Ingest symbols response."""
    symbols_ingested: int
    total_symbols: int
    execution_time: float


class IngestHistoricalDataRequest(BaseModel):
    """Ingest historical data request."""
    symbols: List[str] = Field(description="Symbols to ingest")
    start_date: datetime = Field(description="Start date")
    end_date: datetime = Field(description="End date")
    timeframe: str = Field(default="1", description="Timeframe (1, 5, 15, 30, 60, D)")
    force_update: bool = Field(default=False, description="Force update existing data")
    max_concurrent: int = Field(default=5, description="Maximum concurrent requests")


class IngestHistoricalDataResponse(BaseResponse):
    """Ingest historical data response."""
    ingestion_results: Dict[str, int]
    total_records: int
    execution_time: float
    failed_symbols: List[str] = []


class DataCoverageInfo(BaseModel):
    """Data coverage information."""
    symbol: str
    earliest_date: Optional[datetime] = None
    latest_date: Optional[datetime] = None
    total_records: int
    gaps: List[Dict[str, Any]] = []


class IngestionStatusResponse(BaseResponse):
    """Ingestion status response."""
    total_symbols: int
    active_symbols: int
    total_ohlcv_records: int
    last_update: Optional[datetime] = None
    data_coverage: Dict[str, DataCoverageInfo]


class DataQualityCheck(BaseModel):
    """Data quality check result."""
    symbol: str
    check_type: str
    status: str  # 'pass', 'warning', 'fail'
    message: str
    details: Optional[Dict[str, Any]] = None


class DataQualityResponse(BaseResponse):
    """Data quality response."""
    symbol: str
    overall_status: str
    checks: List[DataQualityCheck]
    recommendations: List[str] = []


class DataMaintenanceRequest(BaseModel):
    """Data maintenance request."""
    operation: str = Field(description="Maintenance operation (cleanup, reindex, vacuum)")
    symbols: Optional[List[str]] = Field(default=None, description="Specific symbols to maintain")
    older_than_days: Optional[int] = Field(default=None, description="Remove data older than N days")


class DataMaintenanceResponse(BaseResponse):
    """Data maintenance response."""
    operation: str
    records_affected: int
    execution_time: float
    details: Dict[str, Any]


class DataExportRequest(BaseModel):
    """Data export request."""
    symbols: List[str] = Field(description="Symbols to export")
    start_date: datetime = Field(description="Start date")
    end_date: datetime = Field(description="End date")
    format: str = Field(default="csv", description="Export format (csv, json, parquet)")
    include_metadata: bool = Field(default=True, description="Include metadata")


class DataExportResponse(BaseResponse):
    """Data export response."""
    export_id: str
    download_url: str
    file_size: int
    record_count: int
    expires_at: datetime


class DataImportRequest(BaseModel):
    """Data import request."""
    file_url: str = Field(description="URL of file to import")
    format: str = Field(description="File format (csv, json, parquet)")
    symbol_mapping: Optional[Dict[str, str]] = Field(default=None, description="Symbol name mapping")
    validation_mode: str = Field(default="strict", description="Validation mode (strict, lenient)")


class DataImportResponse(BaseResponse):
    """Data import response."""
    import_id: str
    records_imported: int
    records_skipped: int
    validation_errors: List[str] = []
    execution_time: float
