"""
Technical indicators API endpoints.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session

from app.api.dependencies import (
    get_data_service,
    validate_symbol,
    validate_timeframe
)
from app.api.schemas.indicators import (
    IndicatorRequest,
    MultipleIndicatorRequest,
    IndicatorResponse,
    MultipleIndicatorResponse,
    IndicatorListResponse,
    IndicatorInfoResponse,
    SignalsRequest,
    SignalsResponse,
    IndicatorValidationResponse
)
from app.services.data_service import DataService
from app.services.indicator_service import IndicatorService
from app.services.realtime_indicators import RealTimeIndicatorEngine, IndicatorWebSocketHandler
from app.database.connection import get_db
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()

# Create indicator service instance
indicator_service = IndicatorService()

# Global real-time indicator engine
realtime_engine = None
websocket_handler = None


@router.get("/list", response_model=IndicatorListResponse)
async def get_available_indicators():
    """Get list of available technical indicators."""
    try:
        indicators = indicator_service.get_available_indicators()
        return IndicatorListResponse(indicators=indicators)
        
    except Exception as e:
        logger.error(f"Error getting available indicators: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve available indicators"
        )


@router.get("/info/{indicator_name}", response_model=IndicatorInfoResponse)
async def get_indicator_info(indicator_name: str):
    """Get information about a specific indicator."""
    try:
        info = indicator_service.get_indicator_info(indicator_name)
        
        if not info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Indicator '{indicator_name}' not found"
            )
        
        return IndicatorInfoResponse(**info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting indicator info for {indicator_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve indicator information"
        )


@router.post("/calculate", response_model=IndicatorResponse)
async def calculate_indicator(
    request: IndicatorRequest,
    data_service: DataService = Depends(get_data_service)
):
    """Calculate a technical indicator for a symbol."""
    try:
        # Validate inputs
        symbol = validate_symbol(request.symbol)
        timeframe = validate_timeframe(request.timeframe)
        
        # Set default date range if not provided
        end_date = request.end_date or datetime.utcnow()
        start_date = request.start_date or (end_date - timedelta(days=30))
        
        # Get market data
        if timeframe == "1m":
            ohlcv_data = data_service.get_ohlcv_data(
                symbol=symbol,
                start_time=start_date,
                end_time=end_date,
                as_dataframe=True
            )
        else:
            ohlcv_data = data_service.get_aggregated_data(
                symbol=symbol,
                timeframe=timeframe,
                start_time=start_date,
                end_time=end_date
            )
        
        if ohlcv_data is None or len(ohlcv_data) == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No market data found for symbol {symbol}"
            )
        
        # Calculate indicator
        result = indicator_service.calculate_indicator(
            indicator_name=request.indicator,
            data=ohlcv_data,
            parameters=request.parameters
        )
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to calculate indicator '{request.indicator}'"
            )
        
        return IndicatorResponse(
            symbol=symbol,
            indicator=request.indicator,
            timeframe=timeframe,
            parameters=request.parameters or {},
            data=result,
            data_points=len(result.get('values', result.get('timestamps', [])))
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating indicator {request.indicator} for {request.symbol}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to calculate indicator"
        )


@router.post("/calculate-multiple", response_model=MultipleIndicatorResponse)
async def calculate_multiple_indicators(
    request: MultipleIndicatorRequest,
    data_service: DataService = Depends(get_data_service)
):
    """Calculate multiple technical indicators for a symbol."""
    try:
        # Validate inputs
        symbol = validate_symbol(request.symbol)
        timeframe = validate_timeframe(request.timeframe)
        
        if not request.indicators:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least one indicator must be specified"
            )
        
        # Set default date range if not provided
        end_date = request.end_date or datetime.utcnow()
        start_date = request.start_date or (end_date - timedelta(days=30))
        
        # Get market data
        if timeframe == "1m":
            ohlcv_data = data_service.get_ohlcv_data(
                symbol=symbol,
                start_time=start_date,
                end_time=end_date,
                as_dataframe=True
            )
        else:
            ohlcv_data = data_service.get_aggregated_data(
                symbol=symbol,
                timeframe=timeframe,
                start_time=start_date,
                end_time=end_date
            )
        
        if ohlcv_data is None or len(ohlcv_data) == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No market data found for symbol {symbol}"
            )
        
        # Calculate multiple indicators
        results = indicator_service.calculate_multiple_indicators(
            indicators_config=request.indicators,
            data=ohlcv_data
        )
        
        if not results:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to calculate any indicators"
            )
        
        # Determine data points count
        data_points = 0
        for result in results.values():
            if 'values' in result:
                data_points = max(data_points, len(result['values']))
            elif 'timestamps' in result:
                data_points = max(data_points, len(result['timestamps']))
        
        return MultipleIndicatorResponse(
            symbol=symbol,
            timeframe=timeframe,
            indicators=results,
            data_points=data_points
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating multiple indicators for {request.symbol}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to calculate indicators"
        )


@router.post("/signals", response_model=SignalsResponse)
async def get_trading_signals(
    request: SignalsRequest,
    data_service: DataService = Depends(get_data_service)
):
    """Get trading signals from a technical indicator."""
    try:
        # Validate inputs
        symbol = validate_symbol(request.symbol)
        timeframe = validate_timeframe(request.timeframe)
        
        # Set default date range if not provided
        end_date = request.end_date or datetime.utcnow()
        start_date = request.start_date or (end_date - timedelta(days=30))
        
        # Get market data
        if timeframe == "1m":
            ohlcv_data = data_service.get_ohlcv_data(
                symbol=symbol,
                start_time=start_date,
                end_time=end_date,
                as_dataframe=True
            )
        else:
            ohlcv_data = data_service.get_aggregated_data(
                symbol=symbol,
                timeframe=timeframe,
                start_time=start_date,
                end_time=end_date
            )
        
        if ohlcv_data is None or len(ohlcv_data) == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No market data found for symbol {symbol}"
            )
        
        # Get trading signals
        signals = indicator_service.get_indicator_signals(
            indicator_name=request.indicator,
            data=ohlcv_data,
            parameters=request.parameters
        )
        
        if not signals:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to generate signals from indicator '{request.indicator}'"
            )
        
        # Get timestamps from data
        if hasattr(ohlcv_data, 'timestamp'):
            timestamps = ohlcv_data['timestamp'].tolist()
        else:
            timestamps = [record.timestamp for record in ohlcv_data]
        
        # Align timestamps with signals
        signal_length = len(signals['buy_signals'])
        if len(timestamps) > signal_length:
            timestamps = timestamps[-signal_length:]
        
        # Count signals
        signal_count = {
            'buy_signals': sum(signals['buy_signals']),
            'sell_signals': sum(signals['sell_signals'])
        }
        
        return SignalsResponse(
            symbol=symbol,
            indicator=request.indicator,
            timeframe=timeframe,
            buy_signals=signals['buy_signals'],
            sell_signals=signals['sell_signals'],
            timestamps=timestamps,
            signal_count=signal_count
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting signals from {request.indicator} for {request.symbol}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate trading signals"
        )


@router.post("/validate", response_model=IndicatorValidationResponse)
async def validate_indicator_data(
    symbol: str = Query(...),
    timeframe: str = Query(default="1m"),
    start_date: Optional[datetime] = Query(default=None),
    end_date: Optional[datetime] = Query(default=None),
    data_service: DataService = Depends(get_data_service)
):
    """Validate data availability for indicator calculations."""
    try:
        # Validate inputs
        symbol = validate_symbol(symbol)
        timeframe = validate_timeframe(timeframe)
        
        errors = []
        
        # Set default date range if not provided
        end_date = end_date or datetime.utcnow()
        start_date = start_date or (end_date - timedelta(days=30))
        
        # Check data availability
        if timeframe == "1m":
            ohlcv_data = data_service.get_ohlcv_data(
                symbol=symbol,
                start_time=start_date,
                end_time=end_date,
                as_dataframe=True
            )
        else:
            ohlcv_data = data_service.get_aggregated_data(
                symbol=symbol,
                timeframe=timeframe,
                start_time=start_date,
                end_time=end_date
            )
        
        if ohlcv_data is None:
            errors.append(f"No data found for symbol {symbol}")
        elif len(ohlcv_data) == 0:
            errors.append(f"Empty dataset for symbol {symbol}")
        elif len(ohlcv_data) < 20:
            errors.append(f"Insufficient data points ({len(ohlcv_data)}) for reliable indicator calculations")
        
        # Validate data format
        if ohlcv_data is not None and len(ohlcv_data) > 0:
            is_valid_format = indicator_service.validate_data(ohlcv_data)
            if not is_valid_format:
                errors.append("Invalid data format for indicator calculations")
        
        return IndicatorValidationResponse(
            is_valid=len(errors) == 0,
            errors=errors
        )
        
    except Exception as e:
        logger.error(f"Error validating indicator data for {symbol}: {e}")
        return IndicatorValidationResponse(
            is_valid=False,
            errors=[f"Validation error: {str(e)}"]
        )


def get_realtime_engine(db: Session = Depends(get_db)) -> RealTimeIndicatorEngine:
    """Get or create real-time indicator engine."""
    global realtime_engine, websocket_handler

    if realtime_engine is None:
        realtime_engine = RealTimeIndicatorEngine(db)
        websocket_handler = IndicatorWebSocketHandler(realtime_engine)

    return realtime_engine


@router.post("/realtime/add-symbol")
async def add_realtime_symbol(
    symbol: str,
    indicators: List[Dict[str, Any]],
    engine: RealTimeIndicatorEngine = Depends(get_realtime_engine)
):
    """Add a symbol for real-time indicator tracking."""
    try:
        symbol = validate_symbol(symbol)

        success = engine.add_symbol(symbol, indicators)

        if success:
            return {"message": f"Symbol {symbol} added for real-time tracking", "symbol": symbol}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to add symbol {symbol}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding real-time symbol: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add symbol for real-time tracking"
        )


@router.get("/realtime/symbols")
async def get_realtime_symbols(
    engine: RealTimeIndicatorEngine = Depends(get_realtime_engine)
):
    """Get list of symbols being tracked in real-time."""
    try:
        symbols = engine.get_active_symbols()
        return {"symbols": symbols, "count": len(symbols)}

    except Exception as e:
        logger.error(f"Error getting real-time symbols: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get real-time symbols"
        )


@router.get("/realtime/values/{symbol}")
async def get_realtime_values(
    symbol: str,
    engine: RealTimeIndicatorEngine = Depends(get_realtime_engine)
):
    """Get current real-time indicator values for a symbol."""
    try:
        symbol = validate_symbol(symbol)

        values = engine.get_current_values(symbol)

        if not values:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No real-time data found for symbol {symbol}"
            )

        return {"symbol": symbol, "indicators": values}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting real-time values: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get real-time indicator values"
        )
