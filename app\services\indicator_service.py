"""
Technical indicator service for managing and calculating indicators.
"""

from typing import Dict, Any, List, Optional, Union, Type
from datetime import datetime
import pandas as pd

from app.core.logging import get_logger
from app.services.indicators import (
    BaseIndicator, SMA, EMA, MACD, ADX,
    RSI, Stochastic, Williams_R, CCI,
    BollingerBands, ATR, Keltner,
    OBV, VWAP, MFI,
    PivotPoints, FibonacciRetracements
)

logger = get_logger(__name__)


class IndicatorService:
    """Service for managing technical indicators."""
    
    # Registry of available indicators
    INDICATORS = {
        'SMA': SMA,
        'EMA': EMA,
        'MACD': MACD,
        'ADX': ADX,
        'RSI': RSI,
        'Stochastic': Stochastic,
        'Williams_R': Williams_R,
        'CCI': CCI,
        'BollingerBands': BollingerBands,
        'ATR': ATR,
        'Keltner': Keltner,
        'OBV': OBV,
        'VWAP': VWAP,
        'MFI': MFI,
        'PivotPoints': PivotPoints,
        'FibonacciRetracements': FibonacciRetracements
    }
    
    def __init__(self):
        """Initialize indicator service."""
        self.active_indicators: Dict[str, BaseIndicator] = {}
    
    def get_available_indicators(self) -> List[str]:
        """Get list of available indicators."""
        return list(self.INDICATORS.keys())
    
    def create_indicator(
        self,
        indicator_name: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> Optional[BaseIndicator]:
        """
        Create an indicator instance.
        
        Args:
            indicator_name: Name of the indicator
            parameters: Indicator parameters
            
        Returns:
            Indicator instance if successful, None otherwise
        """
        try:
            if indicator_name not in self.INDICATORS:
                logger.error(f"Unknown indicator: {indicator_name}")
                return None
            
            indicator_class = self.INDICATORS[indicator_name]
            
            # Create indicator with parameters
            if parameters:
                indicator = indicator_class(**parameters)
            else:
                indicator = indicator_class()
            
            return indicator
            
        except Exception as e:
            logger.error(f"Error creating indicator {indicator_name}: {e}")
            return None
    
    def calculate_indicator(
        self,
        indicator_name: str,
        data: Union[pd.DataFrame, List[Dict[str, Any]]],
        parameters: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Calculate indicator values.
        
        Args:
            indicator_name: Name of the indicator
            data: OHLCV data
            parameters: Indicator parameters
            
        Returns:
            Calculated indicator values
        """
        try:
            indicator = self.create_indicator(indicator_name, parameters)
            if not indicator:
                return None
            
            return indicator.calculate(data)
            
        except Exception as e:
            logger.error(f"Error calculating indicator {indicator_name}: {e}")
            return None
    
    def calculate_multiple_indicators(
        self,
        indicators_config: List[Dict[str, Any]],
        data: Union[pd.DataFrame, List[Dict[str, Any]]]
    ) -> Dict[str, Any]:
        """
        Calculate multiple indicators.
        
        Args:
            indicators_config: List of indicator configurations
                Each config should have 'name' and optional 'parameters'
            data: OHLCV data
            
        Returns:
            Dictionary with all calculated indicators
        """
        results = {}
        
        for config in indicators_config:
            indicator_name = config.get('name')
            parameters = config.get('parameters', {})
            
            if not indicator_name:
                logger.warning("Indicator config missing 'name' field")
                continue
            
            result = self.calculate_indicator(indicator_name, data, parameters)
            if result:
                results[indicator_name] = result
            else:
                logger.warning(f"Failed to calculate indicator: {indicator_name}")
        
        return results
    
    def get_indicator_signals(
        self,
        indicator_name: str,
        data: Union[pd.DataFrame, List[Dict[str, Any]]],
        parameters: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, List[bool]]]:
        """
        Get trading signals from an indicator.
        
        Args:
            indicator_name: Name of the indicator
            data: OHLCV data
            parameters: Indicator parameters
            
        Returns:
            Trading signals (buy/sell)
        """
        try:
            indicator = self.create_indicator(indicator_name, parameters)
            if not indicator:
                return None
            
            # Prepare data as DataFrame
            if isinstance(data, list):
                df = pd.DataFrame(data)
            else:
                df = data.copy()
            
            return indicator.get_signals(df)
            
        except Exception as e:
            logger.error(f"Error getting signals from {indicator_name}: {e}")
            return None
    
    def register_indicator(self, name: str, indicator_class: Type[BaseIndicator]) -> bool:
        """
        Register a custom indicator.
        
        Args:
            name: Indicator name
            indicator_class: Indicator class
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not issubclass(indicator_class, BaseIndicator):
                logger.error(f"Indicator class must inherit from BaseIndicator")
                return False
            
            self.INDICATORS[name] = indicator_class
            logger.info(f"Registered custom indicator: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Error registering indicator {name}: {e}")
            return False
    
    def get_indicator_info(self, indicator_name: str) -> Optional[Dict[str, Any]]:
        """
        Get information about an indicator.
        
        Args:
            indicator_name: Name of the indicator
            
        Returns:
            Indicator information
        """
        if indicator_name not in self.INDICATORS:
            return None
        
        indicator_class = self.INDICATORS[indicator_name]
        
        # Create a temporary instance to get default parameters
        try:
            temp_indicator = indicator_class()
            return {
                'name': indicator_name,
                'class_name': indicator_class.__name__,
                'default_parameters': temp_indicator.parameters,
                'description': indicator_class.__doc__ or "No description available"
            }
        except Exception as e:
            logger.error(f"Error getting info for {indicator_name}: {e}")
            return {
                'name': indicator_name,
                'class_name': indicator_class.__name__,
                'default_parameters': {},
                'description': indicator_class.__doc__ or "No description available"
            }
    
    def validate_data(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> bool:
        """
        Validate OHLCV data format.
        
        Args:
            data: Data to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            if isinstance(data, list):
                if not data:
                    return False
                
                required_fields = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
                first_record = data[0]
                
                for field in required_fields:
                    if field not in first_record:
                        logger.error(f"Missing required field: {field}")
                        return False
                
            elif isinstance(data, pd.DataFrame):
                required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
                
                for col in required_columns:
                    if col not in data.columns:
                        logger.error(f"Missing required column: {col}")
                        return False
                
                if len(data) == 0:
                    logger.error("Empty DataFrame")
                    return False
            else:
                logger.error("Data must be a list of dictionaries or pandas DataFrame")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating data: {e}")
            return False
