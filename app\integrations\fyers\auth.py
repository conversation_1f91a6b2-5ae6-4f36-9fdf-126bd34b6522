"""
Fyers API authentication module.
"""

import os
import sys
import time
import subprocess
import webbrowser
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class FyersAuth:
    """Fyers API authentication handler."""

    # Browser configurations (prioritize Microsoft Edge)
    WINDOWS_BROWSERS = [
        (r'C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe', '--inprivate'),
        (r'C:\Program Files\Google Chrome\Application\chrome.exe', '--incognito'),
        (r'C:\Users\<USER>\AppData\Local\Programs\Opera\opera.exe', '--private'),
        (r'C:\Program Files\Opera\opera.exe', '--private'),
        (r'C:\Program Files (x86)\Opera\opera.exe', '--private')
    ]

    UNIX_BROWSERS = [
        ('/usr/bin/microsoft-edge', '--inprivate'),
        ('/usr/bin/google-chrome', '--incognito'),
        ('/usr/bin/firefox', '--private-window'),
        ('/usr/bin/opera', '--private')
    ]

    def __init__(self, auth_dir: Optional[str] = None):
        """
        Initialize Fyers authentication.

        Args:
            auth_dir: Directory to store authentication files
        """
        self.auth_dir = Path(auth_dir or "auth")
        self.auth_dir.mkdir(exist_ok=True)

        self.client_id_file = self.auth_dir / "fyers_client_id.txt"
        self.access_token_file = self.auth_dir / "fyers_access_token.txt"

        # Configuration from environment variables (manual approach)
        self.config = {
            "client_id": os.getenv('FYERS_CLIENT_ID', settings.fyers.client_id),
            "secret_key": os.getenv('FYERS_SECRET_KEY', settings.fyers.secret_key),
            "redirect_uri": os.getenv('FYERS_REDIRECT_URI', settings.fyers.redirect_uri).strip("'\""),
            "grant_type": "authorization_code",
            "response_type": "code",
            "state": "signal_stack"
        }
    
    def authenticate(self) -> Optional[str]:
        """
        Authenticate with Fyers API and return access token.

        Returns:
            Access token if successful, None otherwise
        """
        try:
            # First check if we have existing valid tokens from today
            if os.path.exists(self.access_token_file) and os.path.exists(self.client_id_file):
                token_mtime = datetime.fromtimestamp(os.path.getmtime(self.access_token_file))
                if token_mtime.date() == datetime.now().date():
                    with open(self.access_token_file, 'r') as f:
                        access_token = f.read().strip()

                    if self._verify_connection(access_token):
                        logger.info("Using existing valid access token")
                        return access_token
                    else:
                        logger.warning("Existing tokens are invalid, will get new ones")

            logger.info("Starting Fyers authentication process...")

            # Get new token using fyers_apiv3
            from fyers_apiv3 import fyersModel
            appSession = fyersModel.SessionModel(
                client_id=self.config["client_id"],
                redirect_uri=self.config["redirect_uri"],
                response_type=self.config["response_type"],
                state=self.config["state"],
                secret_key=self.config["secret_key"],
                grant_type=self.config["grant_type"]
            )

            generateTokenUrl = appSession.generate_authcode()
            logger.info(f"Opening browser for authentication: {generateTokenUrl}")
            self._open_browser(generateTokenUrl)
            auth_code = self._get_auth_code()
            if not auth_code:
                return None

            appSession.set_token(auth_code)
            token_response = appSession.generate_token()

            if not token_response or not token_response.get("access_token"):
                logger.error(f"Failed to generate access token: {token_response}")
                return None

            access_token = token_response["access_token"]

            self._save_tokens(self.config["client_id"], access_token)

            if self._verify_connection(access_token):
                return access_token
            else:
                return None

        except Exception as e:
            logger.error(f"Error during Fyers authentication: {str(e)}")
            return None
    
    def _open_browser(self, url):
        """Open browser in private/incognito mode"""
        try:
            if sys.platform.startswith('win'):
                self._open_windows_browser(url)
            else:
                self._open_unix_browser(url)
        except Exception as e:
            logger.warning(f"Failed to open browser in private mode: {str(e)}")
            webbrowser.open(url, new=2)

    def _open_windows_browser(self, url):
        """Open browser in Windows"""
        username = os.getenv('USERNAME')
        browsers = [(path.replace('%USERNAME%', username), flag)
                   if '%USERNAME%' in path else (path, flag)
                   for path, flag in self.WINDOWS_BROWSERS]

        for browser_path, private_flag in browsers:
            if os.path.exists(browser_path):
                subprocess.Popen([browser_path, private_flag, url])
                return True
        return False

    def _open_unix_browser(self, url):
        """Open browser in Unix-like systems"""
        for browser_path, private_flag in self.UNIX_BROWSERS:
            if os.path.exists(browser_path):
                subprocess.Popen([browser_path, private_flag, url])
                return True
        return False
    
    def _verify_connection(self, access_token):
        """Verify Fyers connection with access token"""
        try:
            from fyers_apiv3.fyersModel import FyersModel

            # Create API instance
            fyers = FyersModel(
                client_id=self.config["client_id"],
                is_async=False,
                token=access_token
            )

            # Test the connection
            profile = fyers.get_profile()
            if profile and profile.get("code") == 200:
                logger.info("Fyers authentication successful!")
                return True
            else:
                logger.error(f"Failed to verify Fyers connection: {profile}")
                return False

        except Exception as e:
            logger.error(f"Error verifying connection: {str(e)}")
            return False
    
    def _save_tokens(self, client_id, access_token):
        """Save authentication tokens to files"""
        with open(self.client_id_file, 'w') as file:
            file.write(client_id)
        with open(self.access_token_file, 'w') as file:
            file.write(access_token)
        logger.info(f"Authentication files saved to {self.auth_dir}")
    
    def _get_auth_code(self):
        """Get authentication code from user"""
        logger.info("\n=== Fyers API Authentication ===")
        logger.info("A browser window will open for you to log in to Fyers.")
        logger.info("After logging in, you will be redirected to Google.")
        logger.info("Copy the auth code from the URL and paste it here.")
        logger.info("\nPlease login in the private browser window that opened.")
        time.sleep(2)

        try:
            auth_code = input("\nEnter Auth Code from the redirected URL: ")
            if not auth_code:
                logger.error("No auth code provided")
                return None
            return auth_code
        except KeyboardInterrupt:
            logger.info("Authentication cancelled by user")
            return None
        except Exception as e:
            logger.error(f"Failed to get authorization code: {e}")
            return None
    
    def get_stored_token(self) -> Optional[str]:
        """Get stored access token without verification."""
        try:
            if self.access_token_file.exists():
                with open(self.access_token_file, 'r') as f:
                    return f.read().strip()
        except Exception as e:
            logger.error(f"Failed to read stored token: {e}")
        return None
    
    def clear_tokens(self) -> None:
        """Clear stored tokens."""
        try:
            if self.access_token_file.exists():
                self.access_token_file.unlink()
            if self.client_id_file.exists():
                self.client_id_file.unlink()
            logger.info("Tokens cleared successfully")
        except Exception as e:
            logger.error(f"Failed to clear tokens: {e}")
