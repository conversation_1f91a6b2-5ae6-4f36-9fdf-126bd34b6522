"""
Risk management module for paper trading.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import math

from app.core.logging import get_logger
from app.services.paper_trading.orders import OrderSide
from app.services.paper_trading.portfolio import PaperPortfolio

logger = get_logger(__name__)


class RiskLevel(Enum):
    """Risk levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"


@dataclass
class RiskLimits:
    """Risk limits configuration."""
    # Position limits
    max_position_size: float = 0.1  # 10% of portfolio per position
    max_portfolio_exposure: float = 0.8  # 80% maximum exposure
    max_sector_exposure: float = 0.3  # 30% per sector
    
    # Loss limits
    max_daily_loss: float = 0.05  # 5% daily loss limit
    max_drawdown: float = 0.2  # 20% maximum drawdown
    stop_loss_percentage: float = 0.1  # 10% stop loss
    
    # Leverage limits
    max_leverage: float = 1.0  # No leverage by default
    margin_requirement: float = 0.2  # 20% margin requirement
    
    # Trading limits
    max_orders_per_day: int = 100
    max_trades_per_symbol: int = 10
    min_time_between_trades: int = 60  # seconds
    
    # Volatility limits
    max_volatility_threshold: float = 0.5  # 50% annualized volatility
    correlation_limit: float = 0.8  # Maximum correlation between positions


@dataclass
class RiskMetrics:
    """Current risk metrics."""
    portfolio_exposure: float = 0.0
    daily_pnl: float = 0.0
    current_drawdown: float = 0.0
    portfolio_volatility: float = 0.0
    var_95: float = 0.0  # Value at Risk (95%)
    beta: float = 0.0
    sharpe_ratio: float = 0.0
    risk_level: RiskLevel = RiskLevel.LOW


class RiskManager:
    """Risk management system for paper trading."""
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize risk manager."""
        self.config = config or {}
        self.limits = RiskLimits(**self.config.get('limits', {}))
        
        # Risk tracking
        self.daily_trades: Dict[str, int] = {}  # symbol -> trade count
        self.last_trade_time: Dict[str, datetime] = {}  # symbol -> last trade time
        self.daily_pnl_history: List[float] = []
        self.portfolio_values: List[float] = []
        
        # Risk alerts
        self.active_alerts: List[Dict[str, Any]] = []
        
        # State
        self.trading_halted = False
        self.halt_reason = None
        
    def validate_order(
        self,
        symbol: str,
        side: OrderSide,
        quantity: float,
        price: float,
        portfolio: PaperPortfolio
    ) -> bool:
        """
        Validate an order against risk limits.
        
        Args:
            symbol: Symbol to trade
            side: Order side
            quantity: Order quantity
            price: Order price
            portfolio: Current portfolio
            
        Returns:
            True if order is allowed, False otherwise
        """
        try:
            # Check if trading is halted
            if self.trading_halted:
                logger.warning(f"Trading halted: {self.halt_reason}")
                return False
            
            # Check position size limit
            if not self._check_position_size_limit(symbol, side, quantity, price, portfolio):
                return False
            
            # Check portfolio exposure limit
            if not self._check_portfolio_exposure_limit(symbol, side, quantity, price, portfolio):
                return False
            
            # Check daily trading limits
            if not self._check_daily_trading_limits(symbol):
                return False
            
            # Check time between trades
            if not self._check_time_between_trades(symbol):
                return False
            
            # Check daily loss limit
            if not self._check_daily_loss_limit(portfolio):
                return False
            
            # Check drawdown limit
            if not self._check_drawdown_limit(portfolio):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating order: {e}")
            return False
    
    def _check_position_size_limit(
        self,
        symbol: str,
        side: OrderSide,
        quantity: float,
        price: float,
        portfolio: PaperPortfolio
    ) -> bool:
        """Check position size limit."""
        order_value = quantity * price
        portfolio_value = portfolio.total_value
        
        # Calculate new position size
        current_position = portfolio.get_position(symbol)
        current_value = current_position.market_value if current_position else 0
        
        if side == OrderSide.BUY:
            new_position_value = current_value + order_value
        else:
            new_position_value = max(0, current_value - order_value)
        
        position_percentage = new_position_value / portfolio_value
        
        if position_percentage > self.limits.max_position_size:
            logger.warning(f"Position size limit exceeded for {symbol}: {position_percentage:.2%} > {self.limits.max_position_size:.2%}")
            self._add_alert("position_size_limit", f"Position size limit exceeded for {symbol}")
            return False
        
        return True
    
    def _check_portfolio_exposure_limit(
        self,
        symbol: str,
        side: OrderSide,
        quantity: float,
        price: float,
        portfolio: PaperPortfolio
    ) -> bool:
        """Check portfolio exposure limit."""
        order_value = quantity * price
        portfolio_value = portfolio.total_value
        
        # Calculate total exposure after order
        current_exposure = sum(pos.market_value for pos in portfolio.positions.values())
        
        if side == OrderSide.BUY:
            new_exposure = current_exposure + order_value
        else:
            new_exposure = max(0, current_exposure - order_value)
        
        exposure_percentage = new_exposure / portfolio_value
        
        if exposure_percentage > self.limits.max_portfolio_exposure:
            logger.warning(f"Portfolio exposure limit exceeded: {exposure_percentage:.2%} > {self.limits.max_portfolio_exposure:.2%}")
            self._add_alert("portfolio_exposure_limit", "Portfolio exposure limit exceeded")
            return False
        
        return True
    
    def _check_daily_trading_limits(self, symbol: str) -> bool:
        """Check daily trading limits."""
        today = datetime.now().date()
        
        # Reset daily counters if new day
        if symbol in self.daily_trades:
            # Check if we need to reset (simplified - in production, use proper date handling)
            pass
        
        # Initialize if not exists
        if symbol not in self.daily_trades:
            self.daily_trades[symbol] = 0
        
        # Check symbol-specific limit
        if self.daily_trades[symbol] >= self.limits.max_trades_per_symbol:
            logger.warning(f"Daily trade limit exceeded for {symbol}: {self.daily_trades[symbol]}")
            self._add_alert("daily_trade_limit", f"Daily trade limit exceeded for {symbol}")
            return False
        
        # Check total daily orders
        total_daily_trades = sum(self.daily_trades.values())
        if total_daily_trades >= self.limits.max_orders_per_day:
            logger.warning(f"Daily order limit exceeded: {total_daily_trades}")
            self._add_alert("daily_order_limit", "Daily order limit exceeded")
            return False
        
        return True
    
    def _check_time_between_trades(self, symbol: str) -> bool:
        """Check minimum time between trades."""
        if symbol in self.last_trade_time:
            time_since_last = (datetime.now() - self.last_trade_time[symbol]).total_seconds()
            if time_since_last < self.limits.min_time_between_trades:
                logger.warning(f"Minimum time between trades not met for {symbol}: {time_since_last}s")
                return False
        
        return True
    
    def _check_daily_loss_limit(self, portfolio: PaperPortfolio) -> bool:
        """Check daily loss limit."""
        if len(self.portfolio_values) == 0:
            return True
        
        # Calculate daily PnL
        start_of_day_value = self.portfolio_values[0]  # Simplified
        current_value = portfolio.total_value
        daily_pnl = (current_value - start_of_day_value) / start_of_day_value
        
        if daily_pnl < -self.limits.max_daily_loss:
            logger.warning(f"Daily loss limit exceeded: {daily_pnl:.2%}")
            self._add_alert("daily_loss_limit", f"Daily loss limit exceeded: {daily_pnl:.2%}")
            self._halt_trading("Daily loss limit exceeded")
            return False
        
        return True
    
    def _check_drawdown_limit(self, portfolio: PaperPortfolio) -> bool:
        """Check maximum drawdown limit."""
        if len(self.portfolio_values) < 2:
            return True
        
        # Calculate current drawdown
        peak_value = max(self.portfolio_values)
        current_value = portfolio.total_value
        drawdown = (peak_value - current_value) / peak_value
        
        if drawdown > self.limits.max_drawdown:
            logger.warning(f"Maximum drawdown exceeded: {drawdown:.2%}")
            self._add_alert("max_drawdown", f"Maximum drawdown exceeded: {drawdown:.2%}")
            self._halt_trading("Maximum drawdown exceeded")
            return False
        
        return True
    
    def update_portfolio_value(self, portfolio_value: float):
        """Update portfolio value for risk calculations."""
        self.portfolio_values.append(portfolio_value)
        
        # Keep only last 1000 values
        if len(self.portfolio_values) > 1000:
            self.portfolio_values = self.portfolio_values[-1000:]
    
    def record_trade(self, symbol: str):
        """Record a trade for risk tracking."""
        # Update daily trade count
        if symbol not in self.daily_trades:
            self.daily_trades[symbol] = 0
        self.daily_trades[symbol] += 1
        
        # Update last trade time
        self.last_trade_time[symbol] = datetime.now()
    
    def calculate_risk_metrics(self, portfolio: PaperPortfolio) -> RiskMetrics:
        """Calculate current risk metrics."""
        metrics = RiskMetrics()
        
        try:
            # Portfolio exposure
            portfolio_value = portfolio.total_value
            total_exposure = sum(pos.market_value for pos in portfolio.positions.values())
            metrics.portfolio_exposure = total_exposure / portfolio_value if portfolio_value > 0 else 0
            
            # Daily PnL
            if len(self.portfolio_values) >= 2:
                yesterday_value = self.portfolio_values[-2]
                today_value = self.portfolio_values[-1]
                metrics.daily_pnl = (today_value - yesterday_value) / yesterday_value
            
            # Current drawdown
            if len(self.portfolio_values) > 0:
                peak_value = max(self.portfolio_values)
                current_value = portfolio.total_value
                metrics.current_drawdown = (peak_value - current_value) / peak_value
            
            # Portfolio volatility (simplified)
            if len(self.portfolio_values) >= 20:
                returns = []
                for i in range(1, len(self.portfolio_values)):
                    ret = (self.portfolio_values[i] - self.portfolio_values[i-1]) / self.portfolio_values[i-1]
                    returns.append(ret)
                
                if returns:
                    import numpy as np
                    metrics.portfolio_volatility = np.std(returns) * math.sqrt(252)  # Annualized
            
            # Risk level assessment
            metrics.risk_level = self._assess_risk_level(metrics)
            
        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
        
        return metrics
    
    def _assess_risk_level(self, metrics: RiskMetrics) -> RiskLevel:
        """Assess overall risk level."""
        risk_score = 0
        
        # Exposure risk
        if metrics.portfolio_exposure > 0.8:
            risk_score += 3
        elif metrics.portfolio_exposure > 0.6:
            risk_score += 2
        elif metrics.portfolio_exposure > 0.4:
            risk_score += 1
        
        # Drawdown risk
        if metrics.current_drawdown > 0.15:
            risk_score += 3
        elif metrics.current_drawdown > 0.1:
            risk_score += 2
        elif metrics.current_drawdown > 0.05:
            risk_score += 1
        
        # Volatility risk
        if metrics.portfolio_volatility > 0.4:
            risk_score += 3
        elif metrics.portfolio_volatility > 0.3:
            risk_score += 2
        elif metrics.portfolio_volatility > 0.2:
            risk_score += 1
        
        # Daily loss risk
        if metrics.daily_pnl < -0.03:
            risk_score += 3
        elif metrics.daily_pnl < -0.02:
            risk_score += 2
        elif metrics.daily_pnl < -0.01:
            risk_score += 1
        
        # Determine risk level
        if risk_score >= 8:
            return RiskLevel.EXTREME
        elif risk_score >= 6:
            return RiskLevel.HIGH
        elif risk_score >= 3:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def _add_alert(self, alert_type: str, message: str):
        """Add a risk alert."""
        alert = {
            'type': alert_type,
            'message': message,
            'timestamp': datetime.now(),
            'acknowledged': False
        }
        self.active_alerts.append(alert)
        
        # Keep only last 100 alerts
        if len(self.active_alerts) > 100:
            self.active_alerts = self.active_alerts[-100:]
    
    def _halt_trading(self, reason: str):
        """Halt trading due to risk limits."""
        self.trading_halted = True
        self.halt_reason = reason
        logger.error(f"Trading halted: {reason}")
        self._add_alert("trading_halt", f"Trading halted: {reason}")
    
    def resume_trading(self):
        """Resume trading (manual override)."""
        self.trading_halted = False
        self.halt_reason = None
        logger.info("Trading resumed manually")
        self._add_alert("trading_resume", "Trading resumed manually")
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """Get risk management summary."""
        return {
            'trading_halted': self.trading_halted,
            'halt_reason': self.halt_reason,
            'active_alerts': len(self.active_alerts),
            'daily_trades': dict(self.daily_trades),
            'limits': {
                'max_position_size': self.limits.max_position_size,
                'max_portfolio_exposure': self.limits.max_portfolio_exposure,
                'max_daily_loss': self.limits.max_daily_loss,
                'max_drawdown': self.limits.max_drawdown,
                'max_orders_per_day': self.limits.max_orders_per_day
            }
        }
