2025-07-13 04:13:21,178 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.177208Z"}
2025-07-13 04:13:21,178 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.178217Z"}
2025-07-13 04:13:21,179 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.179218Z"}
2025-07-13 04:13:21,180 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.180227Z"}
2025-07-13 04:13:21,386 [INFO] app.database.connection: Database connection successful
2025-07-13 04:13:21,387 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.387353Z"}
2025-07-13 04:13:21,387 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.387353Z"}
2025-07-13 04:13:21,388 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:13:21,388 [INFO] app.database.connection: Initializing database...
2025-07-13 04:13:21,392 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:13:21,679 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:13:21,679 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:13:21,709 [ERROR] app.database.init_db: Failed to create hypertables: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'stock_ohlcv', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:13:21,710 [ERROR] app.database.init_db: Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'stock_ohlcv', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:13:21,711 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column \"timestamp\" (used in partitioning)\nHINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.\n\n[SQL: \n                SELECT create_hypertable(\n                    'stock_ohlcv', \n                    'timestamp',\n                    if_not_exists => TRUE,\n                    chunk_time_interval => INTERVAL '1 day'\n                );\n            ]\n(Background on this error at: https://sqlalche.me/e/20/4xp6)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:43:21.711311Z"}
2025-07-13 04:14:15,839 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:15.839084Z"}
2025-07-13 04:14:15,840 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:15.840051Z"}
2025-07-13 04:14:15,841 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:15.841048Z"}
2025-07-13 04:14:15,842 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:15.842058Z"}
2025-07-13 04:14:16,028 [INFO] app.database.connection: Database connection successful
2025-07-13 04:14:16,028 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:16.028458Z"}
2025-07-13 04:14:16,029 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:16.029458Z"}
2025-07-13 04:14:16,030 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:14:16,030 [INFO] app.database.connection: Initializing database...
2025-07-13 04:14:16,033 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:14:16,061 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:14:16,062 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:14:16,068 [ERROR] app.database.init_db: Failed to create hypertables: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'stock_ohlcv', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:14:16,069 [ERROR] app.database.init_db: Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'stock_ohlcv', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:14:16,070 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column \"timestamp\" (used in partitioning)\nHINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.\n\n[SQL: \n                SELECT create_hypertable(\n                    'stock_ohlcv', \n                    'timestamp',\n                    if_not_exists => TRUE,\n                    chunk_time_interval => INTERVAL '1 day'\n                );\n            ]\n(Background on this error at: https://sqlalche.me/e/20/4xp6)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:44:16.070339Z"}
2025-07-13 04:14:44,669 [INFO] signal_stack: {"event": "============================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.669528Z"}
2025-07-13 04:14:44,670 [INFO] signal_stack: {"event": "DATABASE RESET", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.670526Z"}
2025-07-13 04:14:44,670 [INFO] signal_stack: {"event": "============================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.670526Z"}
2025-07-13 04:14:44,671 [INFO] signal_stack: {"event": "Dropping all tables...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.671525Z"}
2025-07-13 04:14:44,863 [INFO] signal_stack: {"event": "Dropped table: stock_ohlcv", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.863536Z"}
2025-07-13 04:14:44,868 [INFO] signal_stack: {"event": "Dropped table: stock_ohlcv_agg", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.868536Z"}
2025-07-13 04:14:44,873 [INFO] signal_stack: {"event": "Dropped table: screener_results", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.872545Z"}
2025-07-13 04:14:44,877 [INFO] signal_stack: {"event": "Dropped table: paper_trades", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.877533Z"}
2025-07-13 04:14:44,881 [INFO] signal_stack: {"event": "Dropped table: backtest_trades", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.881535Z"}
2025-07-13 04:14:44,885 [INFO] signal_stack: {"event": "Dropped table: backtest_results", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.885549Z"}
2025-07-13 04:14:44,888 [INFO] signal_stack: {"event": "Dropped table: strategies", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.888542Z"}
2025-07-13 04:14:44,891 [INFO] signal_stack: {"event": "Dropped table: symbols", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.891536Z"}
2025-07-13 04:14:44,920 [INFO] signal_stack: {"event": "All tables dropped successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.920101Z"}
2025-07-13 04:14:44,921 [INFO] signal_stack: {"event": "Recreating all tables...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.921111Z"}
2025-07-13 04:14:45,148 [INFO] signal_stack: {"event": "All tables recreated successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:45.148443Z"}
2025-07-13 04:14:45,149 [INFO] signal_stack: {"event": "Database reset completed successfully!", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:45.149440Z"}
2025-07-13 04:14:55,569 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.569862Z"}
2025-07-13 04:14:55,570 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.570829Z"}
2025-07-13 04:14:55,571 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.571826Z"}
2025-07-13 04:14:55,571 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.571826Z"}
2025-07-13 04:14:55,767 [INFO] app.database.connection: Database connection successful
2025-07-13 04:14:55,768 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.768055Z"}
2025-07-13 04:14:55,769 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.768055Z"}
2025-07-13 04:14:55,769 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:14:55,769 [INFO] app.database.connection: Initializing database...
2025-07-13 04:14:55,773 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:14:55,797 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:14:55,797 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:14:55,823 [ERROR] app.database.init_db: Failed to create hypertables: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'screener_results', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:14:55,824 [ERROR] app.database.init_db: Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'screener_results', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:14:55,825 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column \"timestamp\" (used in partitioning)\nHINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.\n\n[SQL: \n                SELECT create_hypertable(\n                    'screener_results', \n                    'timestamp',\n                    if_not_exists => TRUE,\n                    chunk_time_interval => INTERVAL '1 day'\n                );\n            ]\n(Background on this error at: https://sqlalche.me/e/20/4xp6)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:44:55.825706Z"}
2025-07-13 04:15:42,336 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.336996Z"}
2025-07-13 04:15:42,338 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.338288Z"}
2025-07-13 04:15:42,338 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.338288Z"}
2025-07-13 04:15:42,339 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.339279Z"}
2025-07-13 04:15:42,530 [INFO] app.database.connection: Database connection successful
2025-07-13 04:15:42,530 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.530331Z"}
2025-07-13 04:15:42,531 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.531333Z"}
2025-07-13 04:15:42,531 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:15:42,531 [INFO] app.database.connection: Initializing database...
2025-07-13 04:15:42,535 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:15:42,562 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:15:42,563 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:15:42,592 [ERROR] app.database.init_db: Failed to create hypertables: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'screener_results', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:15:42,593 [ERROR] app.database.init_db: Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'screener_results', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:15:42,593 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column \"timestamp\" (used in partitioning)\nHINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.\n\n[SQL: \n                SELECT create_hypertable(\n                    'screener_results', \n                    'timestamp',\n                    if_not_exists => TRUE,\n                    chunk_time_interval => INTERVAL '1 day'\n                );\n            ]\n(Background on this error at: https://sqlalche.me/e/20/4xp6)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:45:42.593657Z"}
2025-07-13 04:18:44,872 [INFO] signal_stack: {"event": "============================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:44.872548Z"}
2025-07-13 04:18:44,873 [INFO] signal_stack: {"event": "DATABASE RESET", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:44.873530Z"}
2025-07-13 04:18:44,873 [INFO] signal_stack: {"event": "============================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:44.873530Z"}
2025-07-13 04:18:44,874 [INFO] signal_stack: {"event": "Dropping all tables...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:44.874925Z"}
2025-07-13 04:18:45,073 [INFO] signal_stack: {"event": "Dropped table: stock_ohlcv", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.073546Z"}
2025-07-13 04:18:45,077 [INFO] signal_stack: {"event": "Dropped table: stock_ohlcv_agg", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.077519Z"}
2025-07-13 04:18:45,082 [INFO] signal_stack: {"event": "Dropped table: screener_results", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.081917Z"}
2025-07-13 04:18:45,086 [INFO] signal_stack: {"event": "Dropped table: paper_trades", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.086914Z"}
2025-07-13 04:18:45,090 [INFO] signal_stack: {"event": "Dropped table: backtest_trades", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.090920Z"}
2025-07-13 04:18:45,094 [INFO] signal_stack: {"event": "Dropped table: backtest_results", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.094913Z"}
2025-07-13 04:18:45,098 [INFO] signal_stack: {"event": "Dropped table: strategies", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.097934Z"}
2025-07-13 04:18:45,100 [INFO] signal_stack: {"event": "Dropped table: symbols", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.100934Z"}
2025-07-13 04:18:45,139 [INFO] signal_stack: {"event": "All tables dropped successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.138165Z"}
2025-07-13 04:18:45,139 [INFO] signal_stack: {"event": "Recreating all tables...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.139161Z"}
2025-07-13 04:18:45,343 [INFO] signal_stack: {"event": "All tables recreated successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.343078Z"}
2025-07-13 04:18:45,343 [INFO] signal_stack: {"event": "Database reset completed successfully!", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.343078Z"}
2025-07-13 04:18:55,806 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:55.806433Z"}
2025-07-13 04:18:55,807 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:55.807433Z"}
2025-07-13 04:18:55,807 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:55.807433Z"}
2025-07-13 04:18:55,808 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:55.808433Z"}
2025-07-13 04:18:56,001 [INFO] app.database.connection: Database connection successful
2025-07-13 04:18:56,001 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:56.001413Z"}
2025-07-13 04:18:56,002 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:56.002426Z"}
2025-07-13 04:18:56,002 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:18:56,003 [INFO] app.database.connection: Initializing database...
2025-07-13 04:18:56,006 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:18:56,032 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:18:56,032 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:18:56,089 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:18:56,090 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:18:56,114 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:18:56,114 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:18:56,129 [ERROR] app.database.init_db: Failed to create stored procedures: (psycopg2.errors.SyntaxError) syntax error at or near "timestamp"
LINE 8:                 ) RETURNS TABLE(timestamp TIMESTAMP, sma NUM...
                                        ^

[SQL: 
                CREATE OR REPLACE FUNCTION calculate_sma(
                    p_symbol_id INTEGER,
                    p_timeframe VARCHAR(10),
                    p_period INTEGER,
                    p_start_time TIMESTAMP,
                    p_end_time TIMESTAMP
                ) RETURNS TABLE(timestamp TIMESTAMP, sma NUMERIC) AS $$
                BEGIN
                    RETURN QUERY
                    SELECT 
                        t.timestamp,
                        AVG(t.close) OVER (
                            ORDER BY t.timestamp 
                            ROWS BETWEEN p_period-1 PRECEDING AND CURRENT ROW
                        ) as sma
                    FROM stock_ohlcv_agg t
                    WHERE t.symbol_id = p_symbol_id
                        AND t.timeframe = p_timeframe
                        AND t.timestamp >= p_start_time
                        AND t.timestamp <= p_end_time
                    ORDER BY t.timestamp;
                END;
                $$ LANGUAGE plpgsql;
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-13 04:18:56,130 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.SyntaxError) syntax error at or near "timestamp"
LINE 8:                 ) RETURNS TABLE(timestamp TIMESTAMP, sma NUM...
                                        ^

[SQL: 
                CREATE OR REPLACE FUNCTION calculate_sma(
                    p_symbol_id INTEGER,
                    p_timeframe VARCHAR(10),
                    p_period INTEGER,
                    p_start_time TIMESTAMP,
                    p_end_time TIMESTAMP
                ) RETURNS TABLE(timestamp TIMESTAMP, sma NUMERIC) AS $$
                BEGIN
                    RETURN QUERY
                    SELECT 
                        t.timestamp,
                        AVG(t.close) OVER (
                            ORDER BY t.timestamp 
                            ROWS BETWEEN p_period-1 PRECEDING AND CURRENT ROW
                        ) as sma
                    FROM stock_ohlcv_agg t
                    WHERE t.symbol_id = p_symbol_id
                        AND t.timeframe = p_timeframe
                        AND t.timestamp >= p_start_time
                        AND t.timestamp <= p_end_time
                    ORDER BY t.timestamp;
                END;
                $$ LANGUAGE plpgsql;
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-13 04:18:56,132 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.SyntaxError) syntax error at or near \"timestamp\"\nLINE 8:                 ) RETURNS TABLE(timestamp TIMESTAMP, sma NUM...\n                                        ^\n\n[SQL: \n                CREATE OR REPLACE FUNCTION calculate_sma(\n                    p_symbol_id INTEGER,\n                    p_timeframe VARCHAR(10),\n                    p_period INTEGER,\n                    p_start_time TIMESTAMP,\n                    p_end_time TIMESTAMP\n                ) RETURNS TABLE(timestamp TIMESTAMP, sma NUMERIC) AS $$\n                BEGIN\n                    RETURN QUERY\n                    SELECT \n                        t.timestamp,\n                        AVG(t.close) OVER (\n                            ORDER BY t.timestamp \n                            ROWS BETWEEN p_period-1 PRECEDING AND CURRENT ROW\n                        ) as sma\n                    FROM stock_ohlcv_agg t\n                    WHERE t.symbol_id = p_symbol_id\n                        AND t.timeframe = p_timeframe\n                        AND t.timestamp >= p_start_time\n                        AND t.timestamp <= p_end_time\n                    ORDER BY t.timestamp;\n                END;\n                $$ LANGUAGE plpgsql;\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:48:56.132130Z"}
2025-07-13 04:19:24,086 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.086659Z"}
2025-07-13 04:19:24,087 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.087645Z"}
2025-07-13 04:19:24,087 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.087645Z"}
2025-07-13 04:19:24,088 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.088654Z"}
2025-07-13 04:19:24,273 [INFO] app.database.connection: Database connection successful
2025-07-13 04:19:24,274 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.274825Z"}
2025-07-13 04:19:24,274 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.274825Z"}
2025-07-13 04:19:24,274 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:19:24,275 [INFO] app.database.connection: Initializing database...
2025-07-13 04:19:24,278 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:19:24,304 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:19:24,305 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:19:24,313 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:19:24,314 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:19:24,318 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:19:24,319 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:19:24,327 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:19:24,328 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:19:24,328 [INFO] signal_stack: {"event": "\u2713 Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.328204Z"}
2025-07-13 04:19:24,329 [INFO] signal_stack: {"event": "\nStep 3: Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.329200Z"}
2025-07-13 04:19:24,330 [INFO] signal_stack: {"event": "Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.330192Z"}
2025-07-13 04:19:24,381 [INFO] signal_stack: {"event": "Creating NIFTY symbol in database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.381066Z"}
2025-07-13 04:19:24,382 [INFO] app.services.data_service: {"event": "Creating symbol: NIFTY", "logger": "app.services.data_service", "level": "info", "timestamp": "2025-07-12T22:49:24.382071Z"}
2025-07-13 04:19:24,396 [INFO] signal_stack: {"event": "\u2713 NIFTY symbol created successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.396381Z"}
2025-07-13 04:19:24,397 [INFO] signal_stack: {"event": "\nStep 4: Fetching historical data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.397376Z"}
2025-07-13 04:19:24,398 [INFO] signal_stack: {"event": "Fetching historical data for NIFTY...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.398379Z"}
2025-07-13 04:19:24,409 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T22:49:24.409655Z"}
2025-07-13 04:19:24,410 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:49:24.410656Z"}
2025-07-13 04:19:25,431 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:49:25.431944Z"}
2025-07-13 04:20:13,466 [ERROR] app.integrations.fyers.auth: {"event": "Token generation failed: {'code': -5, 'message': 'invalid app id hash', 's': 'error'}", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T22:50:13.466548Z"}
2025-07-13 04:20:13,467 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T22:50:13.467553Z"}
2025-07-13 04:20:13,467 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T22:50:13.467553Z"}
2025-07-13 04:20:13,468 [ERROR] signal_stack: {"event": "Failed to initialize Fyers connection", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:50:13.468552Z"}
2025-07-13 04:21:21,513 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.513032Z"}
2025-07-13 04:21:21,513 [INFO] signal_stack: {"event": "NIFTY PIPELINE TEST SETUP (WITHOUT FYERS)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.513032Z"}
2025-07-13 04:21:21,514 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.514036Z"}
2025-07-13 04:21:21,514 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.514036Z"}
2025-07-13 04:21:21,698 [INFO] app.database.connection: Database connection successful
2025-07-13 04:21:21,699 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.699575Z"}
2025-07-13 04:21:21,700 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.699575Z"}
2025-07-13 04:21:21,700 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:21:21,700 [INFO] app.database.connection: Initializing database...
2025-07-13 04:21:21,704 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:21:21,729 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:21:21,730 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:21:21,738 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:21:21,739 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:21:21,744 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:21:21,745 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:21:21,753 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:21:21,753 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:21:21,754 [INFO] signal_stack: {"event": "\u2713 Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.754556Z"}
2025-07-13 04:21:21,755 [INFO] signal_stack: {"event": "\nStep 3: Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.755569Z"}
2025-07-13 04:21:21,756 [INFO] signal_stack: {"event": "Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.755569Z"}
2025-07-13 04:21:21,807 [INFO] signal_stack: {"event": "\u2713 NIFTY symbol already exists", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.807933Z"}
2025-07-13 04:21:21,809 [INFO] signal_stack: {"event": "\nStep 4: Creating sample data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.809935Z"}
2025-07-13 04:21:21,809 [INFO] signal_stack: {"event": "Creating sample OHLCV data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.809935Z"}
2025-07-13 04:21:21,847 [ERROR] signal_stack: {"event": "Error creating sample data: DataService.store_ohlcv_data() missing 1 required positional argument: 'ohlcv_data'", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:51:21.847081Z"}
2025-07-13 04:22:15,002 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.002476Z"}
2025-07-13 04:22:15,003 [INFO] signal_stack: {"event": "NIFTY PIPELINE TEST SETUP (WITHOUT FYERS)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.003472Z"}
2025-07-13 04:22:15,004 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.004476Z"}
2025-07-13 04:22:15,004 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.004476Z"}
2025-07-13 04:22:15,217 [INFO] app.database.connection: Database connection successful
2025-07-13 04:22:15,217 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.217048Z"}
2025-07-13 04:22:15,218 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.218047Z"}
2025-07-13 04:22:15,219 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:22:15,219 [INFO] app.database.connection: Initializing database...
2025-07-13 04:22:15,222 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:22:15,255 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:22:15,255 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:22:15,264 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:22:15,265 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:22:15,270 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:22:15,270 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:22:15,279 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:22:15,280 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:22:15,280 [INFO] signal_stack: {"event": "\u2713 Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.280750Z"}
2025-07-13 04:22:15,280 [INFO] signal_stack: {"event": "\nStep 3: Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.280750Z"}
2025-07-13 04:22:15,281 [INFO] signal_stack: {"event": "Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.281746Z"}
2025-07-13 04:22:15,337 [INFO] signal_stack: {"event": "\u2713 NIFTY symbol already exists", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.337051Z"}
2025-07-13 04:22:15,338 [INFO] signal_stack: {"event": "\nStep 4: Creating sample data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.338061Z"}
2025-07-13 04:22:15,339 [INFO] signal_stack: {"event": "Creating sample OHLCV data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.339474Z"}
2025-07-13 04:22:15,597 [INFO] app.services.data_service: {"event": "Stored 782 OHLCV records for NIFTY", "logger": "app.services.data_service", "level": "info", "timestamp": "2025-07-12T22:52:15.596934Z"}
2025-07-13 04:22:15,597 [INFO] signal_stack: {"event": "\u2713 Created 782 sample OHLCV records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.597935Z"}
2025-07-13 04:22:15,598 [INFO] signal_stack: {"event": "\nStep 5: Testing timeframe aggregation...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.598936Z"}
2025-07-13 04:22:15,599 [INFO] signal_stack: {"event": "Testing timeframe aggregation...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.599935Z"}
2025-07-13 04:22:15,600 [INFO] signal_stack: {"event": "Aggregating NIFTY data for 5m...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.600938Z"}
2025-07-13 04:22:15,608 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 5m from 2025-07-10 04:22:15.600938 to 2025-07-13 04:22:15.600938", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:52:15.607280Z"}
2025-07-13 04:22:15,662 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:52:15.661120Z"}
2025-07-13 04:22:15,662 [INFO] signal_stack: {"event": "\u2713 5m aggregation successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.662121Z"}
2025-07-13 04:22:15,663 [INFO] signal_stack: {"event": "Aggregating NIFTY data for 15m...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.663103Z"}
2025-07-13 04:22:15,667 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 15m from 2025-07-10 04:22:15.663103 to 2025-07-13 04:22:15.663103", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:52:15.667130Z"}
2025-07-13 04:22:15,677 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:52:15.677488Z"}
2025-07-13 04:22:15,678 [INFO] signal_stack: {"event": "\u2713 15m aggregation successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.678522Z"}
2025-07-13 04:22:15,679 [INFO] signal_stack: {"event": "Aggregating NIFTY data for 30m...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.678522Z"}
2025-07-13 04:22:15,682 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 30m from 2025-07-10 04:22:15.679489 to 2025-07-13 04:22:15.679489", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:52:15.682848Z"}
2025-07-13 04:22:15,689 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:52:15.689830Z"}
2025-07-13 04:22:15,690 [INFO] signal_stack: {"event": "\u2713 30m aggregation successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.690832Z"}
2025-07-13 04:22:15,691 [INFO] signal_stack: {"event": "Aggregating NIFTY data for 1h...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.691838Z"}
2025-07-13 04:22:15,695 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 1h from 2025-07-10 04:22:15.691838 to 2025-07-13 04:22:15.691838", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:52:15.695147Z"}
2025-07-13 04:22:15,701 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:52:15.701445Z"}
2025-07-13 04:22:15,702 [INFO] signal_stack: {"event": "\u2713 1h aggregation successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.702477Z"}
2025-07-13 04:22:15,717 [INFO] signal_stack: {"event": "Aggregation statistics:", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.717868Z"}
2025-07-13 04:22:15,718 [INFO] signal_stack: {"event": "  5m_records: 158", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.718841Z"}
2025-07-13 04:22:15,719 [INFO] signal_stack: {"event": "  10m_records: 0", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.719875Z"}
2025-07-13 04:22:15,719 [INFO] signal_stack: {"event": "  15m_records: 54", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.719875Z"}
2025-07-13 04:22:15,720 [INFO] signal_stack: {"event": "  30m_records: 28", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.719875Z"}
2025-07-13 04:22:15,720 [INFO] signal_stack: {"event": "  1h_records: 14", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.720838Z"}
2025-07-13 04:22:15,720 [INFO] signal_stack: {"event": "  2h_records: 0", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.720838Z"}
2025-07-13 04:22:15,721 [INFO] signal_stack: {"event": "  4h_records: 0", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.721841Z"}
2025-07-13 04:22:15,721 [INFO] signal_stack: {"event": "  1d_records: 0", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.721841Z"}
2025-07-13 04:22:15,723 [INFO] signal_stack: {"event": "\nStep 6: Verifying data flow...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.723210Z"}
2025-07-13 04:22:15,724 [INFO] signal_stack: {"event": "Verifying data flow...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.724214Z"}
2025-07-13 04:22:15,747 [INFO] signal_stack: {"event": "\u2713 Latest 1-minute data:", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.746017Z"}
2025-07-13 04:22:15,749 [INFO] signal_stack: {"event": "  2025-07-11 15:30:00: O=24465.9400, H=24466.9400, L=24464.6400, C=24465.1200, V=2498", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.749031Z"}
2025-07-13 04:22:15,749 [INFO] signal_stack: {"event": "  2025-07-11 15:29:00: O=24465.6100, H=24468.0100, L=24463.8100, C=24466.3300, V=3014", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.749031Z"}
2025-07-13 04:22:15,750 [INFO] signal_stack: {"event": "  2025-07-11 15:28:00: O=24469.5200, H=24473.2200, L=24464.8200, C=24466.5900, V=4462", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.750002Z"}
2025-07-13 04:22:15,750 [INFO] signal_stack: {"event": "  2025-07-11 15:27:00: O=24469.9400, H=24472.6400, L=24467.3400, C=24470.2000, V=2163", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.750002Z"}
2025-07-13 04:22:15,751 [INFO] signal_stack: {"event": "  2025-07-11 15:26:00: O=24470.4800, H=24473.8800, L=24470.4800, C=24470.6800, V=2849", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.750002Z"}
2025-07-13 04:22:15,758 [WARNING] app.services.aggregation_service: {"event": "No aggregated data found for NIFTY - 5m", "logger": "app.services.aggregation_service", "level": "warning", "timestamp": "2025-07-12T22:52:15.758373Z"}
2025-07-13 04:22:15,760 [WARNING] signal_stack: {"event": "\u26a0 No 5m aggregated data found", "logger": "signal_stack", "level": "warning", "timestamp": "2025-07-12T22:52:15.760389Z"}
2025-07-13 04:22:15,765 [WARNING] app.services.aggregation_service: {"event": "No aggregated data found for NIFTY - 15m", "logger": "app.services.aggregation_service", "level": "warning", "timestamp": "2025-07-12T22:52:15.765831Z"}
2025-07-13 04:22:15,766 [WARNING] signal_stack: {"event": "\u26a0 No 15m aggregated data found", "logger": "signal_stack", "level": "warning", "timestamp": "2025-07-12T22:52:15.766826Z"}
2025-07-13 04:22:15,771 [WARNING] app.services.aggregation_service: {"event": "No aggregated data found for NIFTY - 30m", "logger": "app.services.aggregation_service", "level": "warning", "timestamp": "2025-07-12T22:52:15.771859Z"}
2025-07-13 04:22:15,773 [WARNING] signal_stack: {"event": "\u26a0 No 30m aggregated data found", "logger": "signal_stack", "level": "warning", "timestamp": "2025-07-12T22:52:15.772860Z"}
2025-07-13 04:22:15,774 [INFO] signal_stack: {"event": "\n================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.774847Z"}
2025-07-13 04:22:15,776 [INFO] signal_stack: {"event": "NIFTY PIPELINE TEST SETUP COMPLETED SUCCESSFULLY!", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.776155Z"}
2025-07-13 04:22:15,776 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.776155Z"}
2025-07-13 04:22:15,777 [INFO] signal_stack: {"event": "The system is now ready for:", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.777178Z"}
2025-07-13 04:22:15,777 [INFO] signal_stack: {"event": "1. Database operations with TimescaleDB", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.777178Z"}
2025-07-13 04:22:15,778 [INFO] signal_stack: {"event": "2. Automatic timeframe aggregation", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.778157Z"}
2025-07-13 04:22:15,778 [INFO] signal_stack: {"event": "3. Historical data storage and retrieval", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.778157Z"}
2025-07-13 04:22:15,779 [INFO] signal_stack: {"event": "4. Strategy development and backtesting", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.779159Z"}
2025-07-13 04:22:15,779 [INFO] signal_stack: {"event": "\nNote: Fyers API integration requires valid credentials", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.779159Z"}
2025-07-13 04:22:27,258 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.258633Z"}
2025-07-13 04:22:27,259 [INFO] signal_stack: {"event": "PRODUCTION SYSTEM VALIDATION", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.259632Z"}
2025-07-13 04:22:27,260 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.260631Z"}
2025-07-13 04:22:27,261 [INFO] signal_stack: {"event": "\u26a0\ufe0f  WARNING: This uses REAL market data and API calls", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.260631Z"}
2025-07-13 04:22:27,261 [INFO] signal_stack: {"event": "\u26a0\ufe0f  Ensure you have valid Fyers credentials", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.261602Z"}
2025-07-13 04:22:27,262 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.262619Z"}
2025-07-13 04:22:27,262 [INFO] signal_stack: {"event": "\n--- Database Connection Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.262619Z"}
2025-07-13 04:22:27,459 [INFO] app.database.connection: Database connection successful
2025-07-13 04:22:27,460 [ERROR] signal_stack: {"event": "Database validation failed: Textual SQL expression 'SELECT 1 FROM pg_extensio...' should be explicitly declared as text('SELECT 1 FROM pg_extensio...')", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:52:27.459399Z"}
2025-07-13 04:22:27,460 [ERROR] signal_stack: {"event": "\u274c Database Connection: FAILED (0.20s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:52:27.460404Z"}
2025-07-13 04:22:27,461 [INFO] signal_stack: {"event": "\n--- Database Schema Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.461400Z"}
2025-07-13 04:22:27,461 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:22:27,461 [INFO] app.database.connection: Initializing database...
2025-07-13 04:22:27,465 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:22:27,492 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:22:27,492 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:22:27,501 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:22:27,501 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:22:27,506 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:22:27,507 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:22:27,516 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:22:27,517 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:22:27,517 [ERROR] signal_stack: {"event": "Schema validation failed: Textual SQL expression 'SELECT 1 FROM information...' should be explicitly declared as text('SELECT 1 FROM information...')", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:52:27.517078Z"}
2025-07-13 04:22:27,518 [ERROR] signal_stack: {"event": "\u274c Database Schema: FAILED (0.06s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:52:27.518077Z"}
2025-07-13 04:22:27,518 [INFO] signal_stack: {"event": "\n--- Symbol Setup Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.518077Z"}
2025-07-13 04:22:27,571 [INFO] signal_stack: {"event": "\u2713 Symbol validated: NIFTY (ID: 1)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.571295Z"}
2025-07-13 04:22:27,573 [INFO] signal_stack: {"event": "\u2705 Symbol Setup: PASSED (0.05s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.572295Z"}
2025-07-13 04:22:27,573 [INFO] signal_stack: {"event": "\n--- Fyers Authentication Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.573296Z"}
2025-07-13 04:22:27,573 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T22:52:27.573296Z"}
2025-07-13 04:22:27,574 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:52:27.574533Z"}
2025-07-13 04:22:27,916 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:52:27.916680Z"}
2025-07-13 04:26:05,422 [ERROR] app.integrations.fyers.auth: {"event": "Token generation failed: {'code': -437, 'message': 'invalid auth code', 's': 'error'}", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T22:56:05.422674Z"}
2025-07-13 04:26:05,423 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T22:56:05.423671Z"}
2025-07-13 04:26:05,424 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T22:56:05.424676Z"}
2025-07-13 04:26:05,424 [ERROR] signal_stack: {"event": "Fyers authentication failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:56:05.424676Z"}
2025-07-13 04:26:05,425 [ERROR] signal_stack: {"event": "\u274c Fyers Authentication: FAILED (217.85s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:56:05.425673Z"}
2025-07-13 04:26:05,425 [INFO] signal_stack: {"event": "\n--- Historical Data Fetch Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.425673Z"}
2025-07-13 04:26:05,426 [ERROR] app.services.market_data_service: {"event": "Fyers client not authenticated", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T22:56:05.426669Z"}
2025-07-13 04:26:05,427 [ERROR] signal_stack: {"event": "Historical data fetch failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:56:05.426669Z"}
2025-07-13 04:26:05,427 [ERROR] signal_stack: {"event": "\u274c Historical Data Fetch: FAILED (0.00s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:56:05.427672Z"}
2025-07-13 04:26:05,428 [INFO] signal_stack: {"event": "\n--- Data Storage Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.428671Z"}
2025-07-13 04:26:05,440 [ERROR] signal_stack: {"event": "Failed to retrieve stored data", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:56:05.440253Z"}
2025-07-13 04:26:05,441 [ERROR] signal_stack: {"event": "\u274c Data Storage: FAILED (0.01s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:56:05.441274Z"}
2025-07-13 04:26:05,442 [INFO] signal_stack: {"event": "\n--- Timeframe Aggregation Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.441274Z"}
2025-07-13 04:26:05,627 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 5m from 2025-07-10 04:26:05.442258 to 2025-07-13 04:26:05.442258", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:56:05.627056Z"}
2025-07-13 04:26:05,670 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:56:05.670207Z"}
2025-07-13 04:26:05,675 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 15m from 2025-07-10 04:26:05.671209 to 2025-07-13 04:26:05.671209", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:56:05.675617Z"}
2025-07-13 04:26:05,698 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:56:05.698344Z"}
2025-07-13 04:26:05,702 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 30m from 2025-07-10 04:26:05.698344 to 2025-07-13 04:26:05.698344", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:56:05.702344Z"}
2025-07-13 04:26:05,732 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:56:05.732345Z"}
2025-07-13 04:26:05,736 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 1h from 2025-07-10 04:26:05.733346 to 2025-07-13 04:26:05.733346", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:56:05.736353Z"}
2025-07-13 04:26:05,752 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:56:05.752603Z"}
2025-07-13 04:26:05,770 [INFO] signal_stack: {"event": "\u2713 Timeframe aggregation validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.770040Z"}
2025-07-13 04:26:05,771 [INFO] signal_stack: {"event": "  5m: 158 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.771046Z"}
2025-07-13 04:26:05,771 [INFO] signal_stack: {"event": "  15m: 54 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.771395Z"}
2025-07-13 04:26:05,772 [INFO] signal_stack: {"event": "  30m: 28 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.772410Z"}
2025-07-13 04:26:05,772 [INFO] signal_stack: {"event": "  1h: 14 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.772410Z"}
2025-07-13 04:26:05,773 [INFO] signal_stack: {"event": "\u2705 Timeframe Aggregation: PASSED (0.33s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.773388Z"}
2025-07-13 04:26:05,774 [INFO] signal_stack: {"event": "\n--- Real-time Data Pipeline Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.774414Z"}
2025-07-13 04:26:05,785 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T22:56:05.785393Z"}
2025-07-13 04:26:05,786 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:56:05.786389Z"}
2025-07-13 04:26:05,787 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:56:05.787391Z"}
2025-07-13 04:27:31,881 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:57:31.881051Z"}
2025-07-13 04:27:31,882 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T22:57:31.882033Z"}
2025-07-13 04:27:31,882 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T22:57:31.882033Z"}
2025-07-13 04:27:31,883 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T22:57:31.883039Z"}
2025-07-13 04:27:31,884 [ERROR] app.services.realtime_pipeline: {"event": "Failed to initialize Fyers connection", "logger": "app.services.realtime_pipeline", "level": "error", "timestamp": "2025-07-12T22:57:31.883039Z"}
2025-07-13 04:27:31,884 [ERROR] signal_stack: {"event": "Pipeline initialization failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:57:31.884055Z"}
2025-07-13 04:27:31,885 [ERROR] signal_stack: {"event": "\u274c Real-time Data Pipeline: FAILED (86.11s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:57:31.884055Z"}
2025-07-13 04:27:31,885 [INFO] signal_stack: {"event": "\n--- Data Integrity Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.885049Z"}
2025-07-13 04:27:31,899 [INFO] signal_stack: {"event": "\u2713 Data integrity validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.898049Z"}
2025-07-13 04:27:31,899 [INFO] signal_stack: {"event": "\u2705 Data Integrity: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.899061Z"}
2025-07-13 04:27:31,900 [INFO] signal_stack: {"event": "\n--- Performance Metrics Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.900031Z"}
2025-07-13 04:27:31,905 [INFO] signal_stack: {"event": "\u2713 Performance validated: Query took 0.01s for 0 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.905050Z"}
2025-07-13 04:27:31,906 [INFO] signal_stack: {"event": "\u2705 Performance Metrics: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.906049Z"}
2025-07-13 04:27:31,907 [INFO] signal_stack: {"event": "\n================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.907032Z"}
2025-07-13 04:27:31,907 [INFO] signal_stack: {"event": "VALIDATION REPORT", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.907032Z"}
2025-07-13 04:27:31,908 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.908035Z"}
2025-07-13 04:27:31,908 [INFO] signal_stack: {"event": "Overall Success Rate: 40.0% (4/10)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.908035Z"}
2025-07-13 04:27:31,909 [INFO] signal_stack: {"event": "\u274c FAIL Database Connection (0.20s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.909031Z"}
2025-07-13 04:27:31,909 [INFO] signal_stack: {"event": "\u274c FAIL Database Schema (0.06s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.909031Z"}
2025-07-13 04:27:31,910 [INFO] signal_stack: {"event": "\u2705 PASS Symbol Setup (0.05s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.910029Z"}
2025-07-13 04:27:31,910 [INFO] signal_stack: {"event": "\u274c FAIL Fyers Authentication (217.85s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.910029Z"}
2025-07-13 04:27:31,910 [INFO] signal_stack: {"event": "\u274c FAIL Historical Data Fetch (0.00s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.910029Z"}
2025-07-13 04:27:31,911 [INFO] signal_stack: {"event": "\u274c FAIL Data Storage (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.911033Z"}
2025-07-13 04:27:31,911 [INFO] signal_stack: {"event": "\u2705 PASS Timeframe Aggregation (0.33s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.911033Z"}
2025-07-13 04:27:31,912 [INFO] signal_stack: {"event": "\u274c FAIL Real-time Data Pipeline (86.11s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.912038Z"}
2025-07-13 04:27:31,913 [INFO] signal_stack: {"event": "\u2705 PASS Data Integrity (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.913031Z"}
2025-07-13 04:27:31,913 [INFO] signal_stack: {"event": "\u2705 PASS Performance Metrics (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.913031Z"}
2025-07-13 04:27:31,917 [INFO] signal_stack: {"event": "Detailed report saved to: validation_report_20250713_042731.json", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.917030Z"}
2025-07-13 04:27:31,917 [INFO] signal_stack: {"event": "\u274c MULTIPLE VALIDATIONS FAILED - SYSTEM NOT READY", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.917030Z"}
2025-07-13 04:27:44,809 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:44.809788Z"}
2025-07-13 04:27:44,809 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:44.809788Z"}
2025-07-13 04:27:44,997 [INFO] app.database.connection: Database connection successful
2025-07-13 04:27:44,997 [INFO] app.database.connection: Database connection successful
2025-07-13 04:27:44,997 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:27:44,997 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:27:44,998 [INFO] app.database.connection: Initializing database...
2025-07-13 04:27:44,998 [INFO] app.database.connection: Initializing database...
2025-07-13 04:27:45,001 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:27:45,001 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:27:45,032 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:27:45,032 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:27:45,033 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:27:45,033 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:27:45,040 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:27:45,040 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:27:45,041 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:27:45,041 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:27:45,047 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:27:45,047 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:27:45,048 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:27:45,048 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:27:45,056 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:27:45,056 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:27:45,057 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:27:45,057 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:27:45,057 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:45.057606Z"}
2025-07-13 04:27:45,057 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:45.057606Z"}
2025-07-13 04:27:45,058 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:45.058607Z"}
2025-07-13 04:27:45,058 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:45.058607Z"}
2025-07-13 04:29:25,675 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.675533Z"}
2025-07-13 04:29:25,676 [INFO] signal_stack: {"event": "PRODUCTION SYSTEM VALIDATION", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.676559Z"}
2025-07-13 04:29:25,677 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.676559Z"}
2025-07-13 04:29:25,677 [INFO] signal_stack: {"event": "\u26a0\ufe0f  WARNING: This uses REAL market data and API calls", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.677539Z"}
2025-07-13 04:29:25,677 [INFO] signal_stack: {"event": "\u26a0\ufe0f  Ensure you have valid Fyers credentials", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.677539Z"}
2025-07-13 04:29:25,678 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.678564Z"}
2025-07-13 04:29:25,679 [INFO] signal_stack: {"event": "\n--- Database Connection Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.678564Z"}
2025-07-13 04:29:25,866 [INFO] app.database.connection: Database connection successful
2025-07-13 04:29:25,866 [ERROR] signal_stack: {"event": "Database validation failed: Textual SQL expression 'SELECT 1 FROM pg_extensio...' should be explicitly declared as text('SELECT 1 FROM pg_extensio...')", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:59:25.866533Z"}
2025-07-13 04:29:25,867 [ERROR] signal_stack: {"event": "\u274c Database Connection: FAILED (0.19s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:59:25.867789Z"}
2025-07-13 04:29:25,868 [INFO] signal_stack: {"event": "\n--- Database Schema Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.868786Z"}
2025-07-13 04:29:25,868 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:29:25,868 [INFO] app.database.connection: Initializing database...
2025-07-13 04:29:25,871 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:29:25,896 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:29:25,897 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:29:25,905 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:29:25,906 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:29:25,911 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:29:25,941 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:29:25,949 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:29:25,950 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:29:25,951 [ERROR] signal_stack: {"event": "Schema validation failed: Textual SQL expression 'SELECT 1 FROM information...' should be explicitly declared as text('SELECT 1 FROM information...')", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:59:25.951142Z"}
2025-07-13 04:29:25,951 [ERROR] signal_stack: {"event": "\u274c Database Schema: FAILED (0.08s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:59:25.951142Z"}
2025-07-13 04:29:25,952 [INFO] signal_stack: {"event": "\n--- Symbol Setup Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.952160Z"}
2025-07-13 04:29:26,007 [INFO] signal_stack: {"event": "\u2713 Symbol validated: NIFTY (ID: 1)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:26.006014Z"}
2025-07-13 04:29:26,008 [INFO] signal_stack: {"event": "\u2705 Symbol Setup: PASSED (0.06s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:26.008027Z"}
2025-07-13 04:29:26,008 [INFO] signal_stack: {"event": "\n--- Fyers Authentication Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:26.008027Z"}
2025-07-13 04:29:26,009 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T22:59:26.009031Z"}
2025-07-13 04:29:26,010 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:59:26.010028Z"}
2025-07-13 04:29:26,356 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:59:26.356281Z"}
2025-07-13 04:32:19,499 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:02:19.499190Z"}
2025-07-13 04:32:19,500 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T23:02:19.499190Z"}
2025-07-13 04:32:19,500 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:02:19.500497Z"}
2025-07-13 04:32:19,500 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:02:19.500497Z"}
2025-07-13 04:32:19,501 [ERROR] signal_stack: {"event": "Fyers authentication failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:02:19.501519Z"}
2025-07-13 04:32:19,501 [ERROR] signal_stack: {"event": "\u274c Fyers Authentication: FAILED (173.49s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:02:19.501519Z"}
2025-07-13 04:32:19,502 [INFO] signal_stack: {"event": "\n--- Historical Data Fetch Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.502497Z"}
2025-07-13 04:32:19,502 [ERROR] app.services.market_data_service: {"event": "Fyers client not authenticated", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:02:19.502497Z"}
2025-07-13 04:32:19,503 [ERROR] signal_stack: {"event": "Historical data fetch failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:02:19.503497Z"}
2025-07-13 04:32:19,503 [ERROR] signal_stack: {"event": "\u274c Historical Data Fetch: FAILED (0.00s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:02:19.503497Z"}
2025-07-13 04:32:19,504 [INFO] signal_stack: {"event": "\n--- Data Storage Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.503497Z"}
2025-07-13 04:32:19,514 [ERROR] signal_stack: {"event": "Failed to retrieve stored data", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:02:19.514500Z"}
2025-07-13 04:32:19,514 [ERROR] signal_stack: {"event": "\u274c Data Storage: FAILED (0.01s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:02:19.514500Z"}
2025-07-13 04:32:19,515 [INFO] signal_stack: {"event": "\n--- Timeframe Aggregation Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.515507Z"}
2025-07-13 04:32:19,683 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 5m from 2025-07-10 04:32:19.516498 to 2025-07-13 04:32:19.516498", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:02:19.682038Z"}
2025-07-13 04:32:19,718 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:02:19.718740Z"}
2025-07-13 04:32:19,721 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 15m from 2025-07-10 04:32:19.718740 to 2025-07-13 04:32:19.718740", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:02:19.721740Z"}
2025-07-13 04:32:19,730 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:02:19.730253Z"}
2025-07-13 04:32:19,733 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 30m from 2025-07-10 04:32:19.730253 to 2025-07-13 04:32:19.730253", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:02:19.733265Z"}
2025-07-13 04:32:19,740 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:02:19.740256Z"}
2025-07-13 04:32:19,744 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 1h from 2025-07-10 04:32:19.741236 to 2025-07-13 04:32:19.741236", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:02:19.744258Z"}
2025-07-13 04:32:19,751 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:02:19.751257Z"}
2025-07-13 04:32:19,766 [INFO] signal_stack: {"event": "\u2713 Timeframe aggregation validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.766254Z"}
2025-07-13 04:32:19,766 [INFO] signal_stack: {"event": "  5m: 158 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.766617Z"}
2025-07-13 04:32:19,766 [INFO] signal_stack: {"event": "  15m: 54 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.766617Z"}
2025-07-13 04:32:19,767 [INFO] signal_stack: {"event": "  30m: 28 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.767628Z"}
2025-07-13 04:32:19,767 [INFO] signal_stack: {"event": "  1h: 14 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.767628Z"}
2025-07-13 04:32:19,769 [INFO] signal_stack: {"event": "\u2705 Timeframe Aggregation: PASSED (0.25s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.768640Z"}
2025-07-13 04:32:19,769 [INFO] signal_stack: {"event": "\n--- Real-time Data Pipeline Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.769639Z"}
2025-07-13 04:32:19,780 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:02:19.780638Z"}
2025-07-13 04:32:19,781 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:02:19.781618Z"}
2025-07-13 04:32:19,781 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:02:19.781618Z"}
2025-07-13 04:32:29,641 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:02:29.641644Z"}
2025-07-13 04:32:29,643 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T23:02:29.643061Z"}
2025-07-13 04:32:29,643 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:02:29.643061Z"}
2025-07-13 04:32:29,644 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:02:29.644064Z"}
2025-07-13 04:32:29,644 [ERROR] app.services.realtime_pipeline: {"event": "Failed to initialize Fyers connection", "logger": "app.services.realtime_pipeline", "level": "error", "timestamp": "2025-07-12T23:02:29.644064Z"}
2025-07-13 04:32:29,645 [ERROR] signal_stack: {"event": "Pipeline initialization failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:02:29.644064Z"}
2025-07-13 04:32:29,645 [ERROR] signal_stack: {"event": "\u274c Real-time Data Pipeline: FAILED (9.88s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:02:29.645064Z"}
2025-07-13 04:32:29,645 [INFO] signal_stack: {"event": "\n--- Data Integrity Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.645064Z"}
2025-07-13 04:32:29,656 [INFO] signal_stack: {"event": "\u2713 Data integrity validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.656335Z"}
2025-07-13 04:32:29,657 [INFO] signal_stack: {"event": "\u2705 Data Integrity: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.657333Z"}
2025-07-13 04:32:29,658 [INFO] signal_stack: {"event": "\n--- Performance Metrics Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.658334Z"}
2025-07-13 04:32:29,663 [INFO] signal_stack: {"event": "\u2713 Performance validated: Query took 0.01s for 0 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.663343Z"}
2025-07-13 04:32:29,664 [INFO] signal_stack: {"event": "\u2705 Performance Metrics: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.664330Z"}
2025-07-13 04:32:29,664 [INFO] signal_stack: {"event": "\n================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.664330Z"}
2025-07-13 04:32:29,665 [INFO] signal_stack: {"event": "VALIDATION REPORT", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.665628Z"}
2025-07-13 04:32:29,666 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.666634Z"}
2025-07-13 04:32:29,666 [INFO] signal_stack: {"event": "Overall Success Rate: 40.0% (4/10)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.666634Z"}
2025-07-13 04:32:29,667 [INFO] signal_stack: {"event": "\u274c FAIL Database Connection (0.19s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.667633Z"}
2025-07-13 04:32:29,667 [INFO] signal_stack: {"event": "\u274c FAIL Database Schema (0.08s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.667633Z"}
2025-07-13 04:32:29,667 [INFO] signal_stack: {"event": "\u2705 PASS Symbol Setup (0.06s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.667633Z"}
2025-07-13 04:32:29,668 [INFO] signal_stack: {"event": "\u274c FAIL Fyers Authentication (173.49s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.668633Z"}
2025-07-13 04:32:29,668 [INFO] signal_stack: {"event": "\u274c FAIL Historical Data Fetch (0.00s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.668633Z"}
2025-07-13 04:32:29,668 [INFO] signal_stack: {"event": "\u274c FAIL Data Storage (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.668633Z"}
2025-07-13 04:32:29,668 [INFO] signal_stack: {"event": "\u2705 PASS Timeframe Aggregation (0.25s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.668633Z"}
2025-07-13 04:32:29,669 [INFO] signal_stack: {"event": "\u274c FAIL Real-time Data Pipeline (9.88s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.669633Z"}
2025-07-13 04:32:29,669 [INFO] signal_stack: {"event": "\u2705 PASS Data Integrity (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.669633Z"}
2025-07-13 04:32:29,669 [INFO] signal_stack: {"event": "\u2705 PASS Performance Metrics (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.669633Z"}
2025-07-13 04:32:29,672 [INFO] signal_stack: {"event": "Detailed report saved to: validation_report_20250713_043229.json", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.672622Z"}
2025-07-13 04:32:29,673 [INFO] signal_stack: {"event": "\u274c MULTIPLE VALIDATIONS FAILED - SYSTEM NOT READY", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.673644Z"}
2025-07-13 04:35:16,527 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.527783Z"}
2025-07-13 04:35:16,529 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.528783Z"}
2025-07-13 04:35:16,529 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.529752Z"}
2025-07-13 04:35:16,530 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.530771Z"}
2025-07-13 04:35:16,715 [INFO] app.database.connection: Database connection successful
2025-07-13 04:35:16,716 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.716977Z"}
2025-07-13 04:35:16,717 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.717980Z"}
2025-07-13 04:35:16,717 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:35:16,717 [INFO] app.database.connection: Initializing database...
2025-07-13 04:35:16,720 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:35:16,747 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:35:16,748 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:35:16,756 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:35:16,756 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:35:16,761 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:35:16,762 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:35:16,770 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:35:16,771 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:35:16,771 [INFO] signal_stack: {"event": "\u2713 Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.771781Z"}
2025-07-13 04:35:16,772 [INFO] signal_stack: {"event": "\nStep 3: Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.772771Z"}
2025-07-13 04:35:16,773 [INFO] signal_stack: {"event": "Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.772771Z"}
2025-07-13 04:35:16,827 [INFO] signal_stack: {"event": "\u2713 NIFTY symbol already exists", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.827860Z"}
2025-07-13 04:35:16,828 [INFO] signal_stack: {"event": "\nStep 4: Fetching historical data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.828860Z"}
2025-07-13 04:35:16,829 [INFO] signal_stack: {"event": "Fetching historical data for NIFTY...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.829877Z"}
2025-07-13 04:35:16,841 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:05:16.841308Z"}
2025-07-13 04:35:16,842 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:05:16.842330Z"}
2025-07-13 04:35:17,161 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:05:17.161293Z"}
2025-07-13 04:36:19,397 [INFO] signal_stack: {"event": "Received shutdown signal, stopping pipeline...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:19.397146Z"}
2025-07-13 04:36:28,099 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.099240Z"}
2025-07-13 04:36:28,100 [INFO] signal_stack: {"event": "PRODUCTION SYSTEM VALIDATION", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.100236Z"}
2025-07-13 04:36:28,100 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.100236Z"}
2025-07-13 04:36:28,101 [INFO] signal_stack: {"event": "\u26a0\ufe0f  WARNING: This uses REAL market data and API calls", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.101208Z"}
2025-07-13 04:36:28,102 [INFO] signal_stack: {"event": "\u26a0\ufe0f  Ensure you have valid Fyers credentials", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.102206Z"}
2025-07-13 04:36:28,102 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.102206Z"}
2025-07-13 04:36:28,103 [INFO] signal_stack: {"event": "\n--- Database Connection Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.103226Z"}
2025-07-13 04:36:28,289 [INFO] app.database.connection: Database connection successful
2025-07-13 04:36:28,290 [ERROR] signal_stack: {"event": "Database validation failed: Textual SQL expression 'SELECT 1 FROM pg_extensio...' should be explicitly declared as text('SELECT 1 FROM pg_extensio...')", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:06:28.290413Z"}
2025-07-13 04:36:28,291 [ERROR] signal_stack: {"event": "\u274c Database Connection: FAILED (0.19s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:06:28.291414Z"}
2025-07-13 04:36:28,291 [INFO] signal_stack: {"event": "\n--- Database Schema Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.291414Z"}
2025-07-13 04:36:28,292 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:36:28,292 [INFO] app.database.connection: Initializing database...
2025-07-13 04:36:28,295 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:36:28,322 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:36:28,322 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:36:28,329 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:36:28,330 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:36:28,334 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:36:28,335 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:36:28,354 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:36:28,355 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:36:28,355 [ERROR] signal_stack: {"event": "Schema validation failed: Textual SQL expression 'SELECT 1 FROM information...' should be explicitly declared as text('SELECT 1 FROM information...')", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:06:28.355188Z"}
2025-07-13 04:36:28,356 [ERROR] signal_stack: {"event": "\u274c Database Schema: FAILED (0.06s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:06:28.356189Z"}
2025-07-13 04:36:28,356 [INFO] signal_stack: {"event": "\n--- Symbol Setup Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.356189Z"}
2025-07-13 04:36:28,409 [INFO] signal_stack: {"event": "\u2713 Symbol validated: NIFTY (ID: 1)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.409637Z"}
2025-07-13 04:36:28,410 [INFO] signal_stack: {"event": "\u2705 Symbol Setup: PASSED (0.05s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.410657Z"}
2025-07-13 04:36:28,411 [INFO] signal_stack: {"event": "\n--- Fyers Authentication Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.411656Z"}
2025-07-13 04:36:28,411 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:06:28.411656Z"}
2025-07-13 04:36:28,412 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:06:28.412641Z"}
2025-07-13 04:36:28,751 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:06:28.751650Z"}
2025-07-13 04:37:01,012 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:07:01.012903Z"}
2025-07-13 04:37:01,014 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T23:07:01.014220Z"}
2025-07-13 04:37:01,015 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:07:01.015299Z"}
2025-07-13 04:37:01,016 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:07:01.015299Z"}
2025-07-13 04:37:01,016 [ERROR] signal_stack: {"event": "Fyers authentication failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:07:01.016300Z"}
2025-07-13 04:37:01,017 [ERROR] signal_stack: {"event": "\u274c Fyers Authentication: FAILED (32.61s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:07:01.017305Z"}
2025-07-13 04:37:01,018 [INFO] signal_stack: {"event": "\n--- Historical Data Fetch Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.018295Z"}
2025-07-13 04:37:01,018 [ERROR] app.services.market_data_service: {"event": "Fyers client not authenticated", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:07:01.018927Z"}
2025-07-13 04:37:01,019 [ERROR] signal_stack: {"event": "Historical data fetch failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:07:01.019945Z"}
2025-07-13 04:37:01,020 [ERROR] signal_stack: {"event": "\u274c Historical Data Fetch: FAILED (0.00s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:07:01.020964Z"}
2025-07-13 04:37:01,022 [INFO] signal_stack: {"event": "\n--- Data Storage Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.020964Z"}
2025-07-13 04:37:01,033 [ERROR] signal_stack: {"event": "Failed to retrieve stored data", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:07:01.033376Z"}
2025-07-13 04:37:01,034 [ERROR] signal_stack: {"event": "\u274c Data Storage: FAILED (0.01s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:07:01.034720Z"}
2025-07-13 04:37:01,034 [INFO] signal_stack: {"event": "\n--- Timeframe Aggregation Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.034720Z"}
2025-07-13 04:37:01,202 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 5m from 2025-07-10 04:37:01.035732 to 2025-07-13 04:37:01.035732", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:07:01.202539Z"}
2025-07-13 04:37:01,227 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:07:01.227809Z"}
2025-07-13 04:37:01,232 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 15m from 2025-07-10 04:37:01.228813 to 2025-07-13 04:37:01.228813", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:07:01.232810Z"}
2025-07-13 04:37:01,241 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:07:01.241794Z"}
2025-07-13 04:37:01,245 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 30m from 2025-07-10 04:37:01.242791 to 2025-07-13 04:37:01.242791", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:07:01.245803Z"}
2025-07-13 04:37:01,253 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:07:01.253273Z"}
2025-07-13 04:37:01,258 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 1h from 2025-07-10 04:37:01.254255 to 2025-07-13 04:37:01.254255", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:07:01.258260Z"}
2025-07-13 04:37:01,265 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:07:01.265259Z"}
2025-07-13 04:37:01,284 [INFO] signal_stack: {"event": "\u2713 Timeframe aggregation validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.284668Z"}
2025-07-13 04:37:01,285 [INFO] signal_stack: {"event": "  5m: 158 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.285664Z"}
2025-07-13 04:37:01,285 [INFO] signal_stack: {"event": "  15m: 54 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.285664Z"}
2025-07-13 04:37:01,286 [INFO] signal_stack: {"event": "  30m: 28 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.286659Z"}
2025-07-13 04:37:01,287 [INFO] signal_stack: {"event": "  1h: 14 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.287662Z"}
2025-07-13 04:37:01,288 [INFO] signal_stack: {"event": "\u2705 Timeframe Aggregation: PASSED (0.25s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.288664Z"}
2025-07-13 04:37:01,289 [INFO] signal_stack: {"event": "\n--- Real-time Data Pipeline Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.288664Z"}
2025-07-13 04:37:01,301 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:07:01.301109Z"}
2025-07-13 04:37:01,302 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:07:01.302118Z"}
2025-07-13 04:37:01,303 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:07:01.303119Z"}
2025-07-13 04:38:48,903 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:08:48.903626Z"}
2025-07-13 04:38:48,903 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T23:08:48.903626Z"}
2025-07-13 04:38:48,904 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:08:48.904636Z"}
2025-07-13 04:38:48,904 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:08:48.904636Z"}
2025-07-13 04:38:48,905 [ERROR] app.services.realtime_pipeline: {"event": "Failed to initialize Fyers connection", "logger": "app.services.realtime_pipeline", "level": "error", "timestamp": "2025-07-12T23:08:48.905626Z"}
2025-07-13 04:38:48,906 [ERROR] signal_stack: {"event": "Pipeline initialization failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:08:48.905626Z"}
2025-07-13 04:38:48,906 [ERROR] signal_stack: {"event": "\u274c Real-time Data Pipeline: FAILED (107.62s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:08:48.906621Z"}
2025-07-13 04:38:48,907 [INFO] signal_stack: {"event": "\n--- Data Integrity Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.907622Z"}
2025-07-13 04:38:48,920 [INFO] signal_stack: {"event": "\u2713 Data integrity validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.919628Z"}
2025-07-13 04:38:48,920 [INFO] signal_stack: {"event": "\u2705 Data Integrity: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.920612Z"}
2025-07-13 04:38:48,921 [INFO] signal_stack: {"event": "\n--- Performance Metrics Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.921618Z"}
2025-07-13 04:38:48,927 [INFO] signal_stack: {"event": "\u2713 Performance validated: Query took 0.01s for 0 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.927641Z"}
2025-07-13 04:38:48,927 [INFO] signal_stack: {"event": "\u2705 Performance Metrics: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.927987Z"}
2025-07-13 04:38:48,929 [INFO] signal_stack: {"event": "\n================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.927987Z"}
2025-07-13 04:38:48,929 [INFO] signal_stack: {"event": "VALIDATION REPORT", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.929098Z"}
2025-07-13 04:38:48,930 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.930088Z"}
2025-07-13 04:38:48,931 [INFO] signal_stack: {"event": "Overall Success Rate: 40.0% (4/10)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.930088Z"}
2025-07-13 04:38:48,931 [INFO] signal_stack: {"event": "\u274c FAIL Database Connection (0.19s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.931090Z"}
2025-07-13 04:38:48,932 [INFO] signal_stack: {"event": "\u274c FAIL Database Schema (0.06s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.931090Z"}
2025-07-13 04:38:48,932 [INFO] signal_stack: {"event": "\u2705 PASS Symbol Setup (0.05s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.932092Z"}
2025-07-13 04:38:48,933 [INFO] signal_stack: {"event": "\u274c FAIL Fyers Authentication (32.61s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.932092Z"}
2025-07-13 04:38:48,933 [INFO] signal_stack: {"event": "\u274c FAIL Historical Data Fetch (0.00s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.933095Z"}
2025-07-13 04:38:48,934 [INFO] signal_stack: {"event": "\u274c FAIL Data Storage (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.933095Z"}
2025-07-13 04:38:48,934 [INFO] signal_stack: {"event": "\u2705 PASS Timeframe Aggregation (0.25s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.934095Z"}
2025-07-13 04:38:48,935 [INFO] signal_stack: {"event": "\u274c FAIL Real-time Data Pipeline (107.62s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.934095Z"}
2025-07-13 04:38:48,935 [INFO] signal_stack: {"event": "\u2705 PASS Data Integrity (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.935098Z"}
2025-07-13 04:38:48,936 [INFO] signal_stack: {"event": "\u2705 PASS Performance Metrics (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.936097Z"}
2025-07-13 04:38:48,938 [INFO] signal_stack: {"event": "Detailed report saved to: validation_report_20250713_043848.json", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.937102Z"}
2025-07-13 04:38:48,938 [INFO] signal_stack: {"event": "\u274c MULTIPLE VALIDATIONS FAILED - SYSTEM NOT READY", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.938105Z"}
2025-07-13 04:38:52,209 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.209719Z"}
2025-07-13 04:38:52,210 [INFO] signal_stack: {"event": "PRODUCTION SYSTEM VALIDATION", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.210728Z"}
2025-07-13 04:38:52,211 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.211706Z"}
2025-07-13 04:38:52,211 [INFO] signal_stack: {"event": "\u26a0\ufe0f  WARNING: This uses REAL market data and API calls", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.211706Z"}
2025-07-13 04:38:52,212 [INFO] signal_stack: {"event": "\u26a0\ufe0f  Ensure you have valid Fyers credentials", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.212707Z"}
2025-07-13 04:38:52,212 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.212707Z"}
2025-07-13 04:38:52,212 [INFO] signal_stack: {"event": "\n--- Database Connection Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.212707Z"}
2025-07-13 04:38:52,397 [INFO] app.database.connection: Database connection successful
2025-07-13 04:38:52,398 [ERROR] signal_stack: {"event": "Database validation failed: name 'text' is not defined", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:08:52.398460Z"}
2025-07-13 04:38:52,399 [ERROR] signal_stack: {"event": "\u274c Database Connection: FAILED (0.19s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:08:52.399460Z"}
2025-07-13 04:38:52,399 [INFO] signal_stack: {"event": "\n--- Database Schema Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.399460Z"}
2025-07-13 04:38:52,400 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:38:52,400 [INFO] app.database.connection: Initializing database...
2025-07-13 04:38:52,403 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:38:52,430 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:38:52,431 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:38:52,439 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:38:52,439 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:38:52,445 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:38:52,445 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:38:52,470 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:38:52,470 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:38:52,506 [INFO] signal_stack: {"event": "\u2713 Database schema validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.506565Z"}
2025-07-13 04:38:52,508 [INFO] signal_stack: {"event": "\u2705 Database Schema: PASSED (0.11s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.508566Z"}
2025-07-13 04:38:52,509 [INFO] signal_stack: {"event": "\n--- Symbol Setup Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.508566Z"}
2025-07-13 04:38:52,560 [INFO] signal_stack: {"event": "\u2713 Symbol validated: NIFTY (ID: 1)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.560884Z"}
2025-07-13 04:38:52,561 [INFO] signal_stack: {"event": "\u2705 Symbol Setup: PASSED (0.05s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.561875Z"}
2025-07-13 04:38:52,562 [INFO] signal_stack: {"event": "\n--- Fyers Authentication Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.562881Z"}
2025-07-13 04:38:52,562 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:08:52.562881Z"}
2025-07-13 04:38:52,563 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:08:52.563859Z"}
2025-07-13 04:38:52,884 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:08:52.884787Z"}
2025-07-13 04:39:24,546 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:09:24.546581Z"}
2025-07-13 04:39:24,547 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T23:09:24.546581Z"}
2025-07-13 04:39:24,547 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:09:24.547578Z"}
2025-07-13 04:39:24,548 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:09:24.548579Z"}
2025-07-13 04:39:24,549 [ERROR] signal_stack: {"event": "Fyers authentication failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:09:24.549581Z"}
2025-07-13 04:39:24,550 [ERROR] signal_stack: {"event": "\u274c Fyers Authentication: FAILED (31.99s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:09:24.550568Z"}
2025-07-13 04:39:24,550 [INFO] signal_stack: {"event": "\n--- Historical Data Fetch Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.550568Z"}
2025-07-13 04:39:24,551 [ERROR] app.services.market_data_service: {"event": "Fyers client not authenticated", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:09:24.551571Z"}
2025-07-13 04:39:24,552 [ERROR] signal_stack: {"event": "Historical data fetch failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:09:24.551571Z"}
2025-07-13 04:39:24,552 [ERROR] signal_stack: {"event": "\u274c Historical Data Fetch: FAILED (0.00s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:09:24.552565Z"}
2025-07-13 04:39:24,553 [INFO] signal_stack: {"event": "\n--- Data Storage Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.553574Z"}
2025-07-13 04:39:24,568 [ERROR] signal_stack: {"event": "Failed to retrieve stored data", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:09:24.568216Z"}
2025-07-13 04:39:24,569 [ERROR] signal_stack: {"event": "\u274c Data Storage: FAILED (0.01s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:09:24.569230Z"}
2025-07-13 04:39:24,570 [INFO] signal_stack: {"event": "\n--- Timeframe Aggregation Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.570217Z"}
2025-07-13 04:39:24,755 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 5m from 2025-07-10 04:39:24.571223 to 2025-07-13 04:39:24.571223", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:09:24.755625Z"}
2025-07-13 04:39:24,792 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:09:24.792653Z"}
2025-07-13 04:39:24,796 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 15m from 2025-07-10 04:39:24.793636 to 2025-07-13 04:39:24.793636", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:09:24.796653Z"}
2025-07-13 04:39:24,803 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:09:24.803654Z"}
2025-07-13 04:39:24,807 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 30m from 2025-07-10 04:39:24.803654 to 2025-07-13 04:39:24.803654", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:09:24.807657Z"}
2025-07-13 04:39:24,813 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:09:24.813976Z"}
2025-07-13 04:39:24,818 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 1h from 2025-07-10 04:39:24.814997 to 2025-07-13 04:39:24.814997", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:09:24.816995Z"}
2025-07-13 04:39:24,823 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:09:24.823394Z"}
2025-07-13 04:39:24,837 [INFO] signal_stack: {"event": "\u2713 Timeframe aggregation validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.837408Z"}
2025-07-13 04:39:24,838 [INFO] signal_stack: {"event": "  5m: 158 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.838407Z"}
2025-07-13 04:39:24,838 [INFO] signal_stack: {"event": "  15m: 54 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.838407Z"}
2025-07-13 04:39:24,839 [INFO] signal_stack: {"event": "  30m: 28 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.839376Z"}
2025-07-13 04:39:24,840 [INFO] signal_stack: {"event": "  1h: 14 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.839376Z"}
2025-07-13 04:39:24,841 [INFO] signal_stack: {"event": "\u2705 Timeframe Aggregation: PASSED (0.27s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.841387Z"}
2025-07-13 04:39:24,841 [INFO] signal_stack: {"event": "\n--- Real-time Data Pipeline Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.841387Z"}
2025-07-13 04:39:24,853 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:09:24.853845Z"}
2025-07-13 04:39:24,853 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:09:24.853845Z"}
2025-07-13 04:39:24,854 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:09:24.854873Z"}
2025-07-13 04:41:21,232 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:11:21.232986Z"}
2025-07-13 04:41:21,233 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T23:11:21.233979Z"}
2025-07-13 04:41:21,234 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:11:21.234991Z"}
2025-07-13 04:41:21,234 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:11:21.234991Z"}
2025-07-13 04:41:21,235 [ERROR] app.services.realtime_pipeline: {"event": "Failed to initialize Fyers connection", "logger": "app.services.realtime_pipeline", "level": "error", "timestamp": "2025-07-12T23:11:21.235986Z"}
2025-07-13 04:41:21,236 [ERROR] signal_stack: {"event": "Pipeline initialization failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:11:21.236982Z"}
2025-07-13 04:41:21,237 [ERROR] signal_stack: {"event": "\u274c Real-time Data Pipeline: FAILED (116.39s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:11:21.237982Z"}
2025-07-13 04:41:21,238 [INFO] signal_stack: {"event": "\n--- Data Integrity Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.238320Z"}
2025-07-13 04:41:21,250 [INFO] signal_stack: {"event": "\u2713 Data integrity validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.250671Z"}
2025-07-13 04:41:21,251 [INFO] signal_stack: {"event": "\u2705 Data Integrity: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.251671Z"}
2025-07-13 04:41:21,251 [INFO] signal_stack: {"event": "\n--- Performance Metrics Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.251671Z"}
2025-07-13 04:41:21,257 [INFO] signal_stack: {"event": "\u2713 Performance validated: Query took 0.01s for 0 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.257673Z"}
2025-07-13 04:41:21,258 [INFO] signal_stack: {"event": "\u2705 Performance Metrics: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.258692Z"}
2025-07-13 04:41:21,259 [INFO] signal_stack: {"event": "\n================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.259672Z"}
2025-07-13 04:41:21,259 [INFO] signal_stack: {"event": "VALIDATION REPORT", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.259672Z"}
2025-07-13 04:41:21,260 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.260681Z"}
2025-07-13 04:41:21,261 [INFO] signal_stack: {"event": "Overall Success Rate: 50.0% (5/10)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.261697Z"}
2025-07-13 04:41:21,261 [INFO] signal_stack: {"event": "\u274c FAIL Database Connection (0.19s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.261697Z"}
2025-07-13 04:41:21,262 [INFO] signal_stack: {"event": "\u2705 PASS Database Schema (0.11s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.262681Z"}
2025-07-13 04:41:21,262 [INFO] signal_stack: {"event": "\u2705 PASS Symbol Setup (0.05s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.262681Z"}
2025-07-13 04:41:21,262 [INFO] signal_stack: {"event": "\u274c FAIL Fyers Authentication (31.99s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.262681Z"}
2025-07-13 04:41:21,263 [INFO] signal_stack: {"event": "\u274c FAIL Historical Data Fetch (0.00s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.263707Z"}
2025-07-13 04:41:21,264 [INFO] signal_stack: {"event": "\u274c FAIL Data Storage (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.264675Z"}
2025-07-13 04:41:21,264 [INFO] signal_stack: {"event": "\u2705 PASS Timeframe Aggregation (0.27s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.264675Z"}
2025-07-13 04:41:21,265 [INFO] signal_stack: {"event": "\u274c FAIL Real-time Data Pipeline (116.39s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.265701Z"}
2025-07-13 04:41:21,265 [INFO] signal_stack: {"event": "\u2705 PASS Data Integrity (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.265701Z"}
2025-07-13 04:41:21,266 [INFO] signal_stack: {"event": "\u2705 PASS Performance Metrics (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.266671Z"}
2025-07-13 04:41:21,268 [INFO] signal_stack: {"event": "Detailed report saved to: validation_report_20250713_044121.json", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.268666Z"}
2025-07-13 04:41:21,269 [INFO] signal_stack: {"event": "\u274c MULTIPLE VALIDATIONS FAILED - SYSTEM NOT READY", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.269667Z"}
2025-07-13 04:41:24,640 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.640711Z"}
2025-07-13 04:41:24,641 [INFO] signal_stack: {"event": "PRODUCTION SYSTEM VALIDATION", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.641727Z"}
2025-07-13 04:41:24,642 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.641727Z"}
2025-07-13 04:41:24,642 [INFO] signal_stack: {"event": "\u26a0\ufe0f  WARNING: This uses REAL market data and API calls", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.642740Z"}
2025-07-13 04:41:24,642 [INFO] signal_stack: {"event": "\u26a0\ufe0f  Ensure you have valid Fyers credentials", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.642740Z"}
2025-07-13 04:41:24,643 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.643739Z"}
2025-07-13 04:41:24,644 [INFO] signal_stack: {"event": "\n--- Database Connection Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.643739Z"}
2025-07-13 04:41:24,830 [INFO] app.database.connection: Database connection successful
2025-07-13 04:41:24,834 [INFO] signal_stack: {"event": "\u2713 Database connection and TimescaleDB validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.834051Z"}
2025-07-13 04:41:24,836 [INFO] signal_stack: {"event": "\u2705 Database Connection: PASSED (0.19s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.836070Z"}
2025-07-13 04:41:24,836 [INFO] signal_stack: {"event": "\n--- Database Schema Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.836070Z"}
2025-07-13 04:41:24,837 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:41:24,837 [INFO] app.database.connection: Initializing database...
2025-07-13 04:41:24,839 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:41:24,864 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:41:24,864 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:41:24,873 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:41:24,873 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:41:24,879 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:41:24,880 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:41:24,888 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:41:24,888 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:41:24,913 [INFO] signal_stack: {"event": "\u2713 Database schema validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.913088Z"}
2025-07-13 04:41:24,915 [INFO] signal_stack: {"event": "\u2705 Database Schema: PASSED (0.08s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.915076Z"}
2025-07-13 04:41:24,915 [INFO] signal_stack: {"event": "\n--- Symbol Setup Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.915076Z"}
2025-07-13 04:41:24,968 [INFO] signal_stack: {"event": "\u2713 Symbol validated: NIFTY (ID: 1)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.968077Z"}
2025-07-13 04:41:24,970 [INFO] signal_stack: {"event": "\u2705 Symbol Setup: PASSED (0.05s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.970052Z"}
2025-07-13 04:41:24,970 [INFO] signal_stack: {"event": "\n--- Fyers Authentication Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.970052Z"}
2025-07-13 04:41:24,971 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:11:24.971067Z"}
2025-07-13 04:41:24,971 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:11:24.971446Z"}
2025-07-13 04:41:25,285 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:11:25.285317Z"}
2025-07-13 04:41:48,428 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:11:48.428517Z"}
2025-07-13 04:41:48,429 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T23:11:48.429535Z"}
2025-07-13 04:41:48,430 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:11:48.430532Z"}
2025-07-13 04:41:48,431 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:11:48.430532Z"}
2025-07-13 04:41:48,431 [ERROR] signal_stack: {"event": "Fyers authentication failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:11:48.431512Z"}
2025-07-13 04:41:48,432 [ERROR] signal_stack: {"event": "\u274c Fyers Authentication: FAILED (23.46s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:11:48.432513Z"}
2025-07-13 04:41:48,433 [INFO] signal_stack: {"event": "\n--- Historical Data Fetch Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.432513Z"}
2025-07-13 04:41:48,433 [ERROR] app.services.market_data_service: {"event": "Fyers client not authenticated", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:11:48.433510Z"}
2025-07-13 04:41:48,434 [ERROR] signal_stack: {"event": "Historical data fetch failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:11:48.434514Z"}
2025-07-13 04:41:48,435 [ERROR] signal_stack: {"event": "\u274c Historical Data Fetch: FAILED (0.00s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:11:48.435509Z"}
2025-07-13 04:41:48,436 [INFO] signal_stack: {"event": "\n--- Data Storage Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.435509Z"}
2025-07-13 04:41:48,444 [ERROR] signal_stack: {"event": "Failed to retrieve stored data", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:11:48.444790Z"}
2025-07-13 04:41:48,445 [ERROR] signal_stack: {"event": "\u274c Data Storage: FAILED (0.01s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:11:48.445777Z"}
2025-07-13 04:41:48,446 [INFO] signal_stack: {"event": "\n--- Timeframe Aggregation Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.446758Z"}
2025-07-13 04:41:48,627 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 5m from 2025-07-10 04:41:48.446758 to 2025-07-13 04:41:48.446758", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:11:48.627852Z"}
2025-07-13 04:41:48,651 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:11:48.650847Z"}
2025-07-13 04:41:48,655 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 15m from 2025-07-10 04:41:48.651875 to 2025-07-13 04:41:48.651875", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:11:48.655173Z"}
2025-07-13 04:41:48,662 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:11:48.662405Z"}
2025-07-13 04:41:48,667 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 30m from 2025-07-10 04:41:48.663404 to 2025-07-13 04:41:48.663404", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:11:48.667402Z"}
2025-07-13 04:41:48,673 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:11:48.673764Z"}
2025-07-13 04:41:48,677 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 1h from 2025-07-10 04:41:48.674765 to 2025-07-13 04:41:48.674765", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:11:48.677735Z"}
2025-07-13 04:41:48,684 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:11:48.684104Z"}
2025-07-13 04:41:48,701 [INFO] signal_stack: {"event": "\u2713 Timeframe aggregation validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.701510Z"}
2025-07-13 04:41:48,702 [INFO] signal_stack: {"event": "  5m: 158 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.702504Z"}
2025-07-13 04:41:48,702 [INFO] signal_stack: {"event": "  15m: 54 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.702504Z"}
2025-07-13 04:41:48,703 [INFO] signal_stack: {"event": "  30m: 28 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.703521Z"}
2025-07-13 04:41:48,703 [INFO] signal_stack: {"event": "  1h: 14 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.703521Z"}
2025-07-13 04:41:48,704 [INFO] signal_stack: {"event": "\u2705 Timeframe Aggregation: PASSED (0.26s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.704521Z"}
2025-07-13 04:41:48,705 [INFO] signal_stack: {"event": "\n--- Real-time Data Pipeline Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.705533Z"}
2025-07-13 04:41:48,716 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:11:48.716853Z"}
2025-07-13 04:41:48,717 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:11:48.717847Z"}
2025-07-13 04:41:48,717 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:11:48.717847Z"}
2025-07-13 04:42:10,154 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:12:10.153527Z"}
2025-07-13 04:42:10,154 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T23:12:10.154531Z"}
2025-07-13 04:42:10,155 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:12:10.155534Z"}
2025-07-13 04:42:10,156 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:12:10.155534Z"}
2025-07-13 04:42:10,156 [ERROR] app.services.realtime_pipeline: {"event": "Failed to initialize Fyers connection", "logger": "app.services.realtime_pipeline", "level": "error", "timestamp": "2025-07-12T23:12:10.156543Z"}
2025-07-13 04:42:10,157 [ERROR] signal_stack: {"event": "Pipeline initialization failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:12:10.156543Z"}
2025-07-13 04:42:10,157 [ERROR] signal_stack: {"event": "\u274c Real-time Data Pipeline: FAILED (21.45s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:12:10.157541Z"}
2025-07-13 04:42:10,158 [INFO] signal_stack: {"event": "\n--- Data Integrity Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.158529Z"}
2025-07-13 04:42:10,170 [INFO] signal_stack: {"event": "\u2713 Data integrity validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.170059Z"}
2025-07-13 04:42:10,171 [INFO] signal_stack: {"event": "\u2705 Data Integrity: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.171056Z"}
2025-07-13 04:42:10,172 [INFO] signal_stack: {"event": "\n--- Performance Metrics Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.172069Z"}
2025-07-13 04:42:10,178 [INFO] signal_stack: {"event": "\u2713 Performance validated: Query took 0.01s for 0 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.178043Z"}
2025-07-13 04:42:10,179 [INFO] signal_stack: {"event": "\u2705 Performance Metrics: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.178043Z"}
2025-07-13 04:42:10,179 [INFO] signal_stack: {"event": "\n================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.179039Z"}
2025-07-13 04:42:10,179 [INFO] signal_stack: {"event": "VALIDATION REPORT", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.179039Z"}
2025-07-13 04:42:10,180 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.180059Z"}
2025-07-13 04:42:10,181 [INFO] signal_stack: {"event": "Overall Success Rate: 60.0% (6/10)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.181057Z"}
2025-07-13 04:42:10,181 [INFO] signal_stack: {"event": "\u2705 PASS Database Connection (0.19s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.181057Z"}
2025-07-13 04:42:10,182 [INFO] signal_stack: {"event": "\u2705 PASS Database Schema (0.08s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.182059Z"}
2025-07-13 04:42:10,182 [INFO] signal_stack: {"event": "\u2705 PASS Symbol Setup (0.05s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.182059Z"}
2025-07-13 04:42:10,183 [INFO] signal_stack: {"event": "\u274c FAIL Fyers Authentication (23.46s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.183063Z"}
2025-07-13 04:42:10,183 [INFO] signal_stack: {"event": "\u274c FAIL Historical Data Fetch (0.00s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.183063Z"}
2025-07-13 04:42:10,184 [INFO] signal_stack: {"event": "\u274c FAIL Data Storage (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.184057Z"}
2025-07-13 04:42:10,185 [INFO] signal_stack: {"event": "\u2705 PASS Timeframe Aggregation (0.26s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.184057Z"}
2025-07-13 04:42:10,185 [INFO] signal_stack: {"event": "\u274c FAIL Real-time Data Pipeline (21.45s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.185058Z"}
2025-07-13 04:42:10,185 [INFO] signal_stack: {"event": "\u2705 PASS Data Integrity (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.185058Z"}
2025-07-13 04:42:10,186 [INFO] signal_stack: {"event": "\u2705 PASS Performance Metrics (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.186059Z"}
2025-07-13 04:42:10,202 [INFO] signal_stack: {"event": "Detailed report saved to: validation_report_20250713_044210.json", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.202080Z"}
2025-07-13 04:42:10,203 [INFO] signal_stack: {"event": "\u274c MULTIPLE VALIDATIONS FAILED - SYSTEM NOT READY", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.203068Z"}
2025-07-13 04:54:29,166 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.166636Z"}
2025-07-13 04:54:29,167 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.167635Z"}
2025-07-13 04:54:29,168 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.168646Z"}
2025-07-13 04:54:29,168 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.168646Z"}
2025-07-13 04:54:29,358 [INFO] app.database.connection: Database connection successful
2025-07-13 04:54:29,359 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.359530Z"}
2025-07-13 04:54:29,360 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.360531Z"}
2025-07-13 04:54:29,360 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:54:29,360 [INFO] app.database.connection: Initializing database...
2025-07-13 04:54:29,364 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:54:29,393 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:54:29,394 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:54:29,403 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:54:29,403 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:54:29,409 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:54:29,410 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:54:29,432 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:54:29,433 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:54:29,433 [INFO] signal_stack: {"event": "\u2713 Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.433123Z"}
2025-07-13 04:54:29,434 [INFO] signal_stack: {"event": "\nStep 3: Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.434124Z"}
2025-07-13 04:54:29,434 [INFO] signal_stack: {"event": "Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.434124Z"}
2025-07-13 04:54:29,489 [INFO] signal_stack: {"event": "\u2713 NIFTY symbol already exists", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.489310Z"}
2025-07-13 04:54:29,491 [INFO] signal_stack: {"event": "\nStep 4: Fetching historical data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.491312Z"}
2025-07-13 04:54:29,492 [INFO] signal_stack: {"event": "Fetching historical data for NIFTY...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.491312Z"}
2025-07-13 04:54:29,502 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:24:29.502684Z"}
2025-07-13 04:54:29,503 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:24:29.503682Z"}
2025-07-13 04:54:29,816 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:24:29.816450Z"}
2025-07-13 04:54:29,849 [INFO] app.integrations.fyers.auth: {"event": "\n=== Fyers API Authentication ===", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:24:29.849536Z"}
2025-07-13 04:54:29,850 [INFO] app.integrations.fyers.auth: {"event": "A browser window will open for you to log in to Fyers.", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:24:29.850516Z"}
2025-07-13 04:54:29,850 [INFO] app.integrations.fyers.auth: {"event": "After logging in, you will be redirected to Google.", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:24:29.850516Z"}
2025-07-13 04:54:29,851 [INFO] app.integrations.fyers.auth: {"event": "Copy the auth code from the URL and paste it here.", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:24:29.851524Z"}
2025-07-13 04:54:29,852 [INFO] app.integrations.fyers.auth: {"event": "\nPlease login in the private browser window that opened.", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:24:29.852523Z"}
2025-07-13 04:55:04,411 [INFO] signal_stack: {"event": "Received shutdown signal, stopping pipeline...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:25:04.411642Z"}
2025-07-13 04:56:45,712 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.712140Z"}
2025-07-13 04:56:45,713 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.713140Z"}
2025-07-13 04:56:45,714 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.714141Z"}
2025-07-13 04:56:45,714 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.714141Z"}
2025-07-13 04:56:45,915 [INFO] app.database.connection: Database connection successful
2025-07-13 04:56:45,916 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.916240Z"}
2025-07-13 04:56:45,916 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.916240Z"}
2025-07-13 04:56:45,917 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:56:45,918 [INFO] app.database.connection: Initializing database...
2025-07-13 04:56:45,920 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:56:45,947 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:56:45,947 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:56:45,957 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:56:45,957 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:56:45,963 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:56:45,964 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:56:45,974 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:56:45,976 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:56:45,977 [INFO] signal_stack: {"event": "\u2713 Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.977240Z"}
2025-07-13 04:56:45,978 [INFO] signal_stack: {"event": "\nStep 3: Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.978250Z"}
2025-07-13 04:56:45,979 [INFO] signal_stack: {"event": "Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.978250Z"}
2025-07-13 04:56:46,051 [INFO] signal_stack: {"event": "\u2713 NIFTY symbol already exists", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:46.051551Z"}
2025-07-13 04:56:46,052 [INFO] signal_stack: {"event": "\nStep 4: Fetching historical data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:46.052552Z"}
2025-07-13 04:56:46,053 [INFO] signal_stack: {"event": "Fetching historical data for NIFTY...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:46.053546Z"}
2025-07-13 04:56:46,064 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:26:46.064542Z"}
2025-07-13 04:56:46,065 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:46.065537Z"}
2025-07-13 04:56:46,390 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=SD0YWXHE6D-100&redirect_uri=https%3A%2F%2Ftrade.fyers.in%2Fapi-login%2Fredirect-uri%2Findex.html&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:46.390436Z"}
2025-07-13 04:56:46,425 [INFO] app.integrations.fyers.auth: {"event": "\n=== Fyers API Authentication ===", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:46.425597Z"}
2025-07-13 04:56:46,426 [INFO] app.integrations.fyers.auth: {"event": "A browser window will open for you to log in to Fyers.", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:46.426587Z"}
2025-07-13 04:56:46,426 [INFO] app.integrations.fyers.auth: {"event": "After logging in, you will be redirected to Google.", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:46.426587Z"}
2025-07-13 04:56:46,427 [INFO] app.integrations.fyers.auth: {"event": "Copy the auth code from the URL and paste it here.", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:46.427591Z"}
2025-07-13 04:56:46,427 [INFO] app.integrations.fyers.auth: {"event": "\nPlease login in the private browser window that opened.", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:46.427591Z"}
2025-07-13 04:56:58,511 [INFO] app.integrations.fyers.auth: {"event": "Authentication files saved to auth", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:58.510960Z"}
2025-07-13 04:56:58,808 [INFO] app.integrations.fyers.auth: {"event": "Fyers authentication successful!", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:58.808903Z"}
2025-07-13 04:56:58,809 [ERROR] app.integrations.fyers.client: {"event": "Authentication failed: No module named 'fyers_apiv3.FyersModel'", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:26:58.809900Z"}
2025-07-13 04:56:58,810 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:26:58.810920Z"}
2025-07-13 04:56:58,810 [ERROR] signal_stack: {"event": "Failed to initialize Fyers connection", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:26:58.810920Z"}
2025-07-13 04:57:32,670 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.670976Z"}
2025-07-13 04:57:32,672 [INFO] signal_stack: {"event": "PRODUCTION SYSTEM VALIDATION", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.672338Z"}
2025-07-13 04:57:32,672 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.672338Z"}
2025-07-13 04:57:32,672 [INFO] signal_stack: {"event": "\u26a0\ufe0f  WARNING: This uses REAL market data and API calls", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.672338Z"}
2025-07-13 04:57:32,673 [INFO] signal_stack: {"event": "\u26a0\ufe0f  Ensure you have valid Fyers credentials", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.673357Z"}
2025-07-13 04:57:32,673 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.673357Z"}
2025-07-13 04:57:32,674 [INFO] signal_stack: {"event": "\n--- Database Connection Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.674363Z"}
2025-07-13 04:57:32,874 [INFO] app.database.connection: Database connection successful
2025-07-13 04:57:32,877 [INFO] signal_stack: {"event": "\u2713 Database connection and TimescaleDB validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.877910Z"}
2025-07-13 04:57:32,878 [INFO] signal_stack: {"event": "\u2705 Database Connection: PASSED (0.20s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.878905Z"}
2025-07-13 04:57:32,879 [INFO] signal_stack: {"event": "\n--- Database Schema Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.879921Z"}
2025-07-13 04:57:32,879 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:57:32,880 [INFO] app.database.connection: Initializing database...
2025-07-13 04:57:32,882 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:57:32,908 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:57:32,908 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:57:32,916 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:57:32,917 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:57:32,921 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:57:32,922 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:57:32,935 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:57:32,935 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:57:32,961 [INFO] signal_stack: {"event": "\u2713 Database schema validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.961125Z"}
2025-07-13 04:57:32,962 [INFO] signal_stack: {"event": "\u2705 Database Schema: PASSED (0.08s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.962120Z"}
2025-07-13 04:57:32,963 [INFO] signal_stack: {"event": "\n--- Symbol Setup Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.963121Z"}
2025-07-13 04:57:33,016 [INFO] signal_stack: {"event": "\u2713 Symbol validated: NIFTY (ID: 1)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:33.016674Z"}
2025-07-13 04:57:33,018 [INFO] signal_stack: {"event": "\u2705 Symbol Setup: PASSED (0.06s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:33.018686Z"}
2025-07-13 04:57:33,019 [INFO] signal_stack: {"event": "\n--- Fyers Authentication Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:33.018686Z"}
2025-07-13 04:57:33,019 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:27:33.019684Z"}
2025-07-13 04:57:33,746 [INFO] app.integrations.fyers.auth: {"event": "Fyers authentication successful!", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:27:33.746364Z"}
2025-07-13 04:57:33,747 [INFO] app.integrations.fyers.auth: {"event": "Using existing valid access token", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:27:33.747364Z"}
2025-07-13 04:57:33,748 [INFO] app.integrations.fyers.client: {"event": "Fyers authentication successful", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:27:33.748365Z"}
2025-07-13 04:57:33,749 [INFO] app.services.market_data_service: {"event": "Fyers API connection initialized successfully", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-12T23:27:33.748365Z"}
2025-07-13 04:57:33,932 [ERROR] app.integrations.fyers.client: {"event": "Failed to get profile: {'s': 'error', 'code': 500, 'message': 'Looks like you are passing an invalid entry'}", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:27:33.932427Z"}
2025-07-13 04:57:33,933 [ERROR] signal_stack: {"event": "Failed to retrieve Fyers profile", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:27:33.933417Z"}
2025-07-13 04:57:33,934 [ERROR] signal_stack: {"event": "\u274c Fyers Authentication: FAILED (0.91s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:27:33.934429Z"}
2025-07-13 04:57:33,934 [INFO] signal_stack: {"event": "\n--- Historical Data Fetch Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:33.934429Z"}
2025-07-13 04:57:33,935 [INFO] app.services.market_data_service: {"event": "Fetching historical data for NSE:NIFTY50-INDEX (1) from 2025-07-10 to 2025-07-13", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-12T23:27:33.935427Z"}
2025-07-13 04:57:33,935 [INFO] app.integrations.fyers.client: {"event": "Fetching historical data for NSE:NIFTY50-INDEX (1) from 2025-07-10 to 2025-07-13", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:27:33.935427Z"}
2025-07-13 04:57:34,245 [INFO] app.integrations.fyers.client: {"event": "Retrieved 0 historical records for NSE:NIFTY50-INDEX", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:27:34.245334Z"}
2025-07-13 04:57:34,246 [WARNING] app.services.market_data_service: {"event": "No historical data received for NSE:NIFTY50-INDEX", "logger": "app.services.market_data_service", "level": "warning", "timestamp": "2025-07-12T23:27:34.246336Z"}
2025-07-13 04:57:34,247 [ERROR] signal_stack: {"event": "Historical data fetch failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:27:34.247350Z"}
2025-07-13 04:57:34,248 [ERROR] signal_stack: {"event": "\u274c Historical Data Fetch: FAILED (0.31s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:27:34.248339Z"}
2025-07-13 04:57:34,248 [INFO] signal_stack: {"event": "\n--- Data Storage Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.248339Z"}
2025-07-13 04:57:34,257 [ERROR] signal_stack: {"event": "Failed to retrieve stored data", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:27:34.257705Z"}
2025-07-13 04:57:34,258 [ERROR] signal_stack: {"event": "\u274c Data Storage: FAILED (0.01s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:27:34.258704Z"}
2025-07-13 04:57:34,258 [INFO] signal_stack: {"event": "\n--- Timeframe Aggregation Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.258704Z"}
2025-07-13 04:57:34,425 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 5m from 2025-07-10 04:57:34.259718 to 2025-07-13 04:57:34.259718", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:27:34.425611Z"}
2025-07-13 04:57:34,461 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:27:34.460071Z"}
2025-07-13 04:57:34,464 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 15m from 2025-07-10 04:57:34.461082 to 2025-07-13 04:57:34.461082", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:27:34.464072Z"}
2025-07-13 04:57:34,471 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:27:34.471570Z"}
2025-07-13 04:57:34,475 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 30m from 2025-07-10 04:57:34.471570 to 2025-07-13 04:57:34.471570", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:27:34.475565Z"}
2025-07-13 04:57:34,481 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:27:34.481580Z"}
2025-07-13 04:57:34,485 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 1h from 2025-07-10 04:57:34.481580 to 2025-07-13 04:57:34.481580", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:27:34.484570Z"}
2025-07-13 04:57:34,491 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:27:34.491577Z"}
2025-07-13 04:57:34,507 [INFO] signal_stack: {"event": "\u2713 Timeframe aggregation validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.507575Z"}
2025-07-13 04:57:34,508 [INFO] signal_stack: {"event": "  5m: 158 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.508580Z"}
2025-07-13 04:57:34,508 [INFO] signal_stack: {"event": "  15m: 54 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.508580Z"}
2025-07-13 04:57:34,509 [INFO] signal_stack: {"event": "  30m: 28 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.509565Z"}
2025-07-13 04:57:34,509 [INFO] signal_stack: {"event": "  1h: 14 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.509565Z"}
2025-07-13 04:57:34,510 [INFO] signal_stack: {"event": "\u2705 Timeframe Aggregation: PASSED (0.25s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.510570Z"}
2025-07-13 04:57:34,511 [INFO] signal_stack: {"event": "\n--- Real-time Data Pipeline Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.511573Z"}
2025-07-13 04:57:34,521 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:27:34.521995Z"}
2025-07-13 04:57:35,189 [INFO] app.integrations.fyers.auth: {"event": "Fyers authentication successful!", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:27:35.189575Z"}
2025-07-13 04:57:35,190 [INFO] app.integrations.fyers.auth: {"event": "Using existing valid access token", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:27:35.190820Z"}
2025-07-13 04:57:35,191 [INFO] app.integrations.fyers.client: {"event": "Fyers authentication successful", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:27:35.191832Z"}
2025-07-13 04:57:35,191 [INFO] app.services.market_data_service: {"event": "Fyers API connection initialized successfully", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-12T23:27:35.191832Z"}
2025-07-13 04:57:35,192 [INFO] app.services.realtime_pipeline: {"event": "Real-time data pipeline initialized successfully", "logger": "app.services.realtime_pipeline", "level": "info", "timestamp": "2025-07-12T23:27:35.192831Z"}
2025-07-13 04:57:35,192 [INFO] app.services.realtime_pipeline: {"event": "Starting real-time pipeline for 1 symbols", "logger": "app.services.realtime_pipeline", "level": "info", "timestamp": "2025-07-12T23:27:35.192831Z"}
2025-07-13 04:57:36,851 [INFO] websocket: Websocket connected
2025-07-13 04:57:37,202 [INFO] app.integrations.fyers.websocket_client: {"event": "WebSocket client started", "logger": "app.integrations.fyers.websocket_client", "level": "info", "timestamp": "2025-07-12T23:27:37.202171Z"}
2025-07-13 04:57:37,203 [ERROR] app.integrations.fyers.websocket_client: {"event": "WebSocket not connected. Cannot subscribe to symbols.", "logger": "app.integrations.fyers.websocket_client", "level": "error", "timestamp": "2025-07-12T23:27:37.203169Z"}
2025-07-13 04:57:37,203 [ERROR] app.services.realtime_pipeline: {"event": "Failed to start real-time data streaming", "logger": "app.services.realtime_pipeline", "level": "error", "timestamp": "2025-07-12T23:27:37.203169Z"}
2025-07-13 04:57:37,204 [ERROR] signal_stack: {"event": "Pipeline start failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:27:37.204166Z"}
2025-07-13 04:57:37,205 [ERROR] signal_stack: {"event": "\u274c Real-time Data Pipeline: FAILED (2.69s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:27:37.205151Z"}
2025-07-13 04:57:37,205 [INFO] signal_stack: {"event": "\n--- Data Integrity Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.205511Z"}
2025-07-13 04:57:37,218 [INFO] signal_stack: {"event": "\u2713 Data integrity validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.218048Z"}
2025-07-13 04:57:37,220 [INFO] signal_stack: {"event": "\u2705 Data Integrity: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.219050Z"}
2025-07-13 04:57:37,221 [INFO] signal_stack: {"event": "\n--- Performance Metrics Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.221032Z"}
2025-07-13 04:57:37,227 [INFO] signal_stack: {"event": "\u2713 Performance validated: Query took 0.00s for 0 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.226017Z"}
2025-07-13 04:57:37,227 [INFO] signal_stack: {"event": "\u2705 Performance Metrics: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.227380Z"}
2025-07-13 04:57:37,227 [INFO] signal_stack: {"event": "\n================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.227380Z"}
2025-07-13 04:57:37,228 [INFO] signal_stack: {"event": "VALIDATION REPORT", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.228392Z"}
2025-07-13 04:57:37,228 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.228392Z"}
2025-07-13 04:57:37,229 [INFO] signal_stack: {"event": "Overall Success Rate: 60.0% (6/10)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.229406Z"}
2025-07-13 04:57:37,229 [INFO] signal_stack: {"event": "\u2705 PASS Database Connection (0.20s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.229406Z"}
2025-07-13 04:57:37,230 [INFO] signal_stack: {"event": "\u2705 PASS Database Schema (0.08s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.230402Z"}
2025-07-13 04:57:37,230 [INFO] signal_stack: {"event": "\u2705 PASS Symbol Setup (0.06s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.230402Z"}
2025-07-13 04:57:37,231 [INFO] signal_stack: {"event": "\u274c FAIL Fyers Authentication (0.91s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.231401Z"}
2025-07-13 04:57:37,231 [INFO] signal_stack: {"event": "\u274c FAIL Historical Data Fetch (0.31s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.231401Z"}
2025-07-13 04:57:37,232 [INFO] signal_stack: {"event": "\u274c FAIL Data Storage (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.232403Z"}
2025-07-13 04:57:37,232 [INFO] signal_stack: {"event": "\u2705 PASS Timeframe Aggregation (0.25s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.232403Z"}
2025-07-13 04:57:37,233 [INFO] signal_stack: {"event": "\u274c FAIL Real-time Data Pipeline (2.69s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.233390Z"}
2025-07-13 04:57:37,234 [INFO] signal_stack: {"event": "\u2705 PASS Data Integrity (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.234396Z"}
2025-07-13 04:57:37,235 [INFO] signal_stack: {"event": "\u2705 PASS Performance Metrics (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.234396Z"}
2025-07-13 04:57:37,238 [INFO] signal_stack: {"event": "Detailed report saved to: validation_report_20250713_045737.json", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.237391Z"}
2025-07-13 04:57:37,239 [INFO] signal_stack: {"event": "\u274c MULTIPLE VALIDATIONS FAILED - SYSTEM NOT READY", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.238391Z"}
2025-07-13 04:57:37,793 [ERROR] app.integrations.fyers.websocket_client: {"event": "WebSocket connection error: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'", "logger": "app.integrations.fyers.websocket_client", "level": "error", "timestamp": "2025-07-12T23:27:37.793581Z"}
2025-07-13 15:56:38,220 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:26:38.220154Z"}
2025-07-13 15:56:38,482 [INFO] app.database.connection: Database connection successful
2025-07-13 15:56:38,483 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 15:56:38,483 [INFO] app.database.connection: Initializing database...
2025-07-13 15:56:38,490 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 15:56:38,572 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 15:56:38,573 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 15:56:38,584 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 15:56:38,585 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 15:56:38,592 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 15:56:38,592 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 15:56:38,603 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 15:56:38,603 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 15:56:38,604 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:26:38.604490Z"}
2025-07-13 15:56:38,605 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:26:38.604490Z"}
2025-07-13 15:57:08,214 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:27:08.214343Z"}
2025-07-13 15:57:08,407 [INFO] app.database.connection: Database connection successful
2025-07-13 15:57:08,407 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 15:57:08,408 [INFO] app.database.connection: Initializing database...
2025-07-13 15:57:08,411 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 15:57:08,440 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 15:57:08,440 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 15:57:08,449 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 15:57:08,450 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 15:57:08,456 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 15:57:08,456 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 15:57:08,477 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 15:57:08,478 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 15:57:08,478 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:27:08.478969Z"}
2025-07-13 15:57:08,479 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:27:08.479971Z"}
2025-07-13 15:58:13,957 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:28:13.957725Z"}
2025-07-13 15:58:18,350 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:28:18.350169Z"}
2025-07-13 15:58:18,641 [INFO] app.database.connection: Database connection successful
2025-07-13 15:58:18,641 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 15:58:18,641 [INFO] app.database.connection: Initializing database...
2025-07-13 15:58:18,645 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 15:58:18,676 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 15:58:18,678 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 15:58:18,687 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 15:58:18,688 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 15:58:18,694 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 15:58:18,694 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 15:58:18,703 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 15:58:18,703 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 15:58:18,704 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:28:18.704451Z"}
2025-07-13 15:58:18,705 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:28:18.705430Z"}
2025-07-13 15:58:19,389 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:28:19.389508Z"}
2025-07-13 15:58:23,620 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:28:23.620456Z"}
2025-07-13 15:58:23,812 [INFO] app.database.connection: Database connection successful
2025-07-13 15:58:23,813 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 15:58:23,813 [INFO] app.database.connection: Initializing database...
2025-07-13 15:58:23,817 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 15:58:23,848 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 15:58:23,849 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 15:58:23,858 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 15:58:23,858 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 15:58:23,866 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 15:58:23,867 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 15:58:23,881 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 15:58:23,881 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 15:58:23,882 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:28:23.881989Z"}
2025-07-13 15:58:23,882 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:28:23.882999Z"}
2025-07-13 15:59:55,500 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:29:55.500475Z"}
2025-07-13 15:59:59,702 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:29:59.702864Z"}
2025-07-13 15:59:59,888 [INFO] app.database.connection: Database connection successful
2025-07-13 15:59:59,888 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 15:59:59,889 [INFO] app.database.connection: Initializing database...
2025-07-13 15:59:59,893 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 15:59:59,923 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 15:59:59,924 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 15:59:59,933 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 15:59:59,934 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 15:59:59,940 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 15:59:59,941 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 15:59:59,948 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 15:59:59,949 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 15:59:59,949 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:29:59.949450Z"}
2025-07-13 15:59:59,950 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:29:59.950448Z"}
2025-07-13 16:00:29,909 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:30:29.909041Z"}
2025-07-13 16:00:35,477 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:30:35.476461Z"}
2025-07-13 16:00:35,859 [INFO] app.database.connection: Database connection successful
2025-07-13 16:00:35,863 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 16:00:35,866 [INFO] app.database.connection: Initializing database...
2025-07-13 16:00:35,872 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 16:00:35,924 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 16:00:35,932 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 16:00:35,946 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 16:00:35,955 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 16:00:35,970 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 16:00:35,978 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 16:00:35,995 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 16:00:35,999 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 16:00:36,005 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:30:36.005565Z"}
2025-07-13 16:00:36,007 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:30:36.007568Z"}
2025-07-13 16:01:05,632 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:31:05.632204Z"}
2025-07-13 16:01:09,696 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:31:09.695372Z"}
2025-07-13 16:01:09,881 [INFO] app.database.connection: Database connection successful
2025-07-13 16:01:09,882 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 16:01:09,882 [INFO] app.database.connection: Initializing database...
2025-07-13 16:01:09,886 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 16:01:09,917 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 16:01:09,917 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 16:01:09,926 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 16:01:09,927 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 16:01:09,933 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 16:01:09,933 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 16:01:09,941 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 16:01:09,942 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 16:01:09,942 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:31:09.942654Z"}
2025-07-13 16:01:09,943 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:31:09.943652Z"}
2025-07-13 16:02:01,889 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:32:01.889877Z"}
2025-07-13 16:02:06,409 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:32:06.408869Z"}
2025-07-13 16:02:06,593 [INFO] app.database.connection: Database connection successful
2025-07-13 16:02:06,593 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 16:02:06,594 [INFO] app.database.connection: Initializing database...
2025-07-13 16:02:06,598 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 16:02:06,623 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 16:02:06,624 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 16:02:06,632 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 16:02:06,633 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 16:02:06,638 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 16:02:06,639 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 16:02:06,666 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 16:02:06,667 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 16:02:06,667 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:32:06.667196Z"}
2025-07-13 16:02:06,668 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:32:06.668194Z"}
2025-07-13 16:02:36,544 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:32:36.544450Z"}
2025-07-13 16:04:55,504 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:34:55.504345Z"}
2025-07-13 16:20:56,066 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:50:56.066400Z"}
2025-07-13 16:20:56,067 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:50:56.066400Z"}
2025-07-13 16:20:56,067 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:50:56.067397Z"}
2025-07-13 16:20:56,067 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:50:56.067397Z"}
2025-07-13 16:20:56,255 [INFO] app.database.connection: Database connection successful
2025-07-13 16:20:56,256 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:50:56.255212Z"}
2025-07-13 16:20:56,256 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:50:56.256219Z"}
2025-07-13 16:20:56,257 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 16:20:56,257 [INFO] app.database.connection: Initializing database...
2025-07-13 16:20:56,260 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 16:20:56,287 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 16:20:56,288 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 16:20:56,295 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 16:20:56,296 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 16:20:56,301 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 16:20:56,301 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 16:20:56,315 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 16:20:56,316 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 16:20:56,316 [INFO] signal_stack: {"event": "\u2713 Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:50:56.316795Z"}
2025-07-13 16:20:56,317 [INFO] signal_stack: {"event": "\nStep 3: Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:50:56.317823Z"}
2025-07-13 16:20:56,317 [INFO] signal_stack: {"event": "Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:50:56.317823Z"}
2025-07-13 16:20:56,372 [INFO] signal_stack: {"event": "\u2713 NIFTY symbol already exists", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:50:56.372778Z"}
2025-07-13 16:20:56,373 [INFO] signal_stack: {"event": "\nStep 4: Fetching historical data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:50:56.373780Z"}
2025-07-13 16:20:56,374 [INFO] signal_stack: {"event": "Fetching historical data for NIFTY...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:50:56.374783Z"}
2025-07-13 16:20:56,385 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:50:56.385624Z"}
2025-07-13 16:20:57,541 [INFO] app.integrations.fyers.auth: {"event": "Fyers authentication successful!", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-13T10:50:57.541346Z"}
2025-07-13 16:20:57,542 [INFO] app.integrations.fyers.auth: {"event": "Using existing valid access token", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-13T10:50:57.542346Z"}
2025-07-13 16:20:57,543 [INFO] app.integrations.fyers.client: {"event": "Fyers authentication successful", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:50:57.543857Z"}
2025-07-13 16:20:57,544 [INFO] app.services.market_data_service: {"event": "Fyers API connection initialized successfully", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T10:50:57.544865Z"}
2025-07-13 16:20:57,544 [INFO] app.services.market_data_service: {"event": "Fetching historical data for NSE:NIFTY50-INDEX (1) from 2025-07-06 to 2025-07-13", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T10:50:57.544865Z"}
2025-07-13 16:20:57,545 [INFO] app.integrations.fyers.client: {"event": "Fetching historical data for NSE:NIFTY50-INDEX (1) from 2025-07-06 to 2025-07-13", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:50:57.545881Z"}
2025-07-13 16:20:57,796 [INFO] app.integrations.fyers.client: {"event": "Retrieved 0 historical records for NSE:NIFTY50-INDEX", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:50:57.795516Z"}
2025-07-13 16:20:57,796 [WARNING] app.services.market_data_service: {"event": "No historical data received for NSE:NIFTY50-INDEX", "logger": "app.services.market_data_service", "level": "warning", "timestamp": "2025-07-13T10:50:57.796492Z"}
2025-07-13 16:20:57,797 [ERROR] signal_stack: {"event": "\u2717 Failed to fetch historical data", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-13T10:50:57.797518Z"}
2025-07-13 16:21:09,322 [INFO] signal_stack: {"event": "============================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:09.322029Z"}
2025-07-13 16:21:09,324 [INFO] signal_stack: {"event": "FYERS API INTEGRATION TESTS", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:09.324010Z"}
2025-07-13 16:21:09,324 [INFO] signal_stack: {"event": "============================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:09.324010Z"}
2025-07-13 16:21:09,325 [INFO] signal_stack: {"event": "\n--- Authentication Test ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:09.325042Z"}
2025-07-13 16:21:09,325 [INFO] signal_stack: {"event": "Testing Fyers authentication...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:09.325042Z"}
2025-07-13 16:21:09,337 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:09.337364Z"}
2025-07-13 16:21:09,944 [INFO] app.integrations.fyers.auth: {"event": "Fyers authentication successful!", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-13T10:51:09.944929Z"}
2025-07-13 16:21:09,945 [INFO] app.integrations.fyers.auth: {"event": "Using existing valid access token", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-13T10:51:09.945909Z"}
2025-07-13 16:21:09,946 [INFO] app.integrations.fyers.client: {"event": "Fyers authentication successful", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:09.946910Z"}
2025-07-13 16:21:09,946 [INFO] app.services.market_data_service: {"event": "Fyers API connection initialized successfully", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T10:51:09.946910Z"}
2025-07-13 16:21:09,947 [INFO] signal_stack: {"event": "\u2713 Fyers authentication successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:09.947913Z"}
2025-07-13 16:21:10,751 [ERROR] app.integrations.fyers.client: {"event": "Failed to get profile: {'s': 'error', 'code': 500, 'message': 'Looks like you are passing an invalid entry'}", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-13T10:51:10.751853Z"}
2025-07-13 16:21:10,752 [WARNING] signal_stack: {"event": "\u26a0 Failed to retrieve profile", "logger": "signal_stack", "level": "warning", "timestamp": "2025-07-13T10:51:10.752860Z"}
2025-07-13 16:21:10,753 [INFO] signal_stack: {"event": "\n--- Live Quotes Test ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:10.753849Z"}
2025-07-13 16:21:10,753 [INFO] signal_stack: {"event": "Testing live quotes...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:10.753849Z"}
2025-07-13 16:21:10,766 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:10.765197Z"}
2025-07-13 16:21:11,468 [INFO] app.integrations.fyers.auth: {"event": "Fyers authentication successful!", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-13T10:51:11.468691Z"}
2025-07-13 16:21:11,469 [INFO] app.integrations.fyers.auth: {"event": "Using existing valid access token", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-13T10:51:11.469995Z"}
2025-07-13 16:21:11,471 [INFO] app.integrations.fyers.client: {"event": "Fyers authentication successful", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:11.471680Z"}
2025-07-13 16:21:11,471 [INFO] app.services.market_data_service: {"event": "Fyers API connection initialized successfully", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T10:51:11.471680Z"}
2025-07-13 16:21:11,472 [INFO] app.integrations.fyers.client: {"event": "Fetching quotes for 3 symbols in 1 batches", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:11.472663Z"}
2025-07-13 16:21:11,684 [INFO] app.integrations.fyers.client: {"event": "Successfully fetched quotes for 0 symbols", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:11.684319Z"}
2025-07-13 16:21:11,685 [ERROR] signal_stack: {"event": "\u2717 No quotes retrieved", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-13T10:51:11.685348Z"}
2025-07-13 16:21:11,686 [INFO] signal_stack: {"event": "\n--- Historical Data Test ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:11.686341Z"}
2025-07-13 16:21:11,686 [INFO] signal_stack: {"event": "Testing historical data fetching...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:11.686341Z"}
2025-07-13 16:21:11,697 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:11.696888Z"}
2025-07-13 16:21:12,492 [INFO] app.integrations.fyers.auth: {"event": "Fyers authentication successful!", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-13T10:51:12.492358Z"}
2025-07-13 16:21:12,493 [INFO] app.integrations.fyers.auth: {"event": "Using existing valid access token", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-13T10:51:12.493370Z"}
2025-07-13 16:21:12,494 [INFO] app.integrations.fyers.client: {"event": "Fyers authentication successful", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:12.494371Z"}
2025-07-13 16:21:12,495 [INFO] app.services.market_data_service: {"event": "Fyers API connection initialized successfully", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T10:51:12.495358Z"}
2025-07-13 16:21:12,720 [INFO] signal_stack: {"event": "Creating symbol: BANKNIFTY", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:12.720109Z"}
2025-07-13 16:21:12,720 [INFO] app.services.data_service: {"event": "Creating symbol: BANKNIFTY", "logger": "app.services.data_service", "level": "info", "timestamp": "2025-07-13T10:51:12.720109Z"}
2025-07-13 16:21:12,737 [INFO] signal_stack: {"event": "Creating symbol: RELIANCE", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:12.736119Z"}
2025-07-13 16:21:12,737 [INFO] app.services.data_service: {"event": "Creating symbol: RELIANCE", "logger": "app.services.data_service", "level": "info", "timestamp": "2025-07-13T10:51:12.737309Z"}
2025-07-13 16:21:12,743 [INFO] app.services.market_data_service: {"event": "Fetching historical data for NSE:NIFTY50-INDEX (1) from 2025-07-06 to 2025-07-13", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T10:51:12.743660Z"}
2025-07-13 16:21:12,744 [INFO] app.integrations.fyers.client: {"event": "Fetching historical data for NSE:NIFTY50-INDEX (1) from 2025-07-06 to 2025-07-13", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:12.744684Z"}
2025-07-13 16:21:13,010 [INFO] app.integrations.fyers.client: {"event": "Retrieved 0 historical records for NSE:NIFTY50-INDEX", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:13.010537Z"}
2025-07-13 16:21:13,011 [WARNING] app.services.market_data_service: {"event": "No historical data received for NSE:NIFTY50-INDEX", "logger": "app.services.market_data_service", "level": "warning", "timestamp": "2025-07-13T10:51:13.011537Z"}
2025-07-13 16:21:13,011 [ERROR] signal_stack: {"event": "\u2717 Historical data fetch failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-13T10:51:13.011537Z"}
2025-07-13 16:21:13,013 [INFO] signal_stack: {"event": "\n--- Bulk Historical Fetch Test ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:13.013527Z"}
2025-07-13 16:21:13,013 [INFO] signal_stack: {"event": "Testing bulk historical data fetching...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:13.013527Z"}
2025-07-13 16:21:13,024 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:13.024538Z"}
2025-07-13 16:21:13,311 [INFO] app.integrations.fyers.auth: {"event": "Fyers authentication successful!", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-13T10:51:13.311045Z"}
2025-07-13 16:21:13,312 [INFO] app.integrations.fyers.auth: {"event": "Using existing valid access token", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-13T10:51:13.312043Z"}
2025-07-13 16:21:13,313 [INFO] app.integrations.fyers.client: {"event": "Fyers authentication successful", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:13.313044Z"}
2025-07-13 16:21:13,314 [INFO] app.services.market_data_service: {"event": "Fyers API connection initialized successfully", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T10:51:13.314051Z"}
2025-07-13 16:21:13,321 [INFO] app.services.market_data_service: {"event": "Processing NSE:NIFTY50-INDEX...", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T10:51:13.321783Z"}
2025-07-13 16:21:13,321 [INFO] app.services.market_data_service: {"event": "Fetching historical data for NSE:NIFTY50-INDEX (1) from 2025-07-10 to 2025-07-13", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T10:51:13.321783Z"}
2025-07-13 16:21:13,322 [INFO] app.integrations.fyers.client: {"event": "Fetching historical data for NSE:NIFTY50-INDEX (1) from 2025-07-10 to 2025-07-13", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:13.322771Z"}
2025-07-13 16:21:13,430 [INFO] app.integrations.fyers.client: {"event": "Retrieved 0 historical records for NSE:NIFTY50-INDEX", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:13.430157Z"}
2025-07-13 16:21:13,431 [WARNING] app.services.market_data_service: {"event": "No historical data received for NSE:NIFTY50-INDEX", "logger": "app.services.market_data_service", "level": "warning", "timestamp": "2025-07-13T10:51:13.431637Z"}
2025-07-13 16:21:13,535 [INFO] app.services.market_data_service: {"event": "Processing NSE:BANKNIFTY-INDEX...", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T10:51:13.535994Z"}
2025-07-13 16:21:13,537 [INFO] app.services.market_data_service: {"event": "Fetching historical data for NSE:BANKNIFTY-INDEX (1) from 2025-07-10 to 2025-07-13", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T10:51:13.537019Z"}
2025-07-13 16:21:13,538 [INFO] app.integrations.fyers.client: {"event": "Fetching historical data for NSE:BANKNIFTY-INDEX (1) from 2025-07-10 to 2025-07-13", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:13.538040Z"}
2025-07-13 16:21:13,699 [ERROR] app.integrations.fyers.client: {"event": "Failed to get historical data for NSE:BANKNIFTY-INDEX: {'code': -300, 'message': 'Invalid symbol provided', 's': 'error'}", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-13T10:51:13.698509Z"}
2025-07-13 16:21:13,700 [WARNING] app.services.market_data_service: {"event": "No historical data received for NSE:BANKNIFTY-INDEX", "logger": "app.services.market_data_service", "level": "warning", "timestamp": "2025-07-13T10:51:13.700515Z"}
2025-07-13 16:21:13,814 [INFO] app.services.market_data_service: {"event": "Processing NSE:RELIANCE-EQ...", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T10:51:13.814046Z"}
2025-07-13 16:21:13,814 [INFO] app.services.market_data_service: {"event": "Fetching historical data for NSE:RELIANCE-EQ (1) from 2025-07-10 to 2025-07-13", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T10:51:13.814858Z"}
2025-07-13 16:21:13,815 [INFO] app.integrations.fyers.client: {"event": "Fetching historical data for NSE:RELIANCE-EQ (1) from 2025-07-10 to 2025-07-13", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:13.815852Z"}
2025-07-13 16:21:14,544 [INFO] app.integrations.fyers.client: {"event": "Retrieved 0 historical records for NSE:RELIANCE-EQ", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T10:51:14.544293Z"}
2025-07-13 16:21:14,545 [WARNING] app.services.market_data_service: {"event": "No historical data received for NSE:RELIANCE-EQ", "logger": "app.services.market_data_service", "level": "warning", "timestamp": "2025-07-13T10:51:14.545278Z"}
2025-07-13 16:21:14,658 [INFO] app.services.market_data_service: {"event": "Bulk fetch completed: 0/3 symbols successful", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T10:51:14.658905Z"}
2025-07-13 16:21:14,659 [INFO] signal_stack: {"event": "\u2713 Bulk fetch completed: 0/3 symbols successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:14.659896Z"}
2025-07-13 16:21:14,661 [INFO] signal_stack: {"event": "\n============================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:14.661913Z"}
2025-07-13 16:21:14,661 [INFO] signal_stack: {"event": "TEST RESULTS SUMMARY", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:14.661913Z"}
2025-07-13 16:21:14,662 [INFO] signal_stack: {"event": "============================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:14.662913Z"}
2025-07-13 16:21:14,663 [INFO] signal_stack: {"event": "Authentication: PASS", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:14.663239Z"}
2025-07-13 16:21:14,663 [INFO] signal_stack: {"event": "Live Quotes: FAIL", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:14.663239Z"}
2025-07-13 16:21:14,664 [INFO] signal_stack: {"event": "Historical Data: FAIL", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:14.664256Z"}
2025-07-13 16:21:14,664 [INFO] signal_stack: {"event": "Bulk Historical Fetch: FAIL", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:14.664256Z"}
2025-07-13 16:21:14,665 [INFO] signal_stack: {"event": "\nOverall: 1/4 tests passed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:51:14.665268Z"}
2025-07-13 16:59:55,069 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T11:29:55.068862Z"}
2025-07-13 16:59:55,315 [INFO] app.database.connection: Database connection successful
2025-07-13 16:59:55,316 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 16:59:55,316 [INFO] app.database.connection: Initializing database...
2025-07-13 16:59:55,321 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 16:59:55,350 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 16:59:55,351 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 16:59:55,368 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 16:59:55,369 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 16:59:55,377 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 16:59:55,379 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 16:59:55,393 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 16:59:55,394 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 16:59:55,395 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T11:29:55.395180Z"}
2025-07-13 16:59:55,395 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T11:29:55.395180Z"}
2025-07-13 17:00:26,786 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T11:30:26.786197Z"}
2025-07-13 17:00:26,971 [INFO] app.database.connection: Database connection successful
2025-07-13 17:00:26,972 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 17:00:26,972 [INFO] app.database.connection: Initializing database...
2025-07-13 17:00:26,975 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 17:00:27,005 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 17:00:27,005 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 17:00:27,014 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 17:00:27,015 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 17:00:27,021 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 17:00:27,022 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 17:00:27,031 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 17:00:27,031 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 17:00:27,032 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T11:30:27.032913Z"}
2025-07-13 17:00:27,032 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T11:30:27.032913Z"}
2025-07-13 17:00:50,293 [INFO] app.database.connection: Database connection successful
2025-07-13 19:39:38,277 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T14:09:38.277146Z"}
2025-07-13 19:39:38,278 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T14:09:38.278150Z"}
2025-07-13 19:39:38,278 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T14:09:38.278150Z"}
2025-07-13 19:39:38,279 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T14:09:38.279153Z"}
2025-07-13 19:39:38,472 [INFO] app.database.connection: Database connection successful
2025-07-13 19:39:38,473 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T14:09:38.473093Z"}
2025-07-13 19:39:38,473 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T14:09:38.473093Z"}
2025-07-13 19:39:38,474 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 19:39:38,474 [INFO] app.database.connection: Initializing database...
2025-07-13 19:39:38,477 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 19:39:38,505 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 19:39:38,506 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 19:39:38,514 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 19:39:38,515 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 19:39:38,522 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 19:39:38,522 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 19:39:38,534 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 19:39:38,534 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 19:39:38,535 [INFO] signal_stack: {"event": "\u2713 Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T14:09:38.535237Z"}
2025-07-13 19:39:38,536 [INFO] signal_stack: {"event": "\nStep 3: Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T14:09:38.535237Z"}
2025-07-13 19:39:38,536 [INFO] signal_stack: {"event": "Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T14:09:38.536237Z"}
2025-07-13 19:39:38,595 [INFO] signal_stack: {"event": "\u2713 NIFTY symbol already exists", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T14:09:38.595361Z"}
2025-07-13 19:39:38,597 [INFO] signal_stack: {"event": "\nStep 4: Fetching historical data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T14:09:38.596367Z"}
2025-07-13 19:39:38,597 [INFO] signal_stack: {"event": "Fetching historical data for NIFTY...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T14:09:38.597392Z"}
2025-07-13 19:39:38,608 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T14:09:38.608942Z"}
2025-07-13 19:39:39,451 [INFO] app.integrations.fyers.auth: {"event": "Fyers authentication successful!", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-13T14:09:39.451595Z"}
2025-07-13 19:39:39,451 [INFO] app.integrations.fyers.auth: {"event": "Using existing valid access token", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-13T14:09:39.451595Z"}
2025-07-13 19:39:39,452 [INFO] app.integrations.fyers.client: {"event": "Fyers authentication successful", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T14:09:39.452904Z"}
2025-07-13 19:39:39,453 [INFO] app.services.market_data_service: {"event": "Fyers API connection initialized successfully", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T14:09:39.453924Z"}
2025-07-13 19:39:39,453 [INFO] app.services.market_data_service: {"event": "Fetching historical data for NSE:NIFTY50-INDEX (1) from 2025-07-06 to 2025-07-13", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T14:09:39.453924Z"}
2025-07-13 19:39:39,453 [INFO] app.integrations.fyers.client: {"event": "Fetching historical data for NSE:NIFTY50-INDEX (1) from 2025-07-06 to 2025-07-13", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T14:09:39.453924Z"}
2025-07-13 19:39:39,824 [INFO] app.integrations.fyers.client: {"event": "Retrieved 0 historical records for NSE:NIFTY50-INDEX", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T14:09:39.824331Z"}
2025-07-13 19:39:39,825 [WARNING] app.services.market_data_service: {"event": "No historical data received for NSE:NIFTY50-INDEX", "logger": "app.services.market_data_service", "level": "warning", "timestamp": "2025-07-13T14:09:39.825333Z"}
2025-07-13 19:39:39,826 [ERROR] signal_stack: {"event": "\u2717 Failed to fetch historical data", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-13T14:09:39.826831Z"}
2025-07-13 21:36:20,523 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:06:20.523043Z"}
2025-07-13 21:36:26,278 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:06:26.278609Z"}
2025-07-13 21:36:26,470 [INFO] app.database.connection: Database connection successful
2025-07-13 21:36:26,471 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 21:36:26,471 [INFO] app.database.connection: Initializing database...
2025-07-13 21:36:26,474 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 21:36:26,503 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 21:36:26,503 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 21:36:26,512 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 21:36:26,513 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 21:36:26,519 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 21:36:26,519 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 21:36:26,548 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 21:36:26,549 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 21:36:26,549 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:06:26.549481Z"}
2025-07-13 21:36:26,550 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:06:26.550472Z"}
2025-07-13 21:38:17,010 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:17.009397Z"}
2025-07-13 21:38:17,010 [INFO] signal_stack: {"event": "PRODUCTION SYSTEM VALIDATION", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:17.010690Z"}
2025-07-13 21:38:17,010 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:17.010690Z"}
2025-07-13 21:38:17,011 [INFO] signal_stack: {"event": "\u26a0\ufe0f  WARNING: This uses REAL market data and API calls", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:17.011700Z"}
2025-07-13 21:38:17,011 [INFO] signal_stack: {"event": "\u26a0\ufe0f  Ensure you have valid Fyers credentials", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:17.011700Z"}
2025-07-13 21:38:17,011 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:17.011700Z"}
2025-07-13 21:38:17,012 [INFO] signal_stack: {"event": "\n--- Database Connection Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:17.012715Z"}
2025-07-13 21:38:17,196 [INFO] app.database.connection: Database connection successful
2025-07-13 21:38:17,200 [INFO] signal_stack: {"event": "\u2713 Database connection and TimescaleDB validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:17.200974Z"}
2025-07-13 21:38:17,202 [INFO] signal_stack: {"event": "\u2705 Database Connection: PASSED (0.19s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:17.202373Z"}
2025-07-13 21:38:17,202 [INFO] signal_stack: {"event": "\n--- Database Schema Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:17.202958Z"}
2025-07-13 21:38:17,202 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 21:38:17,204 [INFO] app.database.connection: Initializing database...
2025-07-13 21:38:17,206 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 21:38:17,233 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 21:38:17,234 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 21:38:17,242 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 21:38:17,243 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 21:38:17,248 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 21:38:17,249 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 21:38:17,270 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 21:38:17,271 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 21:38:17,311 [INFO] signal_stack: {"event": "\u2713 Database schema validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:17.311827Z"}
2025-07-13 21:38:17,313 [INFO] signal_stack: {"event": "\u2705 Database Schema: PASSED (0.11s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:17.312822Z"}
2025-07-13 21:38:17,313 [INFO] signal_stack: {"event": "\n--- Symbol Setup Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:17.313826Z"}
2025-07-13 21:38:17,368 [INFO] signal_stack: {"event": "\u2713 Symbol validated: NIFTY (ID: 1)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:17.368141Z"}
2025-07-13 21:38:17,370 [INFO] signal_stack: {"event": "\u2705 Symbol Setup: PASSED (0.06s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:17.370116Z"}
2025-07-13 21:38:17,370 [INFO] signal_stack: {"event": "\n--- Fyers Authentication Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:17.370116Z"}
2025-07-13 21:38:17,371 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T16:08:17.371116Z"}
2025-07-13 21:38:18,001 [INFO] app.integrations.fyers.auth: {"event": "Fyers authentication successful!", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-13T16:08:18.001243Z"}
2025-07-13 21:38:18,002 [INFO] app.integrations.fyers.auth: {"event": "Using existing valid access token", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-13T16:08:18.002226Z"}
2025-07-13 21:38:18,003 [INFO] app.integrations.fyers.client: {"event": "Fyers authentication successful", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T16:08:18.003790Z"}
2025-07-13 21:38:18,003 [INFO] app.services.market_data_service: {"event": "Fyers API connection initialized successfully", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T16:08:18.003790Z"}
2025-07-13 21:38:18,208 [ERROR] app.integrations.fyers.client: {"event": "Failed to get profile: {'s': 'error', 'code': 500, 'message': 'Looks like you are passing an invalid entry'}", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-13T16:08:18.208743Z"}
2025-07-13 21:38:18,209 [ERROR] signal_stack: {"event": "Failed to retrieve Fyers profile", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-13T16:08:18.209733Z"}
2025-07-13 21:38:18,210 [ERROR] signal_stack: {"event": "\u274c Fyers Authentication: FAILED (0.84s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-13T16:08:18.210716Z"}
2025-07-13 21:38:18,210 [INFO] signal_stack: {"event": "\n--- Historical Data Fetch Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:18.210716Z"}
2025-07-13 21:38:18,211 [INFO] app.services.market_data_service: {"event": "Fetching historical data for NSE:NIFTY50-INDEX (1) from 2025-07-10 to 2025-07-13", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T16:08:18.211730Z"}
2025-07-13 21:38:18,212 [INFO] app.integrations.fyers.client: {"event": "Fetching historical data for NSE:NIFTY50-INDEX (1) from 2025-07-10 to 2025-07-13", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T16:08:18.212725Z"}
2025-07-13 21:38:18,413 [INFO] app.integrations.fyers.client: {"event": "Retrieved 0 historical records for NSE:NIFTY50-INDEX", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T16:08:18.413468Z"}
2025-07-13 21:38:18,414 [WARNING] app.services.market_data_service: {"event": "No historical data received for NSE:NIFTY50-INDEX", "logger": "app.services.market_data_service", "level": "warning", "timestamp": "2025-07-13T16:08:18.413468Z"}
2025-07-13 21:38:18,414 [ERROR] signal_stack: {"event": "Historical data fetch failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-13T16:08:18.414499Z"}
2025-07-13 21:38:18,415 [ERROR] signal_stack: {"event": "\u274c Historical Data Fetch: FAILED (0.20s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-13T16:08:18.415486Z"}
2025-07-13 21:38:18,415 [INFO] signal_stack: {"event": "\n--- Data Storage Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:18.415486Z"}
2025-07-13 21:38:18,452 [ERROR] signal_stack: {"event": "Failed to retrieve stored data", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-13T16:08:18.452316Z"}
2025-07-13 21:38:18,453 [ERROR] signal_stack: {"event": "\u274c Data Storage: FAILED (0.04s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-13T16:08:18.453330Z"}
2025-07-13 21:38:18,454 [INFO] signal_stack: {"event": "\n--- Timeframe Aggregation Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:18.454330Z"}
2025-07-13 21:38:18,623 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 5m from 2025-07-10 21:38:18.455330 to 2025-07-13 21:38:18.455330", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-13T16:08:18.623943Z"}
2025-07-13 21:38:18,662 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-13T16:08:18.662354Z"}
2025-07-13 21:38:18,665 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 15m from 2025-07-10 21:38:18.662354 to 2025-07-13 21:38:18.662354", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-13T16:08:18.665744Z"}
2025-07-13 21:38:18,671 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-13T16:08:18.671307Z"}
2025-07-13 21:38:18,676 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 30m from 2025-07-10 21:38:18.672297 to 2025-07-13 21:38:18.672297", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-13T16:08:18.676282Z"}
2025-07-13 21:38:18,682 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-13T16:08:18.682298Z"}
2025-07-13 21:38:18,686 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 1h from 2025-07-10 21:38:18.682298 to 2025-07-13 21:38:18.682298", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-13T16:08:18.685300Z"}
2025-07-13 21:38:18,691 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-13T16:08:18.691308Z"}
2025-07-13 21:38:18,708 [INFO] signal_stack: {"event": "\u2713 Timeframe aggregation validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:18.708960Z"}
2025-07-13 21:38:18,709 [INFO] signal_stack: {"event": "  5m: 158 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:18.708960Z"}
2025-07-13 21:38:18,709 [INFO] signal_stack: {"event": "  15m: 54 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:18.709950Z"}
2025-07-13 21:38:18,710 [INFO] signal_stack: {"event": "  30m: 28 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:18.710942Z"}
2025-07-13 21:38:18,711 [INFO] signal_stack: {"event": "  1h: 14 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:18.711950Z"}
2025-07-13 21:38:18,712 [INFO] signal_stack: {"event": "\u2705 Timeframe Aggregation: PASSED (0.26s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:18.712949Z"}
2025-07-13 21:38:18,713 [INFO] signal_stack: {"event": "\n--- Real-time Data Pipeline Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:18.713933Z"}
2025-07-13 21:38:18,724 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T16:08:18.724752Z"}
2025-07-13 21:38:19,140 [INFO] app.integrations.fyers.auth: {"event": "Fyers authentication successful!", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-13T16:08:19.140987Z"}
2025-07-13 21:38:19,141 [INFO] app.integrations.fyers.auth: {"event": "Using existing valid access token", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-13T16:08:19.141960Z"}
2025-07-13 21:38:19,142 [INFO] app.integrations.fyers.client: {"event": "Fyers authentication successful", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-13T16:08:19.142957Z"}
2025-07-13 21:38:19,143 [INFO] app.services.market_data_service: {"event": "Fyers API connection initialized successfully", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-13T16:08:19.143974Z"}
2025-07-13 21:38:19,144 [INFO] app.services.realtime_pipeline: {"event": "Real-time data pipeline initialized successfully", "logger": "app.services.realtime_pipeline", "level": "info", "timestamp": "2025-07-13T16:08:19.144460Z"}
2025-07-13 21:38:19,145 [INFO] app.services.realtime_pipeline: {"event": "Starting real-time pipeline for 1 symbols", "logger": "app.services.realtime_pipeline", "level": "info", "timestamp": "2025-07-13T16:08:19.144460Z"}
2025-07-13 21:38:20,336 [INFO] websocket: Websocket connected
2025-07-13 21:38:21,156 [INFO] app.integrations.fyers.websocket_client: {"event": "WebSocket client started", "logger": "app.integrations.fyers.websocket_client", "level": "info", "timestamp": "2025-07-13T16:08:21.156164Z"}
2025-07-13 21:38:21,157 [ERROR] app.integrations.fyers.websocket_client: {"event": "WebSocket not connected. Cannot subscribe to symbols.", "logger": "app.integrations.fyers.websocket_client", "level": "error", "timestamp": "2025-07-13T16:08:21.157214Z"}
2025-07-13 21:38:21,158 [ERROR] app.services.realtime_pipeline: {"event": "Failed to start real-time data streaming", "logger": "app.services.realtime_pipeline", "level": "error", "timestamp": "2025-07-13T16:08:21.158204Z"}
2025-07-13 21:38:21,158 [ERROR] signal_stack: {"event": "Pipeline start failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-13T16:08:21.158204Z"}
2025-07-13 21:38:21,159 [ERROR] signal_stack: {"event": "\u274c Real-time Data Pipeline: FAILED (2.45s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-13T16:08:21.159218Z"}
2025-07-13 21:38:21,159 [INFO] signal_stack: {"event": "\n--- Data Integrity Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.159218Z"}
2025-07-13 21:38:21,172 [INFO] signal_stack: {"event": "\u2713 Data integrity validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.172945Z"}
2025-07-13 21:38:21,173 [INFO] signal_stack: {"event": "\u2705 Data Integrity: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.172945Z"}
2025-07-13 21:38:21,173 [INFO] signal_stack: {"event": "\n--- Performance Metrics Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.173943Z"}
2025-07-13 21:38:21,179 [INFO] signal_stack: {"event": "\u2713 Performance validated: Query took 0.01s for 0 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.179983Z"}
2025-07-13 21:38:21,179 [INFO] signal_stack: {"event": "\u2705 Performance Metrics: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.179983Z"}
2025-07-13 21:38:21,181 [INFO] signal_stack: {"event": "\n================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.180982Z"}
2025-07-13 21:38:21,181 [INFO] signal_stack: {"event": "VALIDATION REPORT", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.181283Z"}
2025-07-13 21:38:21,181 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.181283Z"}
2025-07-13 21:38:21,182 [INFO] signal_stack: {"event": "Overall Success Rate: 60.0% (6/10)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.182320Z"}
2025-07-13 21:38:21,182 [INFO] signal_stack: {"event": "\u2705 PASS Database Connection (0.19s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.182320Z"}
2025-07-13 21:38:21,183 [INFO] signal_stack: {"event": "\u2705 PASS Database Schema (0.11s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.183484Z"}
2025-07-13 21:38:21,183 [INFO] signal_stack: {"event": "\u2705 PASS Symbol Setup (0.06s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.183484Z"}
2025-07-13 21:38:21,184 [INFO] signal_stack: {"event": "\u274c FAIL Fyers Authentication (0.84s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.184451Z"}
2025-07-13 21:38:21,184 [INFO] signal_stack: {"event": "\u274c FAIL Historical Data Fetch (0.20s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.184451Z"}
2025-07-13 21:38:21,185 [INFO] signal_stack: {"event": "\u274c FAIL Data Storage (0.04s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.185463Z"}
2025-07-13 21:38:21,186 [INFO] signal_stack: {"event": "\u2705 PASS Timeframe Aggregation (0.26s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.185463Z"}
2025-07-13 21:38:21,186 [INFO] signal_stack: {"event": "\u274c FAIL Real-time Data Pipeline (2.45s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.186463Z"}
2025-07-13 21:38:21,187 [INFO] signal_stack: {"event": "\u2705 PASS Data Integrity (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.187476Z"}
2025-07-13 21:38:21,187 [INFO] signal_stack: {"event": "\u2705 PASS Performance Metrics (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.187476Z"}
2025-07-13 21:38:21,190 [INFO] signal_stack: {"event": "Detailed report saved to: validation_report_20250713_213821.json", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.190466Z"}
2025-07-13 21:38:21,190 [INFO] signal_stack: {"event": "\u274c MULTIPLE VALIDATIONS FAILED - SYSTEM NOT READY", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:08:21.190466Z"}
2025-07-13 21:38:21,796 [ERROR] app.integrations.fyers.websocket_client: {"event": "WebSocket connection error: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'", "logger": "app.integrations.fyers.websocket_client", "level": "error", "timestamp": "2025-07-13T16:08:21.796438Z"}
2025-07-13 21:52:23,046 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:22:23.045221Z"}
2025-07-13 21:52:28,415 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:22:28.415625Z"}
2025-07-13 21:52:28,618 [INFO] app.database.connection: Database connection successful
2025-07-13 21:52:28,618 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 21:52:28,619 [INFO] app.database.connection: Initializing database...
2025-07-13 21:52:28,622 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 21:52:28,657 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 21:52:28,657 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 21:52:28,665 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 21:52:28,666 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 21:52:28,672 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 21:52:28,673 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 21:52:28,695 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 21:52:28,695 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 21:52:28,696 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:22:28.696312Z"}
2025-07-13 21:52:28,697 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T16:22:28.696312Z"}
2025-07-14 15:22:31,177 [INFO] signal_stack: {"event": "============================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T09:52:31.177596Z"}
2025-07-14 15:22:31,178 [INFO] signal_stack: {"event": "DATABASE RESET", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T09:52:31.178614Z"}
2025-07-14 15:22:31,179 [INFO] signal_stack: {"event": "============================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T09:52:31.179619Z"}
2025-07-14 15:22:31,179 [INFO] signal_stack: {"event": "Dropping all tables...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T09:52:31.179619Z"}
2025-07-14 15:22:32,009 [INFO] signal_stack: {"event": "Dropped table: stock_ohlcv", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T09:52:32.009292Z"}
2025-07-14 15:22:32,016 [INFO] signal_stack: {"event": "Dropped table: stock_ohlcv_agg", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T09:52:32.016600Z"}
2025-07-14 15:22:32,021 [INFO] signal_stack: {"event": "Dropped table: screener_results", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T09:52:32.021597Z"}
2025-07-14 15:22:32,027 [INFO] signal_stack: {"event": "Dropped table: paper_trades", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T09:52:32.026595Z"}
2025-07-14 15:22:32,031 [INFO] signal_stack: {"event": "Dropped table: backtest_trades", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T09:52:32.031599Z"}
2025-07-14 15:22:32,039 [INFO] signal_stack: {"event": "Dropped table: backtest_results", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T09:52:32.039595Z"}
2025-07-14 15:22:32,043 [INFO] signal_stack: {"event": "Dropped table: strategies", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T09:52:32.043618Z"}
2025-07-14 15:22:32,047 [INFO] signal_stack: {"event": "Dropped table: symbols", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T09:52:32.047624Z"}
2025-07-14 15:22:33,340 [INFO] signal_stack: {"event": "All tables dropped successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T09:52:33.340012Z"}
2025-07-14 15:22:33,341 [INFO] signal_stack: {"event": "Recreating all tables...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T09:52:33.341014Z"}
2025-07-14 15:22:33,592 [INFO] signal_stack: {"event": "All tables recreated successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T09:52:33.592084Z"}
2025-07-14 15:22:33,593 [INFO] signal_stack: {"event": "Database reset completed successfully!", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T09:52:33.592084Z"}
2025-07-14 15:57:23,752 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:23.752080Z"}
2025-07-14 15:57:23,753 [INFO] signal_stack: {"event": "PRODUCTION SYSTEM VALIDATION", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:23.753092Z"}
2025-07-14 15:57:23,754 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:23.754056Z"}
2025-07-14 15:57:23,755 [INFO] signal_stack: {"event": "\u26a0\ufe0f  WARNING: This uses REAL market data and API calls", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:23.755083Z"}
2025-07-14 15:57:23,755 [INFO] signal_stack: {"event": "\u26a0\ufe0f  Ensure you have valid Fyers credentials", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:23.755083Z"}
2025-07-14 15:57:23,756 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:23.756067Z"}
2025-07-14 15:57:23,756 [INFO] signal_stack: {"event": "\n--- Database Connection Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:23.756498Z"}
2025-07-14 15:57:23,943 [INFO] app.database.connection: Database connection successful
2025-07-14 15:57:23,947 [INFO] signal_stack: {"event": "\u2713 Database connection and TimescaleDB validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:23.947308Z"}
2025-07-14 15:57:23,948 [INFO] signal_stack: {"event": "\u2705 Database Connection: PASSED (0.19s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:23.948320Z"}
2025-07-14 15:57:23,949 [INFO] signal_stack: {"event": "\n--- Database Schema Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:23.949327Z"}
2025-07-14 15:57:23,949 [INFO] app.database.init_db: Starting complete database setup...
2025-07-14 15:57:23,950 [INFO] app.database.connection: Initializing database...
2025-07-14 15:57:23,952 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-14 15:57:23,981 [INFO] app.database.connection: Database initialization completed successfully
2025-07-14 15:57:23,982 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-14 15:57:23,990 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-14 15:57:23,991 [INFO] app.database.init_db: Creating additional indexes...
2025-07-14 15:57:23,997 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-14 15:57:23,998 [INFO] app.database.init_db: Creating stored procedures...
2025-07-14 15:57:24,026 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-14 15:57:24,027 [INFO] app.database.init_db: Database setup completed successfully
2025-07-14 15:57:24,064 [INFO] signal_stack: {"event": "\u2713 Database schema validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:24.064169Z"}
2025-07-14 15:57:24,066 [INFO] signal_stack: {"event": "\u2705 Database Schema: PASSED (0.12s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:24.066125Z"}
2025-07-14 15:57:24,067 [INFO] signal_stack: {"event": "\n--- Symbol Setup Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:24.067160Z"}
2025-07-14 15:57:24,119 [INFO] signal_stack: {"event": "Creating NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:24.119389Z"}
2025-07-14 15:57:24,120 [INFO] app.services.data_service: {"event": "Creating symbol: NIFTY", "logger": "app.services.data_service", "level": "info", "timestamp": "2025-07-14T10:27:24.120008Z"}
2025-07-14 15:57:24,138 [INFO] signal_stack: {"event": "\u2713 Symbol validated: NIFTY (ID: 1)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:24.137375Z"}
2025-07-14 15:57:24,139 [INFO] signal_stack: {"event": "\u2705 Symbol Setup: PASSED (0.07s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:24.139380Z"}
2025-07-14 15:57:24,139 [INFO] signal_stack: {"event": "\n--- Fyers Authentication Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:24.139380Z"}
2025-07-14 15:57:24,140 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-14T10:27:24.140387Z"}
2025-07-14 15:57:25,150 [INFO] app.integrations.fyers.auth: {"event": "Fyers authentication successful!", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-14T10:27:25.150243Z"}
2025-07-14 15:57:25,151 [INFO] app.integrations.fyers.auth: {"event": "Using existing valid access token", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-14T10:27:25.151228Z"}
2025-07-14 15:57:25,152 [INFO] app.integrations.fyers.client: {"event": "Fyers authentication successful", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-14T10:27:25.152233Z"}
2025-07-14 15:57:25,153 [INFO] app.services.market_data_service: {"event": "Fyers API connection initialized successfully", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-14T10:27:25.153243Z"}
2025-07-14 15:57:25,278 [ERROR] app.integrations.fyers.client: {"event": "Failed to get profile: {'s': 'error', 'code': 500, 'message': 'Looks like you are passing an invalid entry'}", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-14T10:27:25.278286Z"}
2025-07-14 15:57:25,279 [ERROR] signal_stack: {"event": "Failed to retrieve Fyers profile", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-14T10:27:25.279286Z"}
2025-07-14 15:57:25,279 [ERROR] signal_stack: {"event": "\u274c Fyers Authentication: FAILED (1.14s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-14T10:27:25.279286Z"}
2025-07-14 15:57:25,280 [INFO] signal_stack: {"event": "\n--- Historical Data Fetch Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:25.280261Z"}
2025-07-14 15:57:25,280 [INFO] app.services.market_data_service: {"event": "Fetching historical data for NSE:NIFTY50-INDEX (1) from 2025-07-11 to 2025-07-14", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-14T10:27:25.280261Z"}
2025-07-14 15:57:25,281 [ERROR] app.services.market_data_service: {"event": "Error fetching historical data for NSE:NIFTY50-INDEX: FyersClient.get_historical_data() missing 1 required positional argument: 'end_date'", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-14T10:27:25.281288Z"}
2025-07-14 15:57:25,281 [ERROR] signal_stack: {"event": "Historical data fetch failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-14T10:27:25.281694Z"}
2025-07-14 15:57:25,282 [ERROR] signal_stack: {"event": "\u274c Historical Data Fetch: FAILED (0.00s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-14T10:27:25.282714Z"}
2025-07-14 15:57:25,282 [INFO] signal_stack: {"event": "\n--- Data Storage Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:25.282714Z"}
2025-07-14 15:57:25,308 [ERROR] signal_stack: {"event": "Failed to retrieve stored data", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-14T10:27:25.308709Z"}
2025-07-14 15:57:25,308 [ERROR] signal_stack: {"event": "\u274c Data Storage: FAILED (0.02s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-14T10:27:25.308709Z"}
2025-07-14 15:57:25,309 [INFO] signal_stack: {"event": "\n--- Timeframe Aggregation Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:25.309694Z"}
2025-07-14 15:57:25,484 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 5m from 2025-07-11 15:57:25.310694 to 2025-07-14 15:57:25.310694", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-14T10:27:25.484771Z"}
2025-07-14 15:57:25,517 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-14T10:27:25.517395Z"}
2025-07-14 15:57:25,521 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 15m from 2025-07-11 15:57:25.518440 to 2025-07-14 15:57:25.518440", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-14T10:27:25.521430Z"}
2025-07-14 15:57:25,524 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-14T10:27:25.524429Z"}
2025-07-14 15:57:25,528 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 30m from 2025-07-11 15:57:25.525440 to 2025-07-14 15:57:25.525440", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-14T10:27:25.528406Z"}
2025-07-14 15:57:25,532 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-14T10:27:25.532428Z"}
2025-07-14 15:57:25,536 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 1h from 2025-07-11 15:57:25.533429 to 2025-07-14 15:57:25.533429", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-14T10:27:25.536431Z"}
2025-07-14 15:57:25,539 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-14T10:27:25.539769Z"}
2025-07-14 15:57:25,552 [ERROR] signal_stack: {"event": "No aggregated data found for 5m", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-14T10:27:25.552776Z"}
2025-07-14 15:57:25,554 [ERROR] signal_stack: {"event": "\u274c Timeframe Aggregation: FAILED (0.24s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-14T10:27:25.554753Z"}
2025-07-14 15:57:25,555 [INFO] signal_stack: {"event": "\n--- Real-time Data Pipeline Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:25.555735Z"}
2025-07-14 15:57:25,568 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-14T10:27:25.568737Z"}
2025-07-14 15:57:25,728 [INFO] app.integrations.fyers.auth: {"event": "Fyers authentication successful!", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-14T10:27:25.728213Z"}
2025-07-14 15:57:25,729 [INFO] app.integrations.fyers.auth: {"event": "Using existing valid access token", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-14T10:27:25.729214Z"}
2025-07-14 15:57:25,730 [INFO] app.integrations.fyers.client: {"event": "Fyers authentication successful", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-14T10:27:25.730689Z"}
2025-07-14 15:57:25,730 [INFO] app.services.market_data_service: {"event": "Fyers API connection initialized successfully", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-14T10:27:25.730689Z"}
2025-07-14 15:57:25,731 [INFO] app.services.realtime_pipeline: {"event": "Real-time data pipeline initialized successfully", "logger": "app.services.realtime_pipeline", "level": "info", "timestamp": "2025-07-14T10:27:25.731692Z"}
2025-07-14 15:57:25,731 [INFO] app.services.realtime_pipeline: {"event": "Starting real-time pipeline for 1 symbols", "logger": "app.services.realtime_pipeline", "level": "info", "timestamp": "2025-07-14T10:27:25.731692Z"}
2025-07-14 15:57:26,526 [INFO] websocket: Websocket connected
2025-07-14 15:57:27,737 [INFO] app.integrations.fyers.websocket_client: {"event": "WebSocket client started", "logger": "app.integrations.fyers.websocket_client", "level": "info", "timestamp": "2025-07-14T10:27:27.737422Z"}
2025-07-14 15:57:27,738 [ERROR] app.integrations.fyers.websocket_client: {"event": "WebSocket not connected. Cannot subscribe to symbols.", "logger": "app.integrations.fyers.websocket_client", "level": "error", "timestamp": "2025-07-14T10:27:27.738477Z"}
2025-07-14 15:57:27,739 [ERROR] app.services.realtime_pipeline: {"event": "Failed to start real-time data streaming", "logger": "app.services.realtime_pipeline", "level": "error", "timestamp": "2025-07-14T10:27:27.739498Z"}
2025-07-14 15:57:27,740 [ERROR] signal_stack: {"event": "Pipeline start failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-14T10:27:27.740499Z"}
2025-07-14 15:57:27,740 [ERROR] signal_stack: {"event": "\u274c Real-time Data Pipeline: FAILED (2.18s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-14T10:27:27.740499Z"}
2025-07-14 15:57:27,741 [INFO] signal_stack: {"event": "\n--- Data Integrity Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.741487Z"}
2025-07-14 15:57:27,750 [ERROR] signal_stack: {"event": "No recent data found for integrity check", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-14T10:27:27.750039Z"}
2025-07-14 15:57:27,751 [ERROR] signal_stack: {"event": "\u274c Data Integrity: FAILED (0.01s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-14T10:27:27.751017Z"}
2025-07-14 15:57:27,751 [INFO] signal_stack: {"event": "\n--- Performance Metrics Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.751017Z"}
2025-07-14 15:57:27,757 [INFO] signal_stack: {"event": "\u2713 Performance validated: Query took 0.01s for 0 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.757030Z"}
2025-07-14 15:57:27,758 [INFO] signal_stack: {"event": "\u2705 Performance Metrics: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.758031Z"}
2025-07-14 15:57:27,759 [INFO] signal_stack: {"event": "\n================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.758031Z"}
2025-07-14 15:57:27,759 [INFO] signal_stack: {"event": "VALIDATION REPORT", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.759043Z"}
2025-07-14 15:57:27,760 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.760033Z"}
2025-07-14 15:57:27,760 [INFO] signal_stack: {"event": "Overall Success Rate: 40.0% (4/10)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.760033Z"}
2025-07-14 15:57:27,761 [INFO] signal_stack: {"event": "\u2705 PASS Database Connection (0.19s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.761031Z"}
2025-07-14 15:57:27,761 [INFO] signal_stack: {"event": "\u2705 PASS Database Schema (0.12s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.761031Z"}
2025-07-14 15:57:27,762 [INFO] signal_stack: {"event": "\u2705 PASS Symbol Setup (0.07s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.762030Z"}
2025-07-14 15:57:27,762 [INFO] signal_stack: {"event": "\u274c FAIL Fyers Authentication (1.14s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.762030Z"}
2025-07-14 15:57:27,763 [INFO] signal_stack: {"event": "\u274c FAIL Historical Data Fetch (0.00s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.763018Z"}
2025-07-14 15:57:27,763 [INFO] signal_stack: {"event": "\u274c FAIL Data Storage (0.02s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.763018Z"}
2025-07-14 15:57:27,764 [INFO] signal_stack: {"event": "\u274c FAIL Timeframe Aggregation (0.24s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.764045Z"}
2025-07-14 15:57:27,764 [INFO] signal_stack: {"event": "\u274c FAIL Real-time Data Pipeline (2.18s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.764045Z"}
2025-07-14 15:57:27,765 [INFO] signal_stack: {"event": "\u274c FAIL Data Integrity (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.765011Z"}
2025-07-14 15:57:27,766 [INFO] signal_stack: {"event": "\u2705 PASS Performance Metrics (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.765011Z"}
2025-07-14 15:57:27,768 [INFO] signal_stack: {"event": "Detailed report saved to: validation_report_20250714_155727.json", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.768020Z"}
2025-07-14 15:57:27,768 [INFO] signal_stack: {"event": "\u274c MULTIPLE VALIDATIONS FAILED - SYSTEM NOT READY", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-14T10:27:27.768020Z"}
2025-07-14 15:57:28,256 [ERROR] app.integrations.fyers.websocket_client: {"event": "WebSocket connection error: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'", "logger": "app.integrations.fyers.websocket_client", "level": "error", "timestamp": "2025-07-14T10:27:28.256001Z"}
2025-07-14 16:19:20,161 [ERROR] websocket: error from callback <function FyersDataSocket.__init_connection.<locals>.<lambda> at 0x00000280629035B0>: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-14 16:19:20,183 [INFO] websocket: tearing down on exception FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-14 16:19:25,430 [ERROR] websocket: error from callback <function FyersDataSocket.__init_connection.<locals>.<lambda> at 0x000002803D9B69E0>: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-14 16:19:25,492 [INFO] websocket: tearing down on exception FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-14 16:19:27,251 [ERROR] websocket: error from callback <function FyersDataSocket.__init_connection.<locals>.<lambda> at 0x0000028062903640>: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-14 16:19:30,927 [INFO] websocket: Websocket connected
2025-07-14 16:19:32,634 [ERROR] websocket: error from callback <function FyersDataSocket.__init_connection.<locals>.<lambda> at 0x000002803D9B6B00>: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-14 17:20:17,517 [ERROR] websocket: error from callback <function FyersDataSocket.__init_connection.<locals>.<lambda> at 0x0000028062903BE0>: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-14 17:20:17,521 [INFO] websocket: tearing down on exception FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-14 17:20:22,546 [ERROR] websocket: error from callback <function FyersDataSocket.__init_connection.<locals>.<lambda> at 0x000002806294C310>: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-14 17:20:22,546 [INFO] websocket: tearing down on exception FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-14 17:20:24,584 [ERROR] websocket: error from callback <function FyersDataSocket.__init_connection.<locals>.<lambda> at 0x0000028062598040>: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-14 17:20:27,556 [ERROR] websocket: error from callback <function FyersDataSocket.__init_connection.<locals>.<lambda> at 0x0000028062903B50>: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-14 17:20:27,557 [INFO] websocket: tearing down on exception FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-14 17:20:29,584 [ERROR] websocket: error from callback <function FyersDataSocket.__init_connection.<locals>.<lambda> at 0x000002806294F0A0>: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-14 17:20:34,581 [ERROR] websocket: error from callback <function FyersDataSocket.__init_connection.<locals>.<lambda> at 0x000002806294C3A0>: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-14 17:20:43,648 [ERROR] websocket: error from callback <function FyersDataSocket.__init_connection.<locals>.<lambda> at 0x000002806294F520>: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-14 17:20:43,649 [INFO] websocket: tearing down on exception FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'
2025-07-15 20:45:24,115 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:15:24.115833Z"}
2025-07-15 20:45:24,115 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:15:24.115833Z"}
2025-07-15 20:45:24,410 [INFO] app.database.connection: Database connection successful
2025-07-15 20:45:24,410 [INFO] app.database.connection: Database connection successful
2025-07-15 20:45:24,411 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:45:24,411 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:45:24,411 [INFO] app.database.connection: Initializing database...
2025-07-15 20:45:24,411 [INFO] app.database.connection: Initializing database...
2025-07-15 20:45:24,416 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:45:24,416 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:45:24,456 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:45:24,456 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:45:24,456 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:45:24,456 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:45:24,471 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:45:24,471 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:45:24,472 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:45:24,472 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:45:24,487 [ERROR] app.database.init_db: Failed to create indexes: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:45:24,487 [ERROR] app.database.init_db: Failed to create indexes: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:45:24,488 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:45:24,488 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:45:24,489 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.UndefinedColumn) column \"symbol_id\" does not exist\n\n[SQL: \n                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc \n                ON stock_ohlcv (symbol_id, timestamp DESC);\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-15T15:15:24.489070Z"}
2025-07-15 20:45:24,489 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.UndefinedColumn) column \"symbol_id\" does not exist\n\n[SQL: \n                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc \n                ON stock_ohlcv (symbol_id, timestamp DESC);\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-15T15:15:24.489070Z"}
2025-07-15 20:45:53,673 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:15:53.673369Z"}
2025-07-15 20:45:53,673 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:15:53.673369Z"}
2025-07-15 20:45:53,896 [INFO] app.database.connection: Database connection successful
2025-07-15 20:45:53,896 [INFO] app.database.connection: Database connection successful
2025-07-15 20:45:53,896 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:45:53,896 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:45:53,896 [INFO] app.database.connection: Initializing database...
2025-07-15 20:45:53,896 [INFO] app.database.connection: Initializing database...
2025-07-15 20:45:53,899 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:45:53,899 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:45:53,929 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:45:53,929 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:45:53,930 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:45:53,930 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:45:53,938 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:45:53,938 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:45:53,939 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:45:53,939 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:45:53,948 [ERROR] app.database.init_db: Failed to create indexes: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:45:53,948 [ERROR] app.database.init_db: Failed to create indexes: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:45:53,949 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:45:53,949 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:45:53,949 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.UndefinedColumn) column \"symbol_id\" does not exist\n\n[SQL: \n                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc \n                ON stock_ohlcv (symbol_id, timestamp DESC);\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-15T15:15:53.949302Z"}
2025-07-15 20:45:53,949 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.UndefinedColumn) column \"symbol_id\" does not exist\n\n[SQL: \n                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc \n                ON stock_ohlcv (symbol_id, timestamp DESC);\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-15T15:15:53.949302Z"}
2025-07-15 20:46:34,552 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:16:34.552172Z"}
2025-07-15 20:46:34,552 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:16:34.552172Z"}
2025-07-15 20:46:34,773 [INFO] app.database.connection: Database connection successful
2025-07-15 20:46:34,773 [INFO] app.database.connection: Database connection successful
2025-07-15 20:46:34,774 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:46:34,774 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:46:34,775 [INFO] app.database.connection: Initializing database...
2025-07-15 20:46:34,775 [INFO] app.database.connection: Initializing database...
2025-07-15 20:46:34,778 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:46:34,778 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:46:34,810 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:46:34,810 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:46:34,810 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:46:34,810 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:46:34,819 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:46:34,819 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:46:34,820 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:46:34,820 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:46:34,830 [ERROR] app.database.init_db: Failed to create indexes: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:46:34,830 [ERROR] app.database.init_db: Failed to create indexes: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:46:34,830 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:46:34,830 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:46:34,831 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.UndefinedColumn) column \"symbol_id\" does not exist\n\n[SQL: \n                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc \n                ON stock_ohlcv (symbol_id, timestamp DESC);\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-15T15:16:34.831364Z"}
2025-07-15 20:46:34,831 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.UndefinedColumn) column \"symbol_id\" does not exist\n\n[SQL: \n                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc \n                ON stock_ohlcv (symbol_id, timestamp DESC);\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-15T15:16:34.831364Z"}
2025-07-15 20:46:50,926 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:16:50.926938Z"}
2025-07-15 20:46:50,926 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:16:50.926938Z"}
2025-07-15 20:46:51,161 [INFO] app.database.connection: Database connection successful
2025-07-15 20:46:51,161 [INFO] app.database.connection: Database connection successful
2025-07-15 20:46:51,162 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:46:51,162 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:46:51,162 [INFO] app.database.connection: Initializing database...
2025-07-15 20:46:51,162 [INFO] app.database.connection: Initializing database...
2025-07-15 20:46:51,165 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:46:51,165 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:46:51,198 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:46:51,198 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:46:51,198 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:46:51,198 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:46:51,207 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:46:51,207 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:46:51,208 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:46:51,208 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:46:51,217 [ERROR] app.database.init_db: Failed to create indexes: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:46:51,217 [ERROR] app.database.init_db: Failed to create indexes: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:46:51,218 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:46:51,218 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:46:51,218 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.UndefinedColumn) column \"symbol_id\" does not exist\n\n[SQL: \n                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc \n                ON stock_ohlcv (symbol_id, timestamp DESC);\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-15T15:16:51.218405Z"}
2025-07-15 20:46:51,218 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.UndefinedColumn) column \"symbol_id\" does not exist\n\n[SQL: \n                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc \n                ON stock_ohlcv (symbol_id, timestamp DESC);\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-15T15:16:51.218405Z"}
2025-07-15 20:47:21,605 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:17:21.605209Z"}
2025-07-15 20:47:21,824 [INFO] app.database.connection: Database connection successful
2025-07-15 20:47:21,824 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:47:21,825 [INFO] app.database.connection: Initializing database...
2025-07-15 20:47:21,829 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:47:21,859 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:47:21,860 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:47:21,869 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:47:21,870 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:47:21,879 [ERROR] app.database.init_db: Failed to create indexes: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:47:21,879 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:47:21,880 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.UndefinedColumn) column \"symbol_id\" does not exist\n\n[SQL: \n                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc \n                ON stock_ohlcv (symbol_id, timestamp DESC);\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-15T15:17:21.880047Z"}
2025-07-15 20:48:05,595 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:18:05.595631Z"}
2025-07-15 20:48:05,595 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:18:05.595631Z"}
2025-07-15 20:48:05,812 [INFO] app.database.connection: Database connection successful
2025-07-15 20:48:05,812 [INFO] app.database.connection: Database connection successful
2025-07-15 20:48:05,813 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:48:05,813 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:48:05,814 [INFO] app.database.connection: Initializing database...
2025-07-15 20:48:05,814 [INFO] app.database.connection: Initializing database...
2025-07-15 20:48:05,817 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:48:05,817 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:48:05,846 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:48:05,846 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:48:05,846 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:48:05,846 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:48:05,856 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:48:05,856 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:48:05,856 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:48:05,856 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:48:05,865 [ERROR] app.database.init_db: Failed to create indexes: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:48:05,865 [ERROR] app.database.init_db: Failed to create indexes: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:48:05,866 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:48:05,866 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:48:05,867 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.UndefinedColumn) column \"symbol_id\" does not exist\n\n[SQL: \n                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc \n                ON stock_ohlcv (symbol_id, timestamp DESC);\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-15T15:18:05.866920Z"}
2025-07-15 20:48:05,867 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.UndefinedColumn) column \"symbol_id\" does not exist\n\n[SQL: \n                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc \n                ON stock_ohlcv (symbol_id, timestamp DESC);\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-15T15:18:05.866920Z"}
2025-07-15 20:48:28,118 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:18:28.118190Z"}
2025-07-15 20:48:28,341 [INFO] app.database.connection: Database connection successful
2025-07-15 20:48:28,342 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:48:28,342 [INFO] app.database.connection: Initializing database...
2025-07-15 20:48:28,346 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:48:28,382 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:48:28,383 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:48:28,392 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:48:28,392 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:48:28,402 [ERROR] app.database.init_db: Failed to create indexes: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:48:28,403 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:48:28,404 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.UndefinedColumn) column \"symbol_id\" does not exist\n\n[SQL: \n                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc \n                ON stock_ohlcv (symbol_id, timestamp DESC);\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-15T15:18:28.404657Z"}
2025-07-15 20:48:28,503 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:28,859 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:29,218 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:29,579 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:29,936 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:30,294 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:30,653 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:31,011 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:31,372 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:31,730 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:32,085 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:32,445 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:32,800 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:33,159 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:33,517 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:33,874 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:34,228 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:34,582 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:34,938 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:35,295 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:35,653 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:36,010 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:36,368 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:36,726 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:37,084 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:37,442 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:37,798 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:38,156 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:38,512 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:38,868 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:39,226 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:39,581 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:39,942 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:40,297 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:40,655 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:41,010 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:41,369 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:41,726 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:42,084 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:42,441 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:42,797 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:43,155 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:43,512 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:43,864 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:44,225 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:44,578 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:44,936 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:45,295 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:45,648 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:46,012 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:46,369 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:46,727 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:47,085 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:47,445 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:47,803 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:48,164 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:48,521 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:48,875 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:49,230 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:49,586 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:49,940 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:50,296 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:50,658 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:51,015 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:51,375 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:51,730 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:52,088 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:52,446 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:52,806 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:53,166 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:53,522 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:53,879 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:54,238 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:54,595 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:54,953 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:55,309 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:55,666 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:56,023 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:56,377 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:56,734 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:57,090 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:57,445 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:57,803 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:58,162 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:58,522 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:58,877 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:59,234 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:59,592 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:48:59,949 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:00,308 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:00,665 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:01,020 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:01,377 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:01,736 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:02,094 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:02,452 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:02,809 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:03,165 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:03,523 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:03,886 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:04,245 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:04,602 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:04,959 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:05,316 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:05,674 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:06,031 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:06,387 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:06,745 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:07,105 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:07,465 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:07,823 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:08,187 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:08,439 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:19:08.439201Z"}
2025-07-15 20:49:08,439 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:19:08.439201Z"}
2025-07-15 20:49:08,546 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:08,658 [INFO] app.database.connection: Database connection successful
2025-07-15 20:49:08,658 [INFO] app.database.connection: Database connection successful
2025-07-15 20:49:08,659 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:49:08,659 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:49:08,659 [INFO] app.database.connection: Initializing database...
2025-07-15 20:49:08,659 [INFO] app.database.connection: Initializing database...
2025-07-15 20:49:08,663 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:49:08,663 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:49:08,694 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:49:08,694 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:49:08,695 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:49:08,695 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:49:08,703 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:49:08,703 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:49:08,704 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:49:08,704 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:49:08,712 [ERROR] app.database.init_db: Failed to create indexes: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:49:08,712 [ERROR] app.database.init_db: Failed to create indexes: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:49:08,713 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:49:08,713 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:49:08,714 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.UndefinedColumn) column \"symbol_id\" does not exist\n\n[SQL: \n                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc \n                ON stock_ohlcv (symbol_id, timestamp DESC);\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-15T15:19:08.714938Z"}
2025-07-15 20:49:08,714 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.UndefinedColumn) column \"symbol_id\" does not exist\n\n[SQL: \n                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc \n                ON stock_ohlcv (symbol_id, timestamp DESC);\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-15T15:19:08.714938Z"}
2025-07-15 20:49:08,903 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:09,262 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:09,618 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:09,976 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:10,335 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:10,696 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:11,055 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:11,413 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:11,770 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:12,129 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:12,488 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:12,845 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:13,200 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:13,555 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:13,912 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:14,269 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:14,625 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:14,980 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:15,337 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:15,692 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:16,050 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:16,407 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:16,765 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:17,123 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:17,481 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:17,837 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:18,196 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:18,552 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:18,910 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:19,264 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:19,620 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:19,976 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:20,332 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:20,688 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:21,044 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:21,404 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:21,762 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:22,120 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:22,477 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:22,834 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:23,193 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:23,549 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:23,906 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:24,262 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:24,618 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:24,974 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:25,331 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:25,687 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:26,043 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:26,401 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:26,756 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:27,112 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:27,467 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:27,825 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:28,184 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:28,540 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:28,898 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:29,256 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:29,614 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:29,971 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:30,327 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:30,684 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:31,042 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:31,399 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:31,756 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:32,113 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:32,469 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:32,825 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:33,183 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:33,540 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:33,899 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:34,258 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:34,620 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:34,980 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:35,335 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:35,690 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:36,047 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:36,404 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:49:36,762 [INFO] watchfiles.main: 1 change detected
2025-07-15 20:51:56,645 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:21:56.645661Z"}
2025-07-15 20:51:56,645 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:21:56.645661Z"}
2025-07-15 20:51:56,867 [INFO] app.database.connection: Database connection successful
2025-07-15 20:51:56,867 [INFO] app.database.connection: Database connection successful
2025-07-15 20:51:56,868 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:51:56,868 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:51:56,869 [INFO] app.database.connection: Initializing database...
2025-07-15 20:51:56,869 [INFO] app.database.connection: Initializing database...
2025-07-15 20:51:56,872 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:51:56,872 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:51:56,902 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:51:56,902 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:51:56,903 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:51:56,903 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:51:56,911 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:51:56,911 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:51:56,911 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:51:56,911 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:51:56,920 [ERROR] app.database.init_db: Failed to create indexes: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:51:56,920 [ERROR] app.database.init_db: Failed to create indexes: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:51:56,922 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:51:56,922 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.UndefinedColumn) column "symbol_id" does not exist

[SQL: 
                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc 
                ON stock_ohlcv (symbol_id, timestamp DESC);
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-15 20:51:56,922 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.UndefinedColumn) column \"symbol_id\" does not exist\n\n[SQL: \n                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc \n                ON stock_ohlcv (symbol_id, timestamp DESC);\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-15T15:21:56.922283Z"}
2025-07-15 20:51:56,922 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.UndefinedColumn) column \"symbol_id\" does not exist\n\n[SQL: \n                CREATE INDEX IF NOT EXISTS idx_stock_ohlcv_symbol_time_desc \n                ON stock_ohlcv (symbol_id, timestamp DESC);\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-15T15:21:56.922283Z"}
2025-07-15 20:52:48,166 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:22:48.166831Z"}
2025-07-15 20:52:48,166 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:22:48.166831Z"}
2025-07-15 20:52:48,388 [INFO] app.database.connection: Database connection successful
2025-07-15 20:52:48,388 [INFO] app.database.connection: Database connection successful
2025-07-15 20:52:48,389 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:52:48,389 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:52:48,389 [INFO] app.database.connection: Initializing database...
2025-07-15 20:52:48,389 [INFO] app.database.connection: Initializing database...
2025-07-15 20:52:48,392 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:52:48,392 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:52:48,424 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:52:48,424 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:52:48,425 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:52:48,425 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:52:48,434 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:52:48,434 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:52:48,435 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:52:48,435 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:52:59,908 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-15 20:52:59,908 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-15 20:52:59,909 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 20:52:59,909 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 20:52:59,938 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 20:52:59,938 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 20:52:59,938 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 20:52:59,938 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 20:52:59,939 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:22:59.939251Z"}
2025-07-15 20:52:59,939 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:22:59.939251Z"}
2025-07-15 20:52:59,940 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:22:59.939251Z"}
2025-07-15 20:52:59,940 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:22:59.939251Z"}
2025-07-15 20:54:18,752 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:24:18.752506Z"}
2025-07-15 20:54:18,752 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:24:18.752506Z"}
2025-07-15 20:54:18,985 [INFO] app.database.connection: Database connection successful
2025-07-15 20:54:18,985 [INFO] app.database.connection: Database connection successful
2025-07-15 20:54:18,985 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:54:18,985 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:54:18,986 [INFO] app.database.connection: Initializing database...
2025-07-15 20:54:18,986 [INFO] app.database.connection: Initializing database...
2025-07-15 20:54:18,990 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:54:18,990 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:54:19,024 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:54:19,024 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:54:19,026 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:54:19,026 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:54:19,037 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:54:19,037 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:54:19,038 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:54:19,038 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:54:19,049 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-15 20:54:19,049 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-15 20:54:19,049 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 20:54:19,049 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 20:54:19,058 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 20:54:19,058 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 20:54:19,058 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 20:54:19,058 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 20:54:19,059 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:24:19.059191Z"}
2025-07-15 20:54:19,059 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:24:19.059191Z"}
2025-07-15 20:54:19,060 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:24:19.060196Z"}
2025-07-15 20:54:19,060 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:24:19.060196Z"}
2025-07-15 20:54:19,062 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:24:19.061196Z"}
2025-07-15 20:54:19,062 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:24:19.061196Z"}
2025-07-15 20:54:35,318 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:24:35.317343Z"}
2025-07-15 20:54:35,318 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:24:35.317343Z"}
2025-07-15 20:54:35,550 [INFO] app.database.connection: Database connection successful
2025-07-15 20:54:35,550 [INFO] app.database.connection: Database connection successful
2025-07-15 20:54:35,552 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:54:35,552 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:54:35,552 [INFO] app.database.connection: Initializing database...
2025-07-15 20:54:35,552 [INFO] app.database.connection: Initializing database...
2025-07-15 20:54:35,556 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:54:35,556 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:54:35,586 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:54:35,586 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:54:35,587 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:54:35,587 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:54:35,595 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:54:35,595 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:54:35,596 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:54:35,596 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:54:35,607 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-15 20:54:35,607 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-15 20:54:35,607 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 20:54:35,607 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 20:54:35,629 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 20:54:35,629 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 20:54:35,630 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 20:54:35,630 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 20:54:35,631 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:24:35.631810Z"}
2025-07-15 20:54:35,631 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:24:35.631810Z"}
2025-07-15 20:54:35,632 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:24:35.632802Z"}
2025-07-15 20:54:35,632 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:24:35.632802Z"}
2025-07-15 20:54:35,634 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:24:35.634810Z"}
2025-07-15 20:54:35,634 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:24:35.634810Z"}
2025-07-15 20:55:23,545 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:25:23.545498Z"}
2025-07-15 20:55:23,545 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:25:23.545498Z"}
2025-07-15 20:55:23,781 [INFO] app.database.connection: Database connection successful
2025-07-15 20:55:23,781 [INFO] app.database.connection: Database connection successful
2025-07-15 20:55:23,781 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:55:23,781 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:55:23,781 [INFO] app.database.connection: Initializing database...
2025-07-15 20:55:23,781 [INFO] app.database.connection: Initializing database...
2025-07-15 20:55:23,785 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:55:23,785 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:55:23,817 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:55:23,817 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:55:23,818 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:55:23,818 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:55:23,828 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:55:23,828 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:55:23,829 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:55:23,829 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:55:23,830 [INFO] app.database.init_db: Skipping index creation to allow server startup
2025-07-15 20:55:23,830 [INFO] app.database.init_db: Skipping index creation to allow server startup
2025-07-15 20:55:23,830 [INFO] app.database.init_db: Index creation skipped successfully
2025-07-15 20:55:23,830 [INFO] app.database.init_db: Index creation skipped successfully
2025-07-15 20:55:23,831 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 20:55:23,831 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 20:55:23,850 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 20:55:23,850 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 20:55:23,851 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 20:55:23,851 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 20:55:23,852 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:25:23.851416Z"}
2025-07-15 20:55:23,852 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:25:23.851416Z"}
2025-07-15 20:55:23,852 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:25:23.852389Z"}
2025-07-15 20:55:23,852 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:25:23.852389Z"}
2025-07-15 20:55:23,855 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:25:23.854409Z"}
2025-07-15 20:55:23,855 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:25:23.854409Z"}
2025-07-15 20:55:44,781 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:25:44.781323Z"}
2025-07-15 20:55:44,781 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:25:44.781323Z"}
2025-07-15 20:55:44,998 [INFO] app.database.connection: Database connection successful
2025-07-15 20:55:44,998 [INFO] app.database.connection: Database connection successful
2025-07-15 20:55:44,999 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:55:44,999 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:55:44,999 [INFO] app.database.connection: Initializing database...
2025-07-15 20:55:44,999 [INFO] app.database.connection: Initializing database...
2025-07-15 20:55:45,003 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:55:45,003 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:55:45,033 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:55:45,033 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:55:45,034 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:55:45,034 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:55:45,043 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:55:45,043 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:55:45,043 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:55:45,043 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:55:45,044 [INFO] app.database.init_db: Skipping index creation to allow server startup
2025-07-15 20:55:45,044 [INFO] app.database.init_db: Skipping index creation to allow server startup
2025-07-15 20:55:45,045 [INFO] app.database.init_db: Index creation skipped successfully
2025-07-15 20:55:45,045 [INFO] app.database.init_db: Index creation skipped successfully
2025-07-15 20:55:45,045 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 20:55:45,045 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 20:55:45,052 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 20:55:45,052 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 20:55:45,053 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 20:55:45,053 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 20:55:45,054 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:25:45.054657Z"}
2025-07-15 20:55:45,054 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:25:45.054657Z"}
2025-07-15 20:55:45,054 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:25:45.054657Z"}
2025-07-15 20:55:45,054 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:25:45.054657Z"}
2025-07-15 20:55:45,056 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:25:45.056644Z"}
2025-07-15 20:55:45,056 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:25:45.056644Z"}
2025-07-15 20:56:13,091 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:26:13.091451Z"}
2025-07-15 20:56:13,091 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:26:13.091451Z"}
2025-07-15 20:56:13,316 [INFO] app.database.connection: Database connection successful
2025-07-15 20:56:13,316 [INFO] app.database.connection: Database connection successful
2025-07-15 20:56:13,317 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:56:13,317 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:56:13,317 [INFO] app.database.connection: Initializing database...
2025-07-15 20:56:13,317 [INFO] app.database.connection: Initializing database...
2025-07-15 20:56:13,320 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:56:13,320 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:56:13,353 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:56:13,353 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:56:13,354 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:56:13,354 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:56:13,364 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:56:13,364 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:56:13,364 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:56:13,364 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:56:13,366 [INFO] app.database.init_db: Skipping index creation to allow server startup
2025-07-15 20:56:13,366 [INFO] app.database.init_db: Skipping index creation to allow server startup
2025-07-15 20:56:13,367 [INFO] app.database.init_db: Index creation skipped successfully
2025-07-15 20:56:13,367 [INFO] app.database.init_db: Index creation skipped successfully
2025-07-15 20:56:13,367 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 20:56:13,367 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 20:56:13,388 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 20:56:13,388 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 20:56:13,389 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 20:56:13,389 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 20:56:13,390 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:26:13.390162Z"}
2025-07-15 20:56:13,390 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:26:13.390162Z"}
2025-07-15 20:56:13,390 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:26:13.390162Z"}
2025-07-15 20:56:13,390 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:26:13.390162Z"}
2025-07-15 20:56:13,393 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:26:13.393182Z"}
2025-07-15 20:56:13,393 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:26:13.393182Z"}
2025-07-15 20:57:25,711 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:27:25.711676Z"}
2025-07-15 20:57:25,931 [INFO] app.database.connection: Database connection successful
2025-07-15 20:57:25,931 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:57:25,932 [INFO] app.database.connection: Initializing database...
2025-07-15 20:57:25,936 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:57:25,967 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:57:25,967 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:57:25,976 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:57:25,976 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:57:25,977 [INFO] app.database.init_db: Skipping index creation to allow server startup
2025-07-15 20:57:25,978 [INFO] app.database.init_db: Index creation skipped successfully
2025-07-15 20:57:25,978 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 20:57:25,996 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 20:57:25,997 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 20:57:25,997 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:27:25.997477Z"}
2025-07-15 20:57:25,998 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:27:25.998504Z"}
2025-07-15 20:57:26,001 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:27:26.000490Z"}
2025-07-15 20:58:24,735 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:28:24.735451Z"}
2025-07-15 20:58:24,735 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:28:24.735451Z"}
2025-07-15 20:58:24,955 [INFO] app.database.connection: Database connection successful
2025-07-15 20:58:24,955 [INFO] app.database.connection: Database connection successful
2025-07-15 20:58:24,956 [INFO] signal_stack: {"event": "Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:28:24.955270Z"}
2025-07-15 20:58:24,956 [INFO] signal_stack: {"event": "Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:28:24.955270Z"}
2025-07-15 20:58:24,956 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:58:24,956 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 20:58:24,957 [INFO] app.database.connection: Initializing database...
2025-07-15 20:58:24,957 [INFO] app.database.connection: Initializing database...
2025-07-15 20:58:24,961 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:58:24,961 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 20:58:24,992 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:58:24,992 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 20:58:24,993 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:58:24,993 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 20:58:25,003 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:58:25,003 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 20:58:25,004 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:58:25,004 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 20:58:25,005 [INFO] app.database.init_db: Skipping index creation to allow server startup
2025-07-15 20:58:25,005 [INFO] app.database.init_db: Skipping index creation to allow server startup
2025-07-15 20:58:25,005 [INFO] app.database.init_db: Index creation skipped successfully
2025-07-15 20:58:25,005 [INFO] app.database.init_db: Index creation skipped successfully
2025-07-15 20:58:25,007 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 20:58:25,007 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 20:58:25,015 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 20:58:25,015 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 20:58:25,015 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 20:58:25,015 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 20:58:25,016 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:28:25.016361Z"}
2025-07-15 20:58:25,016 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:28:25.016361Z"}
2025-07-15 20:58:25,017 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:28:25.017341Z"}
2025-07-15 20:58:25,017 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:28:25.017341Z"}
2025-07-15 20:58:25,019 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:28:25.019340Z"}
2025-07-15 20:58:25,019 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:28:25.019340Z"}
2025-07-15 21:01:04,544 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:04.544151Z"}
2025-07-15 21:01:04,544 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:04.544151Z"}
2025-07-15 21:01:04,761 [INFO] app.database.connection: Database connection successful
2025-07-15 21:01:04,761 [INFO] app.database.connection: Database connection successful
2025-07-15 21:01:04,762 [INFO] signal_stack: {"event": "Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:04.762420Z"}
2025-07-15 21:01:04,762 [INFO] signal_stack: {"event": "Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:04.762420Z"}
2025-07-15 21:01:04,762 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 21:01:04,762 [INFO] app.database.init_db: Starting complete database setup...
2025-07-15 21:01:04,763 [INFO] app.database.connection: Initializing database...
2025-07-15 21:01:04,763 [INFO] app.database.connection: Initializing database...
2025-07-15 21:01:04,766 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 21:01:04,766 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-15 21:01:04,798 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 21:01:04,798 [INFO] app.database.connection: Database initialization completed successfully
2025-07-15 21:01:04,798 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 21:01:04,798 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-15 21:01:04,806 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 21:01:04,806 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-15 21:01:04,807 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 21:01:04,807 [INFO] app.database.init_db: Creating additional indexes...
2025-07-15 21:01:04,808 [INFO] app.database.init_db: Skipping index creation to allow server startup
2025-07-15 21:01:04,808 [INFO] app.database.init_db: Skipping index creation to allow server startup
2025-07-15 21:01:04,808 [INFO] app.database.init_db: Index creation skipped successfully
2025-07-15 21:01:04,808 [INFO] app.database.init_db: Index creation skipped successfully
2025-07-15 21:01:04,809 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 21:01:04,809 [INFO] app.database.init_db: Creating stored procedures...
2025-07-15 21:01:04,817 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 21:01:04,817 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-15 21:01:04,818 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 21:01:04,818 [INFO] app.database.init_db: Database setup completed successfully
2025-07-15 21:01:04,818 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:04.818695Z"}
2025-07-15 21:01:04,818 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:04.818695Z"}
2025-07-15 21:01:04,819 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:04.819678Z"}
2025-07-15 21:01:04,819 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:04.819678Z"}
2025-07-15 21:01:04,821 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:04.821692Z"}
2025-07-15 21:01:04,821 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:04.821692Z"}
2025-07-15 21:01:55,891 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:55.891249Z"}
2025-07-15 21:01:55,891 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:55.891249Z"}
2025-07-15 21:01:55,892 [INFO] signal_stack: {"event": "Skipping database setup for now - server will start without database", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:55.892251Z"}
2025-07-15 21:01:55,892 [INFO] signal_stack: {"event": "Skipping database setup for now - server will start without database", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:55.892251Z"}
2025-07-15 21:01:55,893 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:55.892251Z"}
2025-07-15 21:01:55,893 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:55.892251Z"}
2025-07-15 21:01:55,895 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:55.895242Z"}
2025-07-15 21:01:55,895 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:31:55.895242Z"}
2025-07-15 21:02:12,175 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:32:12.175969Z"}
2025-07-15 21:02:12,175 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:32:12.175969Z"}
2025-07-15 21:02:12,176 [INFO] signal_stack: {"event": "Skipping database setup for now - server will start without database", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:32:12.176993Z"}
2025-07-15 21:02:12,176 [INFO] signal_stack: {"event": "Skipping database setup for now - server will start without database", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:32:12.176993Z"}
2025-07-15 21:02:12,178 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:32:12.178010Z"}
2025-07-15 21:02:12,178 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:32:12.178010Z"}
2025-07-15 21:02:12,179 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:32:12.179971Z"}
2025-07-15 21:02:12,179 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:32:12.179971Z"}
2025-07-15 21:02:40,058 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:32:40.058338Z"}
2025-07-15 21:02:40,059 [INFO] signal_stack: {"event": "Skipping database setup for now - server will start without database", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:32:40.059384Z"}
2025-07-15 21:02:40,059 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:32:40.059384Z"}
2025-07-15 21:02:40,061 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:32:40.061385Z"}
2025-07-15 21:02:57,887 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:32:57.887922Z"}
2025-07-15 21:02:57,888 [INFO] signal_stack: {"event": "Skipping database setup for now - server will start without database", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:32:57.888920Z"}
2025-07-15 21:02:57,889 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:32:57.889921Z"}
2025-07-15 21:03:18,051 [INFO] app.database.connection: Database connection successful
2025-07-15 21:03:28,722 [ERROR] app.api.v1.endpoints.backtesting: {"event": "Error getting available strategies: 3 validation errors for StrategyInfo\nkey\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...um', 'validated': False}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\nclass_name\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...um', 'validated': False}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\ndefault_parameters\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...um', 'validated': False}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing", "logger": "app.api.v1.endpoints.backtesting", "level": "error", "timestamp": "2025-07-15T15:33:28.722223Z"}
2025-07-15 21:03:49,226 [ERROR] app.api.v1.endpoints.backtesting: {"event": "Error getting available strategies: 3 validation errors for StrategyInfo\nkey\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...um', 'validated': False}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\nclass_name\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...um', 'validated': False}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\ndefault_parameters\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...um', 'validated': False}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing", "logger": "app.api.v1.endpoints.backtesting", "level": "error", "timestamp": "2025-07-15T15:33:49.226464Z"}
2025-07-15 21:03:59,588 [ERROR] app.api.v1.endpoints.strategies: {"event": "Error getting strategy types: MovingAverageCrossoverStrategy.__init__() missing 1 required positional argument: 'symbol'", "logger": "app.api.v1.endpoints.strategies", "level": "error", "timestamp": "2025-07-15T15:33:59.587913Z"}
2025-07-15 21:19:30,167 [INFO] app.database.connection: Database connection successful
2025-07-15 21:19:32,217 [ERROR] app.api.v1.endpoints.backtesting: {"event": "Error getting available strategies: 3 validation errors for StrategyInfo\nkey\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...um', 'validated': False}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\nclass_name\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...um', 'validated': False}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\ndefault_parameters\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...um', 'validated': False}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing", "logger": "app.api.v1.endpoints.backtesting", "level": "error", "timestamp": "2025-07-15T15:49:32.217749Z"}
2025-07-15 21:19:34,299 [INFO] app.services.backtesting.reference_engine: {"event": "\ud83d\ude80 Starting Reference Backtest for NIFTY50 (30min timeframe)", "logger": "app.services.backtesting.reference_engine", "level": "info", "timestamp": "2025-07-15T15:49:34.299601Z"}
2025-07-15 21:19:34,330 [INFO] app.services.backtesting.strategy_interface: {"event": "\u2705 Loaded strategy class: SimplePriceActionStrategy from C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\Reference\\V7_IntradayCleanup_Best_30Min\\SimplePriceActionStrategy.py", "logger": "app.services.backtesting.strategy_interface", "level": "info", "timestamp": "2025-07-15T15:49:34.329831Z"}
2025-07-15 21:19:34,330 [INFO] app.services.backtesting.strategy_interface: {"event": "\u2705 Strategy validation passed for SimplePriceActionStrategy", "logger": "app.services.backtesting.strategy_interface", "level": "info", "timestamp": "2025-07-15T15:49:34.330212Z"}
2025-07-15 21:19:34,331 [INFO] app.services.backtesting.reference_engine: {"event": "\u2705 Using registered strategy: SimplePriceActionStrategy", "logger": "app.services.backtesting.reference_engine", "level": "info", "timestamp": "2025-07-15T15:49:34.331215Z"}
2025-07-15 21:19:34,331 [INFO] app.services.backtesting.reference_engine: {"event": "\ud83d\udcca Loading data for NIFTY50 (30min timeframe)", "logger": "app.services.backtesting.reference_engine", "level": "info", "timestamp": "2025-07-15T15:49:34.331215Z"}
2025-07-15 21:19:35,508 [INFO] app.services.backtesting.reference_engine: {"event": "\ud83d\udcc8 Loaded 92145 1-minute records", "logger": "app.services.backtesting.reference_engine", "level": "info", "timestamp": "2025-07-15T15:49:35.508983Z"}
2025-07-15 21:19:35,556 [INFO] app.services.backtesting.reference_engine: {"event": "\ud83d\udcca Resampled to 3195 30-minute records", "logger": "app.services.backtesting.reference_engine", "level": "info", "timestamp": "2025-07-15T15:49:35.556735Z"}
2025-07-15 21:19:35,566 [INFO] app.services.backtesting.reference_engine: {"event": "\u2705 Data prepared for backtesting: 3195 records", "logger": "app.services.backtesting.reference_engine", "level": "info", "timestamp": "2025-07-15T15:49:35.565433Z"}
2025-07-15 21:19:35,587 [INFO] app.services.backtesting.strategy_interface: {"event": "\u2705 Loaded strategy config from C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\Reference\\V7_IntradayCleanup_Best_30Min\\SimplePriceActionStrategyConfig.yaml", "logger": "app.services.backtesting.strategy_interface", "level": "info", "timestamp": "2025-07-15T15:49:35.587619Z"}
2025-07-15 21:19:35,587 [INFO] app.services.backtesting.reference_engine: {"event": "\u2705 Merged strategy configuration", "logger": "app.services.backtesting.reference_engine", "level": "info", "timestamp": "2025-07-15T15:49:35.587619Z"}
2025-07-15 21:19:35,604 [INFO] app.services.backtesting.reference_engine: {"event": "\ud83d\udcdd Created temporary config file: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpa7ewyxz5.yaml", "logger": "app.services.backtesting.reference_engine", "level": "info", "timestamp": "2025-07-15T15:49:35.604699Z"}
2025-07-15 21:19:35,605 [INFO] app.services.backtesting.reference_engine: {"event": "\u26a1 Executing backtest...", "logger": "app.services.backtesting.reference_engine", "level": "info", "timestamp": "2025-07-15T15:49:35.605693Z"}
2025-07-15 21:19:36,662 [INFO] app.services.backtesting.reference_engine: {"event": "\u2705 Backtest execution completed in 1.06s", "logger": "app.services.backtesting.reference_engine", "level": "info", "timestamp": "2025-07-15T15:49:36.662461Z"}
2025-07-15 21:19:36,664 [INFO] app.services.backtesting.reference_engine: {"event": "\ud83d\udcca Using backtesting library trade data with 86 trades", "logger": "app.services.backtesting.reference_engine", "level": "info", "timestamp": "2025-07-15T15:49:36.663458Z"}
2025-07-15 21:19:36,677 [INFO] app.services.backtesting.reference_engine: {"event": "\ud83d\udcca Extracted 86 trades", "logger": "app.services.backtesting.reference_engine", "level": "info", "timestamp": "2025-07-15T15:49:36.677830Z"}
2025-07-15 21:19:36,678 [INFO] app.services.backtesting.reference_engine: {"event": "\u2705 Calculated custom metrics: 73.3% win rate, 25.7% return", "logger": "app.services.backtesting.reference_engine", "level": "info", "timestamp": "2025-07-15T15:49:36.678831Z"}
2025-07-15 21:19:36,680 [INFO] app.services.backtesting.reference_engine: {"event": "\u2705 Reference Backtest completed in 2.38s", "logger": "app.services.backtesting.reference_engine", "level": "info", "timestamp": "2025-07-15T15:49:36.680833Z"}
2025-07-15 21:19:36,682 [INFO] app.services.backtesting.reference_engine: {"event": "\ud83d\udd12 Reference backtest engine closed", "logger": "app.services.backtesting.reference_engine", "level": "info", "timestamp": "2025-07-15T15:49:36.682836Z"}
2025-07-15 21:19:36,689 [ERROR] app.api.v1.endpoints.backtesting: {"event": "Error running reference backtest: 5 validation errors for HybridBacktestResults\ndata_shape\n  Field required [type=missing, input_value={'execution_time': 2.3812...o': 0.9346415723359831}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\nbacktest_library_results\n  Field required [type=missing, input_value={'execution_time': 2.3812...o': 0.9346415723359831}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\nreports\n  Field required [type=missing, input_value={'execution_time': 2.3812...o': 0.9346415723359831}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\nvalidation\n  Field required [type=missing, input_value={'execution_time': 2.3812...o': 0.9346415723359831}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\ntimestamp\n  Field required [type=missing, input_value={'execution_time': 2.3812...o': 0.9346415723359831}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing", "logger": "app.api.v1.endpoints.backtesting", "level": "error", "timestamp": "2025-07-15T15:49:36.689318Z"}
2025-07-15 21:22:06,297 [ERROR] app.api.v1.endpoints.backtesting: {"event": "Error getting available strategies: 3 validation errors for StrategyInfo\nkey\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\nclass_name\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\ndefault_parameters\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing", "logger": "app.api.v1.endpoints.backtesting", "level": "error", "timestamp": "2025-07-15T15:52:06.297885Z"}
2025-07-15 21:22:31,479 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:52:31.479997Z"}
2025-07-15 21:22:31,480 [INFO] signal_stack: {"event": "Skipping database setup for now - server will start without database", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:52:31.480999Z"}
2025-07-15 21:22:31,481 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:52:31.481996Z"}
2025-07-15 21:22:31,484 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:52:31.484417Z"}
2025-07-15 21:23:11,269 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:53:11.269466Z"}
2025-07-15 21:23:11,269 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:53:11.269466Z"}
2025-07-15 21:23:11,270 [INFO] signal_stack: {"event": "Skipping database setup for now - server will start without database", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:53:11.270478Z"}
2025-07-15 21:23:11,270 [INFO] signal_stack: {"event": "Skipping database setup for now - server will start without database", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:53:11.270478Z"}
2025-07-15 21:23:11,270 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:53:11.270478Z"}
2025-07-15 21:23:11,270 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:53:11.270478Z"}
2025-07-15 21:23:11,273 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:53:11.273469Z"}
2025-07-15 21:23:11,273 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:53:11.273469Z"}
2025-07-15 21:23:19,771 [ERROR] app.api.v1.endpoints.backtesting: {"event": "Error getting available strategies: 3 validation errors for StrategyInfo\nkey\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\nclass_name\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\ndefault_parameters\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing", "logger": "app.api.v1.endpoints.backtesting", "level": "error", "timestamp": "2025-07-15T15:53:19.771917Z"}
2025-07-15 21:23:35,922 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:53:35.921950Z"}
2025-07-15 21:23:35,922 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:53:35.921950Z"}
2025-07-15 21:23:35,923 [INFO] signal_stack: {"event": "Skipping database setup for now - server will start without database", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:53:35.923165Z"}
2025-07-15 21:23:35,923 [INFO] signal_stack: {"event": "Skipping database setup for now - server will start without database", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:53:35.923165Z"}
2025-07-15 21:23:35,924 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:53:35.924199Z"}
2025-07-15 21:23:35,924 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:53:35.924199Z"}
2025-07-15 21:23:35,927 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:53:35.926182Z"}
2025-07-15 21:23:35,927 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:53:35.926182Z"}
2025-07-15 21:23:44,936 [INFO] app.database.connection: Database connection successful
2025-07-15 21:23:52,716 [ERROR] app.api.v1.endpoints.backtesting: {"event": "Error getting available strategies: 3 validation errors for StrategyInfo\nkey\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\nclass_name\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\ndefault_parameters\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing", "logger": "app.api.v1.endpoints.backtesting", "level": "error", "timestamp": "2025-07-15T15:53:52.716076Z"}
2025-07-15 21:24:18,985 [ERROR] app.api.v1.endpoints.backtesting: {"event": "Error getting available strategies: 3 validation errors for StrategyInfo\nkey\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\nclass_name\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\ndefault_parameters\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing", "logger": "app.api.v1.endpoints.backtesting", "level": "error", "timestamp": "2025-07-15T15:54:18.985997Z"}
2025-07-15 21:24:41,851 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:54:41.851933Z"}
2025-07-15 21:24:41,851 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:54:41.851933Z"}
2025-07-15 21:24:41,853 [INFO] signal_stack: {"event": "Skipping database setup for now - server will start without database", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:54:41.853929Z"}
2025-07-15 21:24:41,853 [INFO] signal_stack: {"event": "Skipping database setup for now - server will start without database", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:54:41.853929Z"}
2025-07-15 21:24:41,853 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:54:41.853929Z"}
2025-07-15 21:24:41,853 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:54:41.853929Z"}
2025-07-15 21:24:41,856 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:54:41.855937Z"}
2025-07-15 21:24:41,856 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:54:41.855937Z"}
2025-07-15 21:25:03,530 [INFO] app.database.connection: Database connection successful
2025-07-15 21:25:13,015 [ERROR] app.api.v1.endpoints.backtesting: {"event": "Error getting available strategies: 3 validation errors for StrategyInfo\nkey\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\nclass_name\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\ndefault_parameters\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing", "logger": "app.api.v1.endpoints.backtesting", "level": "error", "timestamp": "2025-07-15T15:55:13.015807Z"}
2025-07-15 21:25:20,048 [ERROR] app.api.v1.endpoints.backtesting: {"event": "Error getting available strategies: 3 validation errors for StrategyInfo\nkey\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\nclass_name\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing\ndefault_parameters\n  Field required [type=missing, input_value={'name': 'SimplePriceActi...ium', 'validated': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/missing", "logger": "app.api.v1.endpoints.backtesting", "level": "error", "timestamp": "2025-07-15T15:55:20.048524Z"}
2025-07-15 21:26:17,087 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:56:17.087082Z"}
2025-07-15 21:26:17,087 [INFO] signal_stack: {"event": "Skipping database setup for now - server will start without database", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:56:17.087443Z"}
2025-07-15 21:26:17,088 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-15T15:56:17.088397Z"}
