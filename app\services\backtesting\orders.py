"""
Order management for backtesting engine.
"""

from enum import Enum
from typing import Optional, Dict, Any
from datetime import datetime
from dataclasses import dataclass


class OrderType(Enum):
    """Order types."""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderStatus(Enum):
    """Order status."""
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    PARTIAL = "partial"


class OrderSide(Enum):
    """Order side."""
    BUY = "buy"
    SELL = "sell"


@dataclass
class Order:
    """Trading order."""
    
    id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: Optional[float] = None
    stop_price: Optional[float] = None
    timestamp: Optional[datetime] = None
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: int = 0
    filled_price: Optional[float] = None
    filled_timestamp: Optional[datetime] = None
    commission: float = 0.0
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Initialize order after creation."""
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
        if self.metadata is None:
            self.metadata = {}
    
    @property
    def is_buy(self) -> bool:
        """Check if order is a buy order."""
        return self.side == OrderSide.BUY
    
    @property
    def is_sell(self) -> bool:
        """Check if order is a sell order."""
        return self.side == OrderSide.SELL
    
    @property
    def is_filled(self) -> bool:
        """Check if order is completely filled."""
        return self.status == OrderStatus.FILLED
    
    @property
    def is_pending(self) -> bool:
        """Check if order is pending."""
        return self.status == OrderStatus.PENDING
    
    @property
    def remaining_quantity(self) -> int:
        """Get remaining quantity to fill."""
        return self.quantity - self.filled_quantity
    
    @property
    def fill_percentage(self) -> float:
        """Get fill percentage."""
        if self.quantity == 0:
            return 0.0
        return (self.filled_quantity / self.quantity) * 100
    
    def fill(self, quantity: int, price: float, timestamp: datetime = None, commission: float = 0.0) -> bool:
        """
        Fill order partially or completely.
        
        Args:
            quantity: Quantity to fill
            price: Fill price
            timestamp: Fill timestamp
            commission: Commission for this fill
            
        Returns:
            True if order is completely filled
        """
        if self.status not in [OrderStatus.PENDING, OrderStatus.PARTIAL]:
            return False
        
        if quantity <= 0 or quantity > self.remaining_quantity:
            return False
        
        # Update filled quantities
        if self.filled_quantity == 0:
            # First fill
            self.filled_price = price
            self.filled_timestamp = timestamp or datetime.utcnow()
        else:
            # Subsequent fills - calculate weighted average price
            total_value = (self.filled_quantity * self.filled_price) + (quantity * price)
            self.filled_price = total_value / (self.filled_quantity + quantity)
        
        self.filled_quantity += quantity
        self.commission += commission
        
        # Update status
        if self.filled_quantity >= self.quantity:
            self.status = OrderStatus.FILLED
            return True
        else:
            self.status = OrderStatus.PARTIAL
            return False
    
    def cancel(self) -> bool:
        """Cancel order."""
        if self.status == OrderStatus.PENDING:
            self.status = OrderStatus.CANCELLED
            return True
        return False
    
    def reject(self, reason: str = None) -> bool:
        """Reject order."""
        if self.status == OrderStatus.PENDING:
            self.status = OrderStatus.REJECTED
            if reason:
                self.metadata['rejection_reason'] = reason
            return True
        return False
    
    @property
    def total_value(self) -> float:
        """Get total order value."""
        if self.filled_quantity > 0 and self.filled_price:
            return self.filled_quantity * self.filled_price
        elif self.price:
            return self.quantity * self.price
        return 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert order to dictionary."""
        return {
            'id': self.id,
            'symbol': self.symbol,
            'side': self.side.value,
            'order_type': self.order_type.value,
            'quantity': self.quantity,
            'price': self.price,
            'stop_price': self.stop_price,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'status': self.status.value,
            'filled_quantity': self.filled_quantity,
            'filled_price': self.filled_price,
            'filled_timestamp': self.filled_timestamp.isoformat() if self.filled_timestamp else None,
            'commission': self.commission,
            'total_value': self.total_value,
            'fill_percentage': self.fill_percentage,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Order':
        """Create order from dictionary."""
        return cls(
            id=data['id'],
            symbol=data['symbol'],
            side=OrderSide(data['side']),
            order_type=OrderType(data['order_type']),
            quantity=data['quantity'],
            price=data.get('price'),
            stop_price=data.get('stop_price'),
            timestamp=datetime.fromisoformat(data['timestamp']) if data.get('timestamp') else None,
            status=OrderStatus(data['status']),
            filled_quantity=data.get('filled_quantity', 0),
            filled_price=data.get('filled_price'),
            filled_timestamp=datetime.fromisoformat(data['filled_timestamp']) if data.get('filled_timestamp') else None,
            commission=data.get('commission', 0.0),
            metadata=data.get('metadata', {})
        )
