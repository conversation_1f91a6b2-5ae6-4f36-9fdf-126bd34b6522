#!/usr/bin/env python3
"""
Test setup script for NIFTY pipeline without <PERSON><PERSON> authentication.
This script tests the database and core functionality.
"""

import sys
import os
import time
import signal
from pathlib import Path
from datetime import datetime, timed<PERSON><PERSON>

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import setup_logging
from app.database.connection import get_db, check_database_connection
from app.database.init_db import setup_database
from app.services.market_data_service import MarketDataService
from app.services.aggregation_service import AggregationService
from app.database.models import MarketType

logger = setup_logging(level="INFO")


def setup_nifty_symbol():
    """Set up NIFTY symbol in database."""
    logger.info("Setting up NIFTY symbol...")
    
    try:
        db = next(get_db())
        market_service = MarketDataService(db)
        
        # Check if NIFTY symbol exists
        nifty_symbol = market_service.data_service.get_symbol_by_name("NIFTY")
        
        if not nifty_symbol:
            logger.info("Creating NIFTY symbol in database...")
            
            symbol_data = {
                'symbol': 'NIFTY',
                'name': 'Nifty 50 Index',
                'market_type': MarketType.INDEX,
                'exchange': 'NSE',
                'token': 'NSE:NIFTY50-INDEX',
                'lot_size': 50,
                'tick_size': 0.05,
                'is_active': True
            }
            
            nifty_symbol = market_service.data_service.create_symbol(symbol_data)
            
            if nifty_symbol:
                logger.info("✓ NIFTY symbol created successfully")
            else:
                logger.error("✗ Failed to create NIFTY symbol")
                return False
        else:
            logger.info("✓ NIFTY symbol already exists")
        
        return True
        
    except Exception as e:
        logger.error(f"Error setting up NIFTY symbol: {e}")
        return False
    finally:
        db.close()


def create_sample_data():
    """Create sample OHLCV data for testing."""
    logger.info("Creating sample OHLCV data...")
    
    try:
        db = next(get_db())
        market_service = MarketDataService(db)
        
        # Get NIFTY symbol
        nifty_symbol = market_service.data_service.get_symbol_by_name("NIFTY")
        if not nifty_symbol:
            logger.error("NIFTY symbol not found")
            return False
        
        # Create sample data for the last 3 days
        end_time = datetime.now().replace(second=0, microsecond=0)
        start_time = end_time - timedelta(days=3)
        
        sample_data = []
        current_time = start_time
        base_price = 24500.0
        
        while current_time < end_time:
            # Skip weekends
            if current_time.weekday() < 5:  # Monday = 0, Sunday = 6
                # Market hours: 9:15 AM to 3:30 PM
                if 9 <= current_time.hour < 15 or (current_time.hour == 15 and current_time.minute <= 30):
                    # Generate realistic OHLCV data
                    price_change = (hash(str(current_time)) % 200 - 100) / 100.0  # -1 to +1
                    open_price = base_price + price_change
                    high_price = open_price + abs(hash(str(current_time + timedelta(seconds=1))) % 50) / 10.0
                    low_price = open_price - abs(hash(str(current_time + timedelta(seconds=2))) % 50) / 10.0
                    close_price = low_price + (high_price - low_price) * (hash(str(current_time + timedelta(seconds=3))) % 100) / 100.0
                    volume = 1000 + abs(hash(str(current_time + timedelta(seconds=4))) % 5000)
                    
                    sample_data.append({
                        'timestamp': current_time,
                        'open': round(open_price, 2),
                        'high': round(high_price, 2),
                        'low': round(low_price, 2),
                        'close': round(close_price, 2),
                        'volume': volume
                    })
                    
                    base_price = close_price  # Use close as next base
            
            current_time += timedelta(minutes=1)
        
        # Insert sample data
        if sample_data:
            success = market_service.data_service.store_ohlcv_data("NIFTY", sample_data)
            if success:
                logger.info(f"✓ Created {len(sample_data)} sample OHLCV records")
                return True
            else:
                logger.error("Failed to store sample data")
                return False
        else:
            logger.warning("No sample data created")
            return False
            
    except Exception as e:
        logger.error(f"Error creating sample data: {e}")
        return False
    finally:
        db.close()


def test_aggregation():
    """Test timeframe aggregation."""
    logger.info("Testing timeframe aggregation...")
    
    try:
        db = next(get_db())
        aggregation_service = AggregationService(db)
        
        # Test aggregation for different timeframes
        timeframes = ['5m', '15m', '30m', '1h']
        
        for timeframe in timeframes:
            logger.info(f"Aggregating NIFTY data for {timeframe}...")
            
            success = aggregation_service.aggregate_symbol_data(
                symbol="NIFTY",
                timeframe=timeframe,
                start_time=datetime.now() - timedelta(days=3),
                end_time=datetime.now()
            )
            
            if success:
                logger.info(f"✓ {timeframe} aggregation successful")
            else:
                logger.warning(f"⚠ {timeframe} aggregation failed")
        
        # Get aggregation statistics
        stats = aggregation_service.get_aggregation_statistics()
        logger.info("Aggregation statistics:")
        for key, value in stats.items():
            if key.endswith('_records'):
                logger.info(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing aggregation: {e}")
        return False
    finally:
        db.close()


def verify_data_flow():
    """Verify that data is flowing correctly through the system."""
    logger.info("Verifying data flow...")
    
    try:
        db = next(get_db())
        market_service = MarketDataService(db)
        aggregation_service = AggregationService(db)
        
        # Check latest 1-minute data
        latest_data = market_service.data_service.get_latest_ohlcv("NIFTY", count=5)
        
        if latest_data:
            logger.info("✓ Latest 1-minute data:")
            for data in latest_data:
                logger.info(f"  {data.timestamp}: O={data.open}, H={data.high}, "
                           f"L={data.low}, C={data.close}, V={data.volume}")
        else:
            logger.warning("⚠ No recent 1-minute data found")
        
        # Check aggregated data
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=2)
        
        for timeframe in ['5m', '15m', '30m']:
            agg_df = aggregation_service.get_aggregated_data_as_dataframe(
                "NIFTY", timeframe, start_time, end_time
            )
            
            if not agg_df.empty:
                logger.info(f"✓ {timeframe} aggregated data: {len(agg_df)} records")
                logger.info(f"  Latest: {agg_df.index[-1]} = {agg_df.iloc[-1]['close']}")
            else:
                logger.warning(f"⚠ No {timeframe} aggregated data found")
        
        return True
        
    except Exception as e:
        logger.error(f"Error verifying data flow: {e}")
        return False
    finally:
        db.close()


def main():
    """Main function to run the test setup."""
    logger.info("=" * 80)
    logger.info("NIFTY PIPELINE TEST SETUP (WITHOUT FYERS)")
    logger.info("=" * 80)
    
    try:
        # Step 1: Check database connection
        logger.info("Step 1: Checking database connection...")
        if not check_database_connection():
            logger.error("Database connection failed. Please check your configuration.")
            return False
        logger.info("✓ Database connection successful")
        
        # Step 2: Setup database
        logger.info("\nStep 2: Setting up database...")
        try:
            setup_database()
            logger.info("✓ Database setup completed")
        except Exception as e:
            logger.error(f"Database setup failed: {e}")
            return False
        
        # Step 3: Setup NIFTY symbol
        logger.info("\nStep 3: Setting up NIFTY symbol...")
        if not setup_nifty_symbol():
            return False
        
        # Step 4: Create sample data
        logger.info("\nStep 4: Creating sample data...")
        if not create_sample_data():
            return False
        
        # Step 5: Test aggregation
        logger.info("\nStep 5: Testing timeframe aggregation...")
        if not test_aggregation():
            return False
        
        # Step 6: Verify data flow
        logger.info("\nStep 6: Verifying data flow...")
        if not verify_data_flow():
            return False
        
        logger.info("\n" + "=" * 80)
        logger.info("NIFTY PIPELINE TEST SETUP COMPLETED SUCCESSFULLY!")
        logger.info("=" * 80)
        logger.info("The system is now ready for:")
        logger.info("1. Database operations with TimescaleDB")
        logger.info("2. Automatic timeframe aggregation")
        logger.info("3. Historical data storage and retrieval")
        logger.info("4. Strategy development and backtesting")
        logger.info("\nNote: Fyers API integration requires valid credentials")
        
        return True
        
    except Exception as e:
        logger.error(f"Pipeline test setup failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
