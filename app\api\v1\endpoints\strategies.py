"""
Strategy management API endpoints.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api.dependencies import validate_symbol
from app.api.schemas.strategies import (
    CreateStrategyRequest,
    UpdateStrategyRequest,
    StrategyResponse,
    StrategyInfo,
    StrategyListResponse,
    StrategyTypesResponse,
    StrategyTypeInfo,
    ValidateStrategyRequest,
    ValidateStrategyResponse,
    ValidationIssue,
    StrategyPerformanceRequest,
    StrategyPerformanceResponse,
    CloneStrategyRequest,
    StrategyExecutionRequest,
    StrategyExecutionResponse
)
from app.services.backtest_service import BacktestService
from app.database.models import Strategy
from app.database.connection import get_db
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


def get_backtest_service(db: Session = Depends(get_db)) -> BacktestService:
    """Get backtest service instance."""
    return BacktestService(db)


@router.get("/types", response_model=StrategyTypesResponse)
async def get_strategy_types(
    backtest_service: BacktestService = Depends(get_backtest_service)
):
    """Get available strategy types."""
    try:
        strategy_types = backtest_service.get_available_strategies()
        
        # Enhanced strategy type information
        enhanced_types = []
        for strategy in strategy_types:
            enhanced_types.append(StrategyTypeInfo(
                key=strategy['key'],
                name=strategy['name'],
                description=strategy['description'],
                default_parameters=strategy['default_parameters'],
                required_parameters=list(strategy['default_parameters'].keys()),
                parameter_descriptions=_get_parameter_descriptions(strategy['key'])
            ))
        
        return StrategyTypesResponse(strategy_types=enhanced_types)
        
    except Exception as e:
        logger.error(f"Error getting strategy types: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve strategy types"
        )


@router.post("/", response_model=StrategyResponse)
async def create_strategy(
    request: CreateStrategyRequest,
    db: Session = Depends(get_db)
):
    """Create a new strategy."""
    try:
        # Validate symbols
        validated_symbols = [validate_symbol(symbol) for symbol in request.symbols]
        
        # Create strategy record
        strategy = Strategy(
            name=request.name,
            description=request.description,
            strategy_type=request.strategy_type,
            parameters=str(request.parameters),  # Store as JSON string
            symbols=','.join(validated_symbols),
            is_active=request.is_active,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(strategy)
        db.commit()
        db.refresh(strategy)
        
        # Convert to response format
        strategy_info = StrategyInfo(
            id=strategy.id,
            name=strategy.name,
            description=strategy.description,
            strategy_type=strategy.strategy_type,
            parameters=eval(strategy.parameters),  # Convert back to dict
            symbols=strategy.symbols.split(','),
            is_active=strategy.is_active,
            created_at=strategy.created_at,
            updated_at=strategy.updated_at
        )
        
        return StrategyResponse(strategy=strategy_info)
        
    except Exception as e:
        logger.error(f"Error creating strategy: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create strategy"
        )


@router.get("/", response_model=StrategyListResponse)
async def get_strategies(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    is_active: Optional[bool] = Query(None),
    strategy_type: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """Get list of strategies."""
    try:
        query = db.query(Strategy)
        
        # Apply filters
        if is_active is not None:
            query = query.filter(Strategy.is_active == is_active)
        
        if strategy_type:
            query = query.filter(Strategy.strategy_type == strategy_type)
        
        # Get total count
        total_count = query.count()
        
        # Apply pagination
        strategies = query.offset(skip).limit(limit).all()
        
        # Convert to response format
        strategy_list = []
        for strategy in strategies:
            strategy_info = StrategyInfo(
                id=strategy.id,
                name=strategy.name,
                description=strategy.description,
                strategy_type=strategy.strategy_type,
                parameters=eval(strategy.parameters) if strategy.parameters else {},
                symbols=strategy.symbols.split(',') if strategy.symbols else [],
                is_active=strategy.is_active,
                created_at=strategy.created_at,
                updated_at=strategy.updated_at
            )
            strategy_list.append(strategy_info)
        
        return StrategyListResponse(
            strategies=strategy_list,
            total_count=total_count
        )
        
    except Exception as e:
        logger.error(f"Error getting strategies: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve strategies"
        )


@router.get("/{strategy_id}", response_model=StrategyResponse)
async def get_strategy(
    strategy_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific strategy."""
    try:
        strategy = db.query(Strategy).filter(Strategy.id == strategy_id).first()
        
        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Strategy not found"
            )
        
        strategy_info = StrategyInfo(
            id=strategy.id,
            name=strategy.name,
            description=strategy.description,
            strategy_type=strategy.strategy_type,
            parameters=eval(strategy.parameters) if strategy.parameters else {},
            symbols=strategy.symbols.split(',') if strategy.symbols else [],
            is_active=strategy.is_active,
            created_at=strategy.created_at,
            updated_at=strategy.updated_at
        )
        
        return StrategyResponse(strategy=strategy_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting strategy {strategy_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve strategy"
        )


@router.put("/{strategy_id}", response_model=StrategyResponse)
async def update_strategy(
    strategy_id: int,
    request: UpdateStrategyRequest,
    db: Session = Depends(get_db)
):
    """Update a strategy."""
    try:
        strategy = db.query(Strategy).filter(Strategy.id == strategy_id).first()
        
        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Strategy not found"
            )
        
        # Update fields
        if request.name is not None:
            strategy.name = request.name
        
        if request.description is not None:
            strategy.description = request.description
        
        if request.parameters is not None:
            strategy.parameters = str(request.parameters)
        
        if request.symbols is not None:
            validated_symbols = [validate_symbol(symbol) for symbol in request.symbols]
            strategy.symbols = ','.join(validated_symbols)
        
        if request.is_active is not None:
            strategy.is_active = request.is_active
        
        strategy.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(strategy)
        
        # Convert to response format
        strategy_info = StrategyInfo(
            id=strategy.id,
            name=strategy.name,
            description=strategy.description,
            strategy_type=strategy.strategy_type,
            parameters=eval(strategy.parameters) if strategy.parameters else {},
            symbols=strategy.symbols.split(',') if strategy.symbols else [],
            is_active=strategy.is_active,
            created_at=strategy.created_at,
            updated_at=strategy.updated_at
        )
        
        return StrategyResponse(strategy=strategy_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating strategy {strategy_id}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update strategy"
        )


@router.delete("/{strategy_id}")
async def delete_strategy(
    strategy_id: int,
    db: Session = Depends(get_db)
):
    """Delete a strategy."""
    try:
        strategy = db.query(Strategy).filter(Strategy.id == strategy_id).first()
        
        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Strategy not found"
            )
        
        db.delete(strategy)
        db.commit()
        
        return {"success": True, "message": "Strategy deleted"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting strategy {strategy_id}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete strategy"
        )


@router.post("/validate", response_model=ValidateStrategyResponse)
async def validate_strategy(
    request: ValidateStrategyRequest,
    backtest_service: BacktestService = Depends(get_backtest_service)
):
    """Validate strategy configuration."""
    try:
        issues = []
        recommendations = []
        
        # Validate strategy type
        available_strategies = backtest_service.get_available_strategies()
        strategy_keys = [s['key'] for s in available_strategies]
        
        if request.strategy_type not in strategy_keys:
            issues.append(ValidationIssue(
                type="error",
                field="strategy_type",
                message=f"Unknown strategy type: {request.strategy_type}"
            ))
        
        # Validate symbols
        try:
            validated_symbols = [validate_symbol(symbol) for symbol in request.symbols]
        except Exception as e:
            issues.append(ValidationIssue(
                type="error",
                field="symbols",
                message=f"Invalid symbol format: {str(e)}"
            ))
        
        # Validate parameters
        if request.strategy_type in strategy_keys:
            strategy_info = next(s for s in available_strategies if s['key'] == request.strategy_type)
            default_params = strategy_info['default_parameters']
            
            for param_name, default_value in default_params.items():
                if param_name not in request.parameters:
                    issues.append(ValidationIssue(
                        type="warning",
                        field=f"parameters.{param_name}",
                        message=f"Missing parameter '{param_name}', will use default: {default_value}"
                    ))
                else:
                    # Type validation
                    provided_value = request.parameters[param_name]
                    if type(provided_value) != type(default_value):
                        issues.append(ValidationIssue(
                            type="error",
                            field=f"parameters.{param_name}",
                            message=f"Parameter '{param_name}' should be {type(default_value).__name__}, got {type(provided_value).__name__}"
                        ))
        
        # Add recommendations
        if len(request.symbols) > 10:
            recommendations.append("Consider reducing the number of symbols for better performance")
        
        if len(request.symbols) == 1:
            recommendations.append("Consider testing with multiple symbols for better diversification")
        
        is_valid = not any(issue.type == "error" for issue in issues)
        
        return ValidateStrategyResponse(
            is_valid=is_valid,
            issues=issues,
            recommendations=recommendations
        )
        
    except Exception as e:
        logger.error(f"Error validating strategy: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate strategy"
        )


@router.post("/{strategy_id}/clone", response_model=StrategyResponse)
async def clone_strategy(
    strategy_id: int,
    request: CloneStrategyRequest,
    db: Session = Depends(get_db)
):
    """Clone an existing strategy."""
    try:
        original_strategy = db.query(Strategy).filter(Strategy.id == strategy_id).first()
        
        if not original_strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Strategy not found"
            )
        
        # Parse original parameters
        original_params = eval(original_strategy.parameters) if original_strategy.parameters else {}
        
        # Apply parameter modifications
        new_params = original_params.copy()
        if request.modify_parameters:
            new_params.update(request.modify_parameters)
        
        # Create cloned strategy
        cloned_strategy = Strategy(
            name=request.new_name,
            description=request.new_description or f"Clone of {original_strategy.name}",
            strategy_type=original_strategy.strategy_type,
            parameters=str(new_params),
            symbols=original_strategy.symbols,
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(cloned_strategy)
        db.commit()
        db.refresh(cloned_strategy)
        
        # Convert to response format
        strategy_info = StrategyInfo(
            id=cloned_strategy.id,
            name=cloned_strategy.name,
            description=cloned_strategy.description,
            strategy_type=cloned_strategy.strategy_type,
            parameters=new_params,
            symbols=cloned_strategy.symbols.split(',') if cloned_strategy.symbols else [],
            is_active=cloned_strategy.is_active,
            created_at=cloned_strategy.created_at,
            updated_at=cloned_strategy.updated_at
        )
        
        return StrategyResponse(strategy=strategy_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cloning strategy {strategy_id}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clone strategy"
        )


def _get_parameter_descriptions(strategy_type: str) -> Dict[str, str]:
    """Get parameter descriptions for a strategy type."""
    descriptions = {
        'ma_crossover': {
            'fast_period': 'Period for fast moving average',
            'slow_period': 'Period for slow moving average'
        },
        'rsi_strategy': {
            'rsi_period': 'Period for RSI calculation',
            'oversold_level': 'RSI level considered oversold',
            'overbought_level': 'RSI level considered overbought'
        },
        'bollinger_bands': {
            'period': 'Period for moving average calculation',
            'std_dev': 'Standard deviation multiplier for bands'
        }
    }
    
    return descriptions.get(strategy_type, {})
