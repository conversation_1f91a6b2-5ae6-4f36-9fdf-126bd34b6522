"""
Fyers WebSocket client for real-time market data.
"""

import asyncio
import json
import threading
from typing import List, Dict, Callable, Optional, Any
from datetime import datetime
import websockets

from app.core.config import settings
from app.core.logging import get_logger
from app.integrations.fyers.models import QuoteData

logger = get_logger(__name__)


class FyersWebSocketClient:
    """Fyers WebSocket client for real-time data."""
    
    def __init__(self, access_token: str):
        """
        Initialize WebSocket client.
        
        Args:
            access_token: Fyers access token
        """
        self.access_token = access_token
        self.client_id = settings.fyers.client_id
        
        # WebSocket connection
        self.websocket = None
        self.is_connected = False
        self.is_running = False
        
        # Subscribed symbols
        self.subscribed_symbols = set()
        
        # Callbacks
        self.data_callbacks: List[Callable[[str, QuoteData], None]] = []
        self.connection_callbacks: List[Callable[[bool], None]] = []
        
        # Threading
        self.thread = None
        self.loop = None
    
    def add_data_callback(self, callback: Callable[[str, QuoteData], None]) -> None:
        """
        Add callback for market data updates.
        
        Args:
            callback: Function to call with (symbol, quote_data)
        """
        self.data_callbacks.append(callback)
    
    def add_connection_callback(self, callback: Callable[[bool], None]) -> None:
        """
        Add callback for connection status changes.
        
        Args:
            callback: Function to call with connection status
        """
        self.connection_callbacks.append(callback)
    
    def start(self) -> bool:
        """
        Start WebSocket connection in a separate thread.
        
        Returns:
            True if started successfully, False otherwise
        """
        if self.is_running:
            logger.warning("WebSocket client is already running")
            return True
        
        try:
            self.is_running = True
            self.thread = threading.Thread(target=self._run_websocket, daemon=True)
            self.thread.start()
            
            # Wait a moment for connection to establish
            import time
            time.sleep(2)
            
            logger.info("WebSocket client started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start WebSocket client: {e}")
            self.is_running = False
            return False
    
    def stop(self) -> None:
        """Stop WebSocket connection."""
        if not self.is_running:
            return
        
        logger.info("Stopping WebSocket client...")
        self.is_running = False
        
        if self.loop and not self.loop.is_closed():
            asyncio.run_coroutine_threadsafe(self._disconnect(), self.loop)
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        
        logger.info("WebSocket client stopped")
    
    def subscribe_symbols(self, symbols: List[str]) -> bool:
        """
        Subscribe to symbols for real-time data.
        
        Args:
            symbols: List of symbols to subscribe
            
        Returns:
            True if subscription successful, False otherwise
        """
        if not self.is_connected:
            logger.error("WebSocket not connected. Cannot subscribe to symbols.")
            return False
        
        try:
            # Add symbols to subscription set
            self.subscribed_symbols.update(symbols)
            
            # Send subscription message
            subscription_message = {
                "T": "SUB_L2",
                "L2LIST": symbols,
                "SUB_T": 1
            }
            
            if self.loop and not self.loop.is_closed():
                asyncio.run_coroutine_threadsafe(
                    self._send_message(subscription_message), 
                    self.loop
                )
            
            logger.info(f"Subscribed to {len(symbols)} symbols")
            return True
            
        except Exception as e:
            logger.error(f"Failed to subscribe to symbols: {e}")
            return False
    
    def unsubscribe_symbols(self, symbols: List[str]) -> bool:
        """
        Unsubscribe from symbols.
        
        Args:
            symbols: List of symbols to unsubscribe
            
        Returns:
            True if unsubscription successful, False otherwise
        """
        if not self.is_connected:
            logger.error("WebSocket not connected. Cannot unsubscribe from symbols.")
            return False
        
        try:
            # Remove symbols from subscription set
            self.subscribed_symbols.difference_update(symbols)
            
            # Send unsubscription message
            unsubscription_message = {
                "T": "SUB_L2",
                "L2LIST": symbols,
                "SUB_T": 0
            }
            
            if self.loop and not self.loop.is_closed():
                asyncio.run_coroutine_threadsafe(
                    self._send_message(unsubscription_message), 
                    self.loop
                )
            
            logger.info(f"Unsubscribed from {len(symbols)} symbols")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unsubscribe from symbols: {e}")
            return False
    
    def _run_websocket(self) -> None:
        """Run WebSocket connection in event loop."""
        try:
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            
            self.loop.run_until_complete(self._connect_and_listen())
            
        except Exception as e:
            logger.error(f"WebSocket thread error: {e}")
        finally:
            if self.loop and not self.loop.is_closed():
                self.loop.close()
    
    async def _connect_and_listen(self) -> None:
        """Connect to WebSocket and listen for messages."""
        try:
            # Import Fyers WebSocket
            from fyers_apiv3.FyersWebsocket import data_ws
            
            # Create WebSocket connection
            fyers_ws = data_ws.FyersDataSocket(
                access_token=self.access_token,
                log_path="",
                litemode=False,
                write_to_file=False,
                reconnect=True,
                on_connect=self._on_connect,
                on_close=self._on_close,
                on_error=self._on_error,
                on_message=self._on_message
            )
            
            # Connect
            fyers_ws.connect()
            
            # Keep connection alive
            while self.is_running:
                await asyncio.sleep(1)
            
        except Exception as e:
            logger.error(f"WebSocket connection error: {e}")
            self._notify_connection_status(False)
    
    async def _disconnect(self) -> None:
        """Disconnect WebSocket."""
        try:
            if self.websocket:
                await self.websocket.close()
                self.websocket = None
            
            self.is_connected = False
            self._notify_connection_status(False)
            
        except Exception as e:
            logger.error(f"Error disconnecting WebSocket: {e}")
    
    async def _send_message(self, message: Dict[str, Any]) -> None:
        """Send message to WebSocket."""
        try:
            if self.websocket and not self.websocket.closed:
                await self.websocket.send(json.dumps(message))
        except Exception as e:
            logger.error(f"Error sending WebSocket message: {e}")
    
    def _on_connect(self, ws) -> None:
        """Handle WebSocket connection."""
        logger.info("WebSocket connected")
        self.is_connected = True
        self._notify_connection_status(True)
    
    def _on_close(self, ws) -> None:
        """Handle WebSocket disconnection."""
        logger.info("WebSocket disconnected")
        self.is_connected = False
        self._notify_connection_status(False)
    
    def _on_error(self, ws, error) -> None:
        """Handle WebSocket error."""
        logger.error(f"WebSocket error: {error}")
        self.is_connected = False
        self._notify_connection_status(False)
    
    def _on_message(self, ws, message) -> None:
        """Handle WebSocket message."""
        try:
            # Parse message
            if isinstance(message, str):
                data = json.loads(message)
            else:
                data = message
            
            # Process market data
            if isinstance(data, dict) and 'symbol' in data:
                symbol = data['symbol']
                
                # Create QuoteData from WebSocket message
                quote_data = QuoteData(
                    symbol=symbol,
                    ltp=float(data.get('ltp', 0)),
                    open_price=float(data.get('open_price', 0)),
                    high_price=float(data.get('high_price', 0)),
                    low_price=float(data.get('low_price', 0)),
                    close_price=float(data.get('close_price', 0)),
                    prev_close=float(data.get('prev_close_price', 0)),
                    volume=int(data.get('volume', 0)),
                    change=float(data.get('ch', 0)),
                    change_percent=float(data.get('chp', 0)),
                    timestamp=datetime.now()
                )
                
                # Notify callbacks
                self._notify_data_callbacks(symbol, quote_data)
            
        except Exception as e:
            logger.error(f"Error processing WebSocket message: {e}")
    
    def _notify_connection_status(self, connected: bool) -> None:
        """Notify connection status callbacks."""
        for callback in self.connection_callbacks:
            try:
                callback(connected)
            except Exception as e:
                logger.error(f"Error in connection callback: {e}")
    
    def _notify_data_callbacks(self, symbol: str, quote_data: QuoteData) -> None:
        """Notify data callbacks."""
        for callback in self.data_callbacks:
            try:
                callback(symbol, quote_data)
            except Exception as e:
                logger.error(f"Error in data callback: {e}")
    
    @property
    def connection_status(self) -> Dict[str, Any]:
        """Get connection status information."""
        return {
            'connected': self.is_connected,
            'running': self.is_running,
            'subscribed_symbols': len(self.subscribed_symbols),
            'symbols': list(self.subscribed_symbols)
        }
