{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Python\\\\signal_stack\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport Dashboard from './components/Dashboard';\nimport MarketData from './components/MarketData';\nimport StrategyManager from './components/StrategyManager';\nimport BacktestingDashboard from './components/BacktestingDashboard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#1976d2'\n    },\n    secondary: {\n      main: '#dc004e'\n    },\n    success: {\n      main: '#2e7d32'\n    },\n    error: {\n      main: '#d32f2f'\n    }\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif'\n  }\n});\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/market-data\",\n          element: /*#__PURE__*/_jsxDEV(MarketData, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/strategies\",\n          element: /*#__PURE__*/_jsxDEV(StrategyManager, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/backtesting\",\n          element: /*#__PURE__*/_jsxDEV(BacktestingDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "ThemeProvider", "createTheme", "CssBaseline", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Dashboard", "MarketData", "StrategyManager", "BacktestingDashboard", "jsxDEV", "_jsxDEV", "theme", "palette", "mode", "primary", "main", "secondary", "success", "error", "typography", "fontFamily", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport Dashboard from './components/Dashboard';\nimport MarketData from './components/MarketData';\nimport StrategyManager from './components/StrategyManager';\nimport BacktestingDashboard from './components/BacktestingDashboard';\n\nconst theme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#1976d2',\n    },\n    secondary: {\n      main: '#dc004e',\n    },\n    success: {\n      main: '#2e7d32',\n    },\n    error: {\n      main: '#d32f2f',\n    },\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n  },\n});\n\nfunction App() {\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <Router>\n        <Routes>\n          <Route path=\"/\" element={<Navigate to=\"/dashboard\" replace />} />\n          <Route path=\"/dashboard\" element={<Dashboard />} />\n          <Route path=\"/market-data\" element={<MarketData />} />\n          <Route path=\"/strategies\" element={<StrategyManager />} />\n          <Route path=\"/backtesting\" element={<BacktestingDashboard />} />\n        </Routes>\n      </Router>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAoB,OAAO;AACvC,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,oBAAoB,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMC,KAAK,GAAGb,WAAW,CAAC;EACxBc,OAAO,EAAE;IACPC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTD,IAAI,EAAE;IACR,CAAC;IACDE,OAAO,EAAE;MACPF,IAAI,EAAE;IACR,CAAC;IACDG,KAAK,EAAE;MACLH,IAAI,EAAE;IACR;EACF,CAAC;EACDI,UAAU,EAAE;IACVC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,SAASC,GAAGA,CAAA,EAAG;EACb,oBACEX,OAAA,CAACb,aAAa;IAACc,KAAK,EAAEA,KAAM;IAAAW,QAAA,gBAC1BZ,OAAA,CAACX,WAAW;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfhB,OAAA,CAACT,MAAM;MAAAqB,QAAA,eACLZ,OAAA,CAACR,MAAM;QAAAoB,QAAA,gBACLZ,OAAA,CAACP,KAAK;UAACwB,IAAI,EAAC,GAAG;UAACC,OAAO,eAAElB,OAAA,CAACN,QAAQ;YAACyB,EAAE,EAAC,YAAY;YAACC,OAAO;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjEhB,OAAA,CAACP,KAAK;UAACwB,IAAI,EAAC,YAAY;UAACC,OAAO,eAAElB,OAAA,CAACL,SAAS;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnDhB,OAAA,CAACP,KAAK;UAACwB,IAAI,EAAC,cAAc;UAACC,OAAO,eAAElB,OAAA,CAACJ,UAAU;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDhB,OAAA,CAACP,KAAK;UAACwB,IAAI,EAAC,aAAa;UAACC,OAAO,eAAElB,OAAA,CAACH,eAAe;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DhB,OAAA,CAACP,KAAK;UAACwB,IAAI,EAAC,cAAc;UAACC,OAAO,eAAElB,OAAA,CAACF,oBAAoB;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACK,EAAA,GAfQV,GAAG;AAiBZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}