"""
Sample trading strategies for backtesting.
"""

from typing import List, Dict, Any
import pandas as pd
import numpy as np

from .strategy import BaseStrategy, TradingSignal, SignalType
from app.services.indicator_service import IndicatorService
from app.core.logging import get_logger

logger = get_logger(__name__)


class MovingAverageCrossoverStrategy(BaseStrategy):
    """Simple moving average crossover strategy."""

    def __init__(self, symbol: str, fast_period: int = 10, slow_period: int = 20):
        """
        Initialize MA crossover strategy.

        Args:
            symbol: Symbol to trade
            fast_period: Fast moving average period
            slow_period: Slow moving average period
        """
        super().__init__("MA_Crossover", {
            "symbol": symbol,
            "fast_period": fast_period,
            "slow_period": slow_period
        })
        self.symbol = symbol
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.indicator_service = IndicatorService()

        # Strategy state
        self.fast_ma = []
        self.slow_ma = []
        self.last_signal = None
    
    def initialize(self, data: pd.DataFrame) -> None:
        """Initialize strategy with historical data."""
        logger.info(f"Initializing {self.name} strategy")
        
        # Calculate moving averages for the entire dataset
        fast_ma_result = self.indicator_service.calculate_indicator(
            "SMA", data, {"period": self.fast_period}
        )
        slow_ma_result = self.indicator_service.calculate_indicator(
            "SMA", data, {"period": self.slow_period}
        )
        
        if fast_ma_result and slow_ma_result:
            self.fast_ma = fast_ma_result.get("values", [])
            self.slow_ma = slow_ma_result.get("values", [])
            logger.info(f"Calculated {len(self.fast_ma)} fast MA and {len(self.slow_ma)} slow MA values")
        else:
            logger.error("Failed to calculate moving averages")
    
    def generate_signals(self, data: pd.DataFrame, current_index: int) -> List[TradingSignal]:
        """Generate trading signals."""
        signals = []
        
        # Need enough data for both MAs
        if current_index < self.slow_period:
            return signals
        
        # Calculate MA indices (accounting for the lookback period)
        fast_ma_index = current_index - self.fast_period + 1
        slow_ma_index = current_index - self.slow_period + 1
        
        if (fast_ma_index < 0 or slow_ma_index < 0 or 
            fast_ma_index >= len(self.fast_ma) or slow_ma_index >= len(self.slow_ma)):
            return signals
        
        current_fast_ma = self.fast_ma[fast_ma_index]
        current_slow_ma = self.slow_ma[slow_ma_index]
        
        # Get previous values for crossover detection
        if fast_ma_index > 0 and slow_ma_index > 0:
            prev_fast_ma = self.fast_ma[fast_ma_index - 1]
            prev_slow_ma = self.slow_ma[slow_ma_index - 1]
            
            current_bar = data.iloc[current_index]
            current_price = float(current_bar['close'])
            timestamp = current_bar['timestamp']
            
            # Bullish crossover: fast MA crosses above slow MA
            if (prev_fast_ma <= prev_slow_ma and current_fast_ma > current_slow_ma and 
                self.last_signal != "BUY"):
                
                signal = TradingSignal(
                    symbol=self.symbol,
                    signal_type=SignalType.BUY,
                    timestamp=timestamp,
                    price=current_price,
                    confidence=0.8,
                    metadata={
                        "fast_ma": current_fast_ma,
                        "slow_ma": current_slow_ma,
                        "crossover_type": "bullish"
                    }
                )
                signals.append(signal)
                self.last_signal = "BUY"
                logger.debug(f"BUY signal at {timestamp}: price={current_price:.2f}")
            
            # Bearish crossover: fast MA crosses below slow MA
            elif (prev_fast_ma >= prev_slow_ma and current_fast_ma < current_slow_ma and 
                  self.last_signal != "SELL"):
                
                signal = TradingSignal(
                    symbol=self.symbol,
                    signal_type=SignalType.SELL,
                    timestamp=timestamp,
                    price=current_price,
                    confidence=0.8,
                    metadata={
                        "fast_ma": current_fast_ma,
                        "slow_ma": current_slow_ma,
                        "crossover_type": "bearish"
                    }
                )
                signals.append(signal)
                self.last_signal = "SELL"
                logger.debug(f"SELL signal at {timestamp}: price={current_price:.2f}")
        
        return signals


class RSIStrategy(BaseStrategy):
    """RSI-based mean reversion strategy."""
    
    def __init__(self, symbol: str, rsi_period: int = 14, oversold_level: float = 30, overbought_level: float = 70):
        """
        Initialize RSI strategy.

        Args:
            symbol: Symbol to trade
            rsi_period: RSI calculation period
            oversold_level: RSI oversold threshold
            overbought_level: RSI overbought threshold
        """
        super().__init__("RSI_Strategy", {
            "symbol": symbol,
            "rsi_period": rsi_period,
            "oversold_level": oversold_level,
            "overbought_level": overbought_level
        })
        self.symbol = symbol
        self.rsi_period = rsi_period
        self.oversold_level = oversold_level
        self.overbought_level = overbought_level
        self.indicator_service = IndicatorService()
        
        # Strategy state
        self.rsi_values = []
        self.last_signal = None
    
    def initialize(self, data: pd.DataFrame) -> None:
        """Initialize strategy with historical data."""
        logger.info(f"Initializing {self.name} strategy")
        
        # Calculate RSI for the entire dataset
        rsi_result = self.indicator_service.calculate_indicator(
            "RSI", data, {"period": self.rsi_period}
        )
        
        if rsi_result:
            self.rsi_values = rsi_result.get("values", [])
            logger.info(f"Calculated {len(self.rsi_values)} RSI values")
        else:
            logger.error("Failed to calculate RSI")
    
    def generate_signals(self, data: pd.DataFrame, current_index: int) -> List[TradingSignal]:
        """Generate trading signals based on RSI levels."""
        signals = []
        
        # Need enough data for RSI calculation
        if current_index < self.rsi_period:
            return signals
        
        # Calculate RSI index
        rsi_index = current_index - self.rsi_period
        
        if rsi_index < 0 or rsi_index >= len(self.rsi_values):
            return signals
        
        current_rsi = self.rsi_values[rsi_index]
        current_bar = data.iloc[current_index]
        current_price = float(current_bar['close'])
        timestamp = current_bar['timestamp']
        
        # Buy signal: RSI crosses above oversold level
        if current_rsi > self.oversold_level and self.last_signal != "BUY":
            # Check if RSI was previously below oversold level
            if rsi_index > 0 and self.rsi_values[rsi_index - 1] <= self.oversold_level:
                signal = TradingSignal(
                    symbol=self.symbol,
                    signal_type=SignalType.BUY,
                    timestamp=timestamp,
                    price=current_price,
                    confidence=0.7,
                    metadata={
                        "rsi": current_rsi,
                        "signal_reason": "oversold_bounce"
                    }
                )
                signals.append(signal)
                self.last_signal = "BUY"
                logger.debug(f"BUY signal at {timestamp}: RSI={current_rsi:.2f}")
        
        # Sell signal: RSI crosses below overbought level
        elif current_rsi < self.overbought_level and self.last_signal != "SELL":
            # Check if RSI was previously above overbought level
            if rsi_index > 0 and self.rsi_values[rsi_index - 1] >= self.overbought_level:
                signal = TradingSignal(
                    symbol=self.symbol,
                    signal_type=SignalType.SELL,
                    timestamp=timestamp,
                    price=current_price,
                    confidence=0.7,
                    metadata={
                        "rsi": current_rsi,
                        "signal_reason": "overbought_reversal"
                    }
                )
                signals.append(signal)
                self.last_signal = "SELL"
                logger.debug(f"SELL signal at {timestamp}: RSI={current_rsi:.2f}")
        
        return signals


class BollingerBandsStrategy(BaseStrategy):
    """Bollinger Bands mean reversion strategy."""
    
    def __init__(self, symbol: str, period: int = 20, std_dev: float = 2.0):
        """
        Initialize Bollinger Bands strategy.

        Args:
            symbol: Symbol to trade
            period: Moving average period
            std_dev: Standard deviation multiplier
        """
        super().__init__("Bollinger_Bands", {
            "symbol": symbol,
            "period": period,
            "std_dev": std_dev
        })
        self.symbol = symbol
        self.period = period
        self.std_dev = std_dev
        self.indicator_service = IndicatorService()

        # Strategy state
        self.bb_data = {}
        self.last_signal = None
    
    def initialize(self, data: pd.DataFrame) -> None:
        """Initialize strategy with historical data."""
        logger.info(f"Initializing {self.name} strategy")
        
        # Calculate Bollinger Bands
        bb_result = self.indicator_service.calculate_indicator(
            "BollingerBands", data, {"period": self.period, "std_dev": self.std_dev}
        )
        
        if bb_result:
            self.bb_data = bb_result
            logger.info(f"Calculated Bollinger Bands with {len(bb_result.get('upper', []))} values")
        else:
            logger.error("Failed to calculate Bollinger Bands")
    
    def generate_signals(self, data: pd.DataFrame, current_index: int) -> List[TradingSignal]:
        """Generate trading signals based on Bollinger Bands."""
        signals = []
        
        # Need enough data for BB calculation
        if current_index < self.period:
            return signals
        
        # Calculate BB index
        bb_index = current_index - self.period + 1
        
        if (bb_index < 0 or bb_index >= len(self.bb_data.get('upper', []))):
            return signals
        
        upper_band = self.bb_data['upper'][bb_index]
        lower_band = self.bb_data['lower'][bb_index]
        middle_band = self.bb_data['middle'][bb_index]
        
        current_bar = data.iloc[current_index]
        current_price = float(current_bar['close'])
        timestamp = current_bar['timestamp']
        
        # Buy signal: price touches or goes below lower band
        if current_price <= lower_band and self.last_signal != "BUY":
            signal = TradingSignal(
                symbol=self.symbol,
                signal_type=SignalType.BUY,
                timestamp=timestamp,
                price=current_price,
                confidence=0.75,
                metadata={
                    "upper_band": upper_band,
                    "lower_band": lower_band,
                    "middle_band": middle_band,
                    "signal_reason": "lower_band_touch"
                }
            )
            signals.append(signal)
            self.last_signal = "BUY"
            logger.debug(f"BUY signal at {timestamp}: price={current_price:.2f}, lower_band={lower_band:.2f}")
        
        # Sell signal: price touches or goes above upper band
        elif current_price >= upper_band and self.last_signal != "SELL":
            signal = TradingSignal(
                symbol=self.symbol,
                signal_type=SignalType.SELL,
                timestamp=timestamp,
                price=current_price,
                confidence=0.75,
                metadata={
                    "upper_band": upper_band,
                    "lower_band": lower_band,
                    "middle_band": middle_band,
                    "signal_reason": "upper_band_touch"
                }
            )
            signals.append(signal)
            self.last_signal = "SELL"
            logger.debug(f"SELL signal at {timestamp}: price={current_price:.2f}, upper_band={upper_band:.2f}")
        
        return signals
