"""
Volume-based technical indicators.
"""

from typing import Dict, Any, Union, List
import pandas as pd
import numpy as np

from .base import BaseIndicator


class OBV(BaseIndicator):
    """On-Balance Volume indicator."""
    
    def __init__(self):
        """Initialize OBV."""
        super().__init__("OBV", {})
    
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calculate OBV values."""
        df = self._prepare_data(data)
        
        if len(df) < 2:
            return {"values": [], "timestamps": []}
        
        # Calculate price changes
        price_change = df['close'].diff()
        
        # Calculate OBV
        obv = []
        obv_value = 0
        
        for i in range(len(df)):
            if i == 0:
                obv.append(df['volume'].iloc[i])
                obv_value = df['volume'].iloc[i]
            else:
                if price_change.iloc[i] > 0:
                    obv_value += df['volume'].iloc[i]
                elif price_change.iloc[i] < 0:
                    obv_value -= df['volume'].iloc[i]
                # If price_change == 0, OBV remains the same
                obv.append(obv_value)
        
        # Store values
        self.values = obv
        self.timestamps = df['timestamp'].tolist()
        self.is_ready = len(self.values) > 0
        
        return {
            "values": self.values,
            "timestamps": self.timestamps
        }


class VWAP(BaseIndicator):
    """Volume Weighted Average Price indicator."""
    
    def __init__(self):
        """Initialize VWAP."""
        super().__init__("VWAP", {})
    
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calculate VWAP values."""
        df = self._prepare_data(data)
        
        if len(df) < 1:
            return {"values": [], "timestamps": []}
        
        # Calculate typical price
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        
        # Calculate VWAP
        cumulative_volume = df['volume'].cumsum()
        cumulative_price_volume = (typical_price * df['volume']).cumsum()
        
        vwap = cumulative_price_volume / cumulative_volume
        
        # Store values
        self.values = vwap.tolist()
        self.timestamps = df['timestamp'].tolist()
        self.is_ready = len(self.values) > 0
        
        return {
            "values": self.values,
            "timestamps": self.timestamps
        }


class MFI(BaseIndicator):
    """Money Flow Index indicator."""
    
    def __init__(self, period: int = 14):
        """
        Initialize MFI.
        
        Args:
            period: MFI calculation period
        """
        super().__init__("MFI", {"period": period})
        self.period = period
    
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calculate MFI values."""
        df = self._prepare_data(data)
        
        if len(df) < self.period + 1:
            return {"values": [], "timestamps": []}
        
        # Calculate typical price
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        
        # Calculate raw money flow
        raw_money_flow = typical_price * df['volume']
        
        # Determine positive and negative money flow
        price_change = typical_price.diff()
        positive_flow = raw_money_flow.where(price_change > 0, 0)
        negative_flow = raw_money_flow.where(price_change < 0, 0)
        
        # Calculate money flow ratio
        positive_mf = positive_flow.rolling(window=self.period).sum()
        negative_mf = negative_flow.rolling(window=self.period).sum()
        
        money_flow_ratio = positive_mf / negative_mf
        
        # Calculate MFI
        mfi = 100 - (100 / (1 + money_flow_ratio))
        
        # Store values
        self.values = mfi.dropna().tolist()
        self.timestamps = df['timestamp'][self.period:].tolist()
        self.is_ready = len(self.values) > 0
        
        return {
            "values": self.values,
            "timestamps": self.timestamps,
            "period": self.period
        }
