"""
Paper trading API schemas.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field

from app.api.schemas.common import BaseResponse


class CreateSessionRequest(BaseModel):
    """Create paper trading session request."""
    name: str = Field(description="Session name")
    initial_cash: float = Field(default=100000.0, description="Initial cash amount")
    commission_rate: float = Field(default=0.001, description="Commission rate")
    update_interval: int = Field(default=60, description="Price update interval in seconds")


class CreateSessionResponse(BaseResponse):
    """Create session response."""
    session_id: str
    name: str
    initial_cash: float


class SessionInfo(BaseModel):
    """Paper trading session information."""
    id: str
    name: str
    initial_cash: float
    current_cash: float
    commission_rate: float
    created_at: datetime
    is_running: bool
    current_value: Optional[float] = None
    total_return: Optional[float] = None
    total_trades: Optional[int] = None
    open_positions: Optional[int] = None


class SessionListResponse(BaseResponse):
    """Session list response."""
    sessions: List[SessionInfo]


class AddStrategyRequest(BaseModel):
    """Add strategy to session request."""
    strategy_type: str = Field(description="Strategy type")
    symbols: List[str] = Field(description="Symbols to watch")
    parameters: Optional[Dict[str, Any]] = Field(default=None, description="Strategy parameters")


class PlaceOrderRequest(BaseModel):
    """Place order request."""
    symbol: str = Field(description="Symbol to trade")
    side: str = Field(description="Order side (buy/sell)")
    order_type: str = Field(description="Order type (market/limit/stop)")
    quantity: int = Field(description="Order quantity")
    price: Optional[float] = Field(default=None, description="Order price (for limit orders)")


class PlaceOrderResponse(BaseResponse):
    """Place order response."""
    order_id: Optional[str] = None
    symbol: str
    side: str
    quantity: int
    status: str


class PositionInfo(BaseModel):
    """Position information."""
    id: str
    symbol: str
    side: str
    quantity: int
    avg_price: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    unrealized_pnl_percent: float
    entry_timestamp: datetime


class OrderInfo(BaseModel):
    """Order information."""
    id: str
    symbol: str
    side: str
    order_type: str
    quantity: int
    price: Optional[float]
    status: str
    filled_quantity: int
    avg_fill_price: float
    created_at: datetime


class SessionStatusResponse(BaseResponse):
    """Session status response."""
    session_id: str
    is_running: bool
    cash: float
    total_value: float
    total_pnl: float
    positions: List[PositionInfo]
    pending_orders: List[OrderInfo]
    performance: Dict[str, Any]


class TradeRecord(BaseModel):
    """Trade record."""
    id: str
    symbol: str
    side: str
    quantity: int
    entry_price: float
    exit_price: float
    entry_time: datetime
    exit_time: datetime
    pnl: float
    return_percentage: float
    duration_days: float


class PerformanceResponse(BaseResponse):
    """Performance report response."""
    session_id: str
    engine_status: Dict[str, Any]
    portfolio_performance: Dict[str, Any]
    strategy_performance: Dict[str, Any]
    recent_trades: List[TradeRecord]
    value_history: List[Dict[str, Any]]


class SimulateSignalRequest(BaseModel):
    """Simulate signal request."""
    symbol: str = Field(description="Symbol")
    signal_type: str = Field(description="Signal type (buy/sell)")
    price: float = Field(description="Signal price")
    quantity: int = Field(default=100, description="Signal quantity")


class SessionHistoryResponse(BaseResponse):
    """Session history response."""
    session_id: str
    trades: List[TradeRecord]
    order_history: List[OrderInfo]
    value_history: List[Dict[str, Any]]
    performance_summary: Dict[str, Any]


class ExportDataResponse(BaseResponse):
    """Export session data response."""
    session_id: str
    export_timestamp: datetime
    data: Dict[str, Any]
