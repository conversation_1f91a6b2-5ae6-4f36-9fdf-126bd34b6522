"""
Database connection and session management.
"""

from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from typing import Generator
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# Database engine with connection pooling
engine = create_engine(
    settings.database.url,
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    echo=settings.api.debug
)

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for all models
Base = declarative_base()


def get_db() -> Generator[Session, None, None]:
    """
    Dependency to get database session.
    
    Yields:
        Database session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def init_database():
    """
    Initialize database with TimescaleDB extension and create tables.
    """
    logger.info("Initializing database...")
    
    try:
        # Create TimescaleDB extension if enabled
        if settings.database.timescale_enabled:
            with engine.connect() as conn:
                # Check if TimescaleDB extension exists
                result = conn.execute(text(
                    "SELECT 1 FROM pg_extension WHERE extname = 'timescaledb'"
                ))
                
                if not result.fetchone():
                    logger.info("Creating TimescaleDB extension...")
                    conn.execute(text("CREATE EXTENSION IF NOT EXISTS timescaledb"))
                    conn.commit()
                else:
                    logger.info("TimescaleDB extension already exists")
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database initialization completed successfully")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise


def check_database_connection() -> bool:
    """
    Check if database connection is working.
    
    Returns:
        True if connection is successful, False otherwise
    """
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        logger.info("Database connection successful")
        return True
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False
