"""
Market data API schemas.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field

from app.api.schemas.common import (
    BaseResponse, 
    PaginatedResponse, 
    OHLCVData, 
    QuoteData, 
    SymbolInfo,
    DateRangeParams
)


class MarketDataRequest(DateRangeParams):
    """Market data request parameters."""
    symbol: str = Field(description="Trading symbol")
    timeframe: str = Field(default="1m", description="Data timeframe (1m, 5m, 15m, 30m, 1h, 1d)")
    limit: Optional[int] = Field(default=None, ge=1, le=10000, description="Maximum number of records")


class MarketDataResponse(BaseResponse):
    """Market data response."""
    symbol: str
    timeframe: str
    data: List[OHLCVData]
    total_records: int
    start_date: datetime
    end_date: datetime


class SymbolListResponse(PaginatedResponse):
    """Symbol list response."""
    symbols: List[SymbolInfo]


class QuoteResponse(BaseResponse):
    """Real-time quote response."""
    quote: QuoteData


class MultiQuoteResponse(BaseResponse):
    """Multiple quotes response."""
    quotes: Dict[str, QuoteData]
    timestamp: datetime


class SymbolStatsResponse(BaseResponse):
    """Symbol statistics response."""
    symbol: str
    stats: Dict[str, Any]


class TimeframeListResponse(BaseResponse):
    """Available timeframes response."""
    timeframes: List[str] = [
        "1m", "5m", "10m", "15m", "30m", "1h", "2h", "4h", "1d"
    ]


class MarketStatusResponse(BaseResponse):
    """Market status response."""
    is_open: bool
    next_open: Optional[datetime] = None
    next_close: Optional[datetime] = None
    timezone: str = "Asia/Kolkata"
