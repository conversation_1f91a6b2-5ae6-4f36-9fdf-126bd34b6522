"""
Pytest configuration and fixtures for the Signal Stack testing suite.
"""

import pytest
import asyncio
from typing import Generator, Dict, Any
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

from app.database.base import Base
from app.database.connection import get_db
from app.main import app
from app.services.data_service import DataService
from app.services.market_data_service import MarketDataService
from app.services.indicator_service import IndicatorService
from app.services.backtest_service import BacktestService
from app.services.paper_trading_service import PaperTradingService
from app.database.models import Symbol, StockOHLCV


# Test database configuration
TEST_DATABASE_URL = "sqlite:///./test_signal_stack.db"


@pytest.fixture(scope="session")
def test_engine():
    """Create test database engine."""
    engine = create_engine(TEST_DATABASE_URL, connect_args={"check_same_thread": False})
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def test_db(test_engine):
    """Create test database session."""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture(scope="function")
def client(test_db):
    """Create test client with database override."""
    def override_get_db():
        try:
            yield test_db
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()


@pytest.fixture
def sample_symbol(test_db) -> Symbol:
    """Create a sample symbol for testing."""
    symbol = Symbol(
        symbol="NIFTY",
        name="NIFTY 50 Index",
        exchange="NSE",
        segment="INDEX",
        instrument_type="INDEX",
        tick_size=0.05,
        lot_size=1,
        is_active=True,
        fyers_symbol="NSE:NIFTY50-INDEX"
    )
    test_db.add(symbol)
    test_db.commit()
    test_db.refresh(symbol)
    return symbol


@pytest.fixture
def sample_ohlcv_data(test_db, sample_symbol) -> pd.DataFrame:
    """Create sample OHLCV data for testing."""
    # Generate 100 days of sample data
    dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
    
    # Generate realistic price data
    np.random.seed(42)  # For reproducible tests
    base_price = 20000
    returns = np.random.normal(0.001, 0.02, len(dates))  # Daily returns
    
    prices = [base_price]
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # Create OHLCV data
    ohlcv_data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        high = close * (1 + abs(np.random.normal(0, 0.01)))
        low = close * (1 - abs(np.random.normal(0, 0.01)))
        open_price = close * (1 + np.random.normal(0, 0.005))
        volume = int(np.random.normal(1000000, 200000))
        
        # Ensure OHLC relationships are valid
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        ohlcv_record = StockOHLCV(
            symbol_id=sample_symbol.id,
            timestamp=date,
            open=open_price,
            high=high,
            low=low,
            close=close,
            volume=volume
        )
        ohlcv_data.append(ohlcv_record)
    
    # Add to database
    test_db.add_all(ohlcv_data)
    test_db.commit()
    
    # Return as DataFrame
    df_data = {
        'timestamp': dates,
        'open': [record.open for record in ohlcv_data],
        'high': [record.high for record in ohlcv_data],
        'low': [record.low for record in ohlcv_data],
        'close': [record.close for record in ohlcv_data],
        'volume': [record.volume for record in ohlcv_data]
    }
    
    df = pd.DataFrame(df_data)
    df.set_index('timestamp', inplace=True)
    return df


@pytest.fixture
def data_service(test_db) -> DataService:
    """Create DataService instance for testing."""
    return DataService(test_db)


@pytest.fixture
def market_data_service(test_db) -> MarketDataService:
    """Create MarketDataService instance for testing."""
    return MarketDataService(test_db)


@pytest.fixture
def indicator_service() -> IndicatorService:
    """Create IndicatorService instance for testing."""
    return IndicatorService()


@pytest.fixture
def backtest_service(test_db) -> BacktestService:
    """Create BacktestService instance for testing."""
    return BacktestService(test_db)


@pytest.fixture
def paper_trading_service(test_db) -> PaperTradingService:
    """Create PaperTradingService instance for testing."""
    return PaperTradingService(test_db)


@pytest.fixture
def mock_fyers_data() -> Dict[str, Any]:
    """Mock Fyers API response data."""
    return {
        'symbol': 'NSE:NIFTY50-INDEX',
        'candles': [
            ['2024-01-01T09:15:00+05:30', 20000, 20100, 19950, 20050, 1000000],
            ['2024-01-01T09:16:00+05:30', 20050, 20150, 20000, 20100, 1100000],
            ['2024-01-01T09:17:00+05:30', 20100, 20200, 20050, 20150, 1200000],
        ]
    }


@pytest.fixture
def sample_strategy_config() -> Dict[str, Any]:
    """Sample strategy configuration for testing."""
    return {
        'name': 'test_strategy',
        'type': 'technical',
        'parameters': {
            'short_window': 10,
            'long_window': 20,
            'rsi_period': 14,
            'rsi_oversold': 30,
            'rsi_overbought': 70
        },
        'symbols': ['NIFTY'],
        'timeframe': '1m'
    }


@pytest.fixture
def sample_backtest_config() -> Dict[str, Any]:
    """Sample backtest configuration for testing."""
    return {
        'initial_capital': 100000,
        'commission': 0.001,
        'slippage': 0.0005,
        'position_sizing_method': 'fixed_amount',
        'position_size': 10000,
        'max_position_size': 0.1
    }


@pytest.fixture
def sample_paper_trading_config() -> Dict[str, Any]:
    """Sample paper trading configuration for testing."""
    return {
        'name': 'Test Session',
        'initial_capital': 100000,
        'symbols': ['NIFTY'],
        'strategy_name': 'ma_crossover',
        'strategy_parameters': {
            'short_window': 5,
            'long_window': 10
        }
    }


@pytest.fixture
def auth_headers() -> Dict[str, str]:
    """Authentication headers for API testing."""
    return {
        'X-API-Key': 'dev-key-123',
        'Content-Type': 'application/json'
    }


@pytest.fixture
def sample_market_data_update() -> Dict[str, Any]:
    """Sample market data update for real-time testing."""
    return {
        'symbol': 'NIFTY',
        'price': 20000.0,
        'volume': 1000000,
        'timestamp': datetime.now().isoformat(),
        'bid': 19999.5,
        'ask': 20000.5,
        'high': 20100.0,
        'low': 19900.0,
        'open': 19950.0
    }


# Async fixtures for async testing
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# Performance testing fixtures
@pytest.fixture
def performance_timer():
    """Timer fixture for performance testing."""
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = datetime.now()
        
        def stop(self):
            self.end_time = datetime.now()
        
        @property
        def elapsed(self) -> float:
            if self.start_time and self.end_time:
                return (self.end_time - self.start_time).total_seconds()
            return 0.0
    
    return Timer()


# Test data generators
def generate_random_ohlcv(
    symbol: str,
    start_date: datetime,
    periods: int,
    base_price: float = 20000,
    volatility: float = 0.02
) -> pd.DataFrame:
    """Generate random OHLCV data for testing."""
    dates = pd.date_range(start=start_date, periods=periods, freq='D')
    
    np.random.seed(42)
    returns = np.random.normal(0.001, volatility, len(dates))
    
    prices = [base_price]
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    data = []
    for date, close in zip(dates, prices):
        high = close * (1 + abs(np.random.normal(0, 0.01)))
        low = close * (1 - abs(np.random.normal(0, 0.01)))
        open_price = close * (1 + np.random.normal(0, 0.005))
        volume = int(np.random.normal(1000000, 200000))
        
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        data.append({
            'timestamp': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    return df


# Test markers
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.e2e = pytest.mark.e2e
pytest.mark.performance = pytest.mark.performance
pytest.mark.slow = pytest.mark.slow
