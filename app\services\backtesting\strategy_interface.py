"""
Generic Strategy Interface for the Reference Backtesting Framework.
This module provides base classes and utilities for creating strategy adapters.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
import importlib.util
import sys
import os

from app.core.logging import get_logger

logger = get_logger(__name__)


class StrategyAdapter(ABC):
    """
    Abstract base class for strategy adapters.
    This allows different strategy formats to work with the reference framework.
    """
    
    @abstractmethod
    def get_strategy_class(self):
        """Return the strategy class that can be used with backtesting library."""
        pass
    
    @abstractmethod
    def get_strategy_config(self) -> Optional[Dict[str, Any]]:
        """Return strategy configuration if available."""
        pass
    
    @abstractmethod
    def validate_strategy(self) -> bool:
        """Validate that the strategy is properly configured."""
        pass


class BacktestingLibraryAdapter(StrategyAdapter):
    """
    Adapter for strategies that are already compatible with the backtesting library.
    This is for strategies that inherit from backtesting.Strategy.
    """
    
    def __init__(self, strategy_module: str, strategy_class: str, config_path: Optional[str] = None):
        """
        Initialize adapter for backtesting library compatible strategies.
        
        Args:
            strategy_module: Path to strategy module file
            strategy_class: Name of the strategy class
            config_path: Optional path to configuration file
        """
        self.strategy_module = strategy_module
        self.strategy_class = strategy_class
        self.config_path = config_path
        self._strategy_cls = None
        self._config = None
        
    def get_strategy_class(self):
        """Load and return the strategy class."""
        if self._strategy_cls is None:
            self._strategy_cls = self._load_strategy_class()
        return self._strategy_cls
    
    def get_strategy_config(self) -> Optional[Dict[str, Any]]:
        """Load and return strategy configuration."""
        if self._config is None and self.config_path:
            self._config = self._load_config()
        return self._config
    
    def validate_strategy(self) -> bool:
        """Validate that the strategy class exists and is properly configured."""
        try:
            strategy_cls = self.get_strategy_class()
            
            # Check if it's a valid backtesting strategy
            from backtesting import Strategy
            if not issubclass(strategy_cls, Strategy):
                logger.error(f"Strategy class {self.strategy_class} does not inherit from backtesting.Strategy")
                return False
            
            # Check required methods
            required_methods = ['init', 'next']
            for method in required_methods:
                if not hasattr(strategy_cls, method):
                    logger.error(f"Strategy class {self.strategy_class} missing required method: {method}")
                    return False
            
            logger.info(f"✅ Strategy validation passed for {self.strategy_class}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Strategy validation failed: {e}")
            return False
    
    def _load_strategy_class(self):
        """Load strategy class from module file."""
        try:
            # Handle different module path formats
            if os.path.isabs(self.strategy_module):
                module_path = Path(self.strategy_module)
            else:
                # Try relative to project root
                project_root = Path(__file__).parent.parent.parent.parent
                module_path = project_root / self.strategy_module
                
                if not module_path.exists():
                    # Try relative to current working directory
                    module_path = Path(self.strategy_module)
            
            if not module_path.exists():
                raise FileNotFoundError(f"Strategy module not found: {self.strategy_module}")
            
            # Load module dynamically
            spec = importlib.util.spec_from_file_location("strategy_module", module_path)
            module = importlib.util.module_from_spec(spec)
            
            # Add module directory to sys.path temporarily
            module_dir = str(module_path.parent)
            if module_dir not in sys.path:
                sys.path.insert(0, module_dir)
            
            spec.loader.exec_module(module)
            
            # Get strategy class
            if not hasattr(module, self.strategy_class):
                raise AttributeError(f"Strategy class '{self.strategy_class}' not found in module")
            
            strategy_cls = getattr(module, self.strategy_class)
            
            logger.info(f"✅ Loaded strategy class: {self.strategy_class} from {module_path}")
            return strategy_cls
            
        except Exception as e:
            logger.error(f"❌ Failed to load strategy class: {e}")
            raise
    
    def _load_config(self) -> Optional[Dict[str, Any]]:
        """Load configuration from file."""
        try:
            if not self.config_path:
                return None

            # Handle different config path formats
            if os.path.isabs(self.config_path):
                config_file = Path(self.config_path)
            else:
                # Try relative to project root
                project_root = Path(__file__).parent.parent.parent.parent
                config_file = project_root / self.config_path

                if not config_file.exists():
                    # Try relative to current working directory
                    config_file = Path(self.config_path)

            if not config_file.exists():
                logger.warning(f"Config file not found: {self.config_path}")
                return None

            import yaml
            with open(config_file, 'r') as f:
                config = yaml.safe_load(f)

            logger.info(f"✅ Loaded strategy config from {config_file}")
            return config

        except Exception as e:
            logger.error(f"❌ Failed to load config: {e}")
            return None


class StrategyRegistry:
    """
    Registry for managing different strategy types and their adapters.
    """
    
    def __init__(self):
        self._strategies = {}
        self._register_builtin_strategies()
    
    def register_strategy(
        self, 
        name: str, 
        adapter: StrategyAdapter, 
        description: str = ""
    ):
        """Register a strategy with the registry."""
        self._strategies[name] = {
            'adapter': adapter,
            'description': description,
            'validated': False
        }
        logger.info(f"📝 Registered strategy: {name}")
    
    def get_strategy(self, name: str) -> Optional[StrategyAdapter]:
        """Get a strategy adapter by name."""
        if name not in self._strategies:
            logger.error(f"Strategy '{name}' not found in registry")
            return None
        
        strategy_info = self._strategies[name]
        adapter = strategy_info['adapter']
        
        # Validate strategy if not already validated
        if not strategy_info['validated']:
            if adapter.validate_strategy():
                strategy_info['validated'] = True
            else:
                logger.error(f"Strategy '{name}' failed validation")
                return None
        
        return adapter
    
    def list_strategies(self) -> List[Dict[str, Any]]:
        """List all registered strategies."""
        strategies = []
        for name, info in self._strategies.items():
            strategies.append({
                'name': name,
                'description': info['description'],
                'validated': info['validated']
            })
        return strategies
    
    def _register_builtin_strategies(self):
        """Register built-in strategies."""
        try:
            # Register SimplePriceActionStrategy from reference project
            reference_adapter = BacktestingLibraryAdapter(
                strategy_module="Reference/V7_IntradayCleanup_Best_30Min/SimplePriceActionStrategy.py",
                strategy_class="SimplePriceActionStrategy",
                config_path="Reference/V7_IntradayCleanup_Best_30Min/SimplePriceActionStrategyConfig.yaml"
            )
            
            self.register_strategy(
                name="SimplePriceActionStrategy",
                adapter=reference_adapter,
                description="High-performance price action strategy from reference project"
            )
            
            logger.info("✅ Registered built-in strategies")
            
        except Exception as e:
            logger.error(f"❌ Failed to register built-in strategies: {e}")


# Global strategy registry instance
strategy_registry = StrategyRegistry()


def get_strategy_adapter(strategy_name: str) -> Optional[StrategyAdapter]:
    """
    Convenience function to get a strategy adapter.
    
    Args:
        strategy_name: Name of the strategy
    
    Returns:
        Strategy adapter or None if not found
    """
    return strategy_registry.get_strategy(strategy_name)


def register_custom_strategy(
    name: str,
    module_path: str,
    class_name: str,
    config_path: Optional[str] = None,
    description: str = ""
) -> bool:
    """
    Convenience function to register a custom strategy.
    
    Args:
        name: Strategy name
        module_path: Path to strategy module
        class_name: Strategy class name
        config_path: Optional config file path
        description: Strategy description
    
    Returns:
        True if registration successful
    """
    try:
        adapter = BacktestingLibraryAdapter(module_path, class_name, config_path)
        strategy_registry.register_strategy(name, adapter, description)
        return True
    except Exception as e:
        logger.error(f"❌ Failed to register custom strategy: {e}")
        return False
