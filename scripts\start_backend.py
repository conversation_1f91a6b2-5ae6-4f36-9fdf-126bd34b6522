#!/usr/bin/env python3
"""
Backend startup script for Signal Stack trading platform.
"""

import sys
import os
import subprocess
import time
from pathlib import Path
import argparse

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger

logger = get_logger(__name__)


def check_dependencies():
    """Check if all required dependencies are installed."""
    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        import psycopg2
        logger.info("✓ All required dependencies are installed")
        return True
    except ImportError as e:
        logger.error(f"✗ Missing dependency: {e}")
        return False


def check_database_connection():
    """Check database connection."""
    try:
        from app.database.connection import get_db
        db = next(get_db())
        db.execute("SELECT 1")
        db.close()
        logger.info("✓ Database connection successful")
        return True
    except Exception as e:
        logger.error(f"✗ Database connection failed: {e}")
        return False


def check_configuration():
    """Check configuration files."""
    config_file = project_root / "config.yaml"
    if not config_file.exists():
        logger.error("✗ config.yaml not found")
        return False
    
    logger.info("✓ Configuration file found")
    return True


def start_server(host="0.0.0.0", port=8000, reload=True, workers=1):
    """Start the FastAPI server."""
    try:
        logger.info(f"🚀 Starting Signal Stack Backend Server")
        logger.info(f"   Host: {host}")
        logger.info(f"   Port: {port}")
        logger.info(f"   Reload: {reload}")
        logger.info(f"   Workers: {workers}")
        
        # Change to project root directory
        os.chdir(project_root)
        
        # Build uvicorn command
        cmd = [
            "uvicorn",
            "app.main:app",
            "--host", host,
            "--port", str(port)
        ]
        
        if reload:
            cmd.append("--reload")
        
        if workers > 1:
            cmd.extend(["--workers", str(workers)])
        
        # Start server
        logger.info(f"Executing: {' '.join(cmd)}")
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Signal Stack Backend Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--no-reload", action="store_true", help="Disable auto-reload")
    parser.add_argument("--workers", type=int, default=1, help="Number of worker processes")
    parser.add_argument("--skip-checks", action="store_true", help="Skip pre-flight checks")
    
    args = parser.parse_args()
    
    if not args.skip_checks:
        logger.info("🔍 Running pre-flight checks...")
        
        # Check dependencies
        if not check_dependencies():
            logger.error("Please install required dependencies: pip install -r requirements.txt")
            sys.exit(1)
        
        # Check configuration
        if not check_configuration():
            logger.error("Please ensure config.yaml exists and is properly configured")
            sys.exit(1)
        
        # Check database
        if not check_database_connection():
            logger.error("Please ensure PostgreSQL is running and database is configured")
            sys.exit(1)
        
        logger.info("✅ All pre-flight checks passed")
    
    # Start server
    start_server(
        host=args.host,
        port=args.port,
        reload=not args.no_reload,
        workers=args.workers
    )


if __name__ == "__main__":
    main()
