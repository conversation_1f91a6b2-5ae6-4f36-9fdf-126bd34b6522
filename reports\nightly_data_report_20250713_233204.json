{"timestamp": "2025-07-13T23:31:54.271598", "database_status": {"error": "Textual SQL expression 'SELECT COUNT(*) FROM stoc...' should be explicitly declared as text('SELECT COUNT(*) FROM stoc...')"}, "symbol_coverage": {"nifty50_total": 165, "nifty50_with_data": 1, "nifty50_without_data": 164, "coverage_percentage": 0.****************, "symbols_with_data": [{"symbol": "NIFTY", "records": 92717, "latest_date": "2025-07-11 15:30:00"}], "symbols_without_data": ["BHARTIARTL", "DIVISLAB", "MARUTI", "UPL", "LT", "ULTRACEMCO", "TCS", "NTPC", "JSWSTEEL", "GRASIM", "HDFCBANK", "HEROMOTOCO", "TECHM", "HINDALCO", "HINDUNILVR", "POWERGRID", "ADANIPORTS", "APOLLOHOSP", "INFY", "ITC", "BAJAJ-AUTO", "BAJAJFINSV", "LTIM", "NESTLEIND", "KOTAKBANK", "M&M", "COALINDIA", "SBILIFE", "ASIANPAINT", "ONGC", "ADANIENT", "NIFTYIT", "BANKNIFTY", "NIFTYNXT50", "NIFTYMIDCAP50", "NIFTYINFRA", "NIFTYPSE", "FINNIFTY", "NIFTYCPSE", "MIDCPNIFTY", "NIFTYTATA25CAP", "NIFTYMIDSMLHLTH", "NIFTYMULTIMFG", "NIFTYMULTIINFRA", "NIFTYINDDEFENCE", "NIFTYINDTOURISM", "NIFTYCAPITALMKT", "NIFTY500MOMENTM50", "NIFTYMS400MQ100", "NIFTYSML250MQ100", "NIFTYTOP10EW", "NIFTYAQL30", "NIFTYAQLV30", "NIFTYEV", "NIFTYHIGHBETA50", "NIFTYNEWCONSUMP", "NIFTYCORPMAATR", "NIFTYLOWVOL50", "NIFTYMOBILITY", "NIFTYQLTYLV30", "NIFTYSML250Q50", "NIFTYTOP15EW", "NIFTY100ALPHA30", "NIFTY100ENHESG", "NIFTY200VALUE30", "NIFTY500EW", "NIFTYMULTIMQ50", "NIFTY500VALUE50", "NIFTYTOP20EW", "NIFTYCOREHOUSING", "NIFTYFINSEREXBNK", "NIFTYHOUSING", "NIFTYIPO", "NIFTYMSFINSERV", "NIFTYMSINDCONS", "NIFTYMSITTELCM", "NIFTYNONCYCCONS", "NIFTYRURAL", "NIFTYSHARIAH25", "NIFTYTRANSLOGIS", "NIFTY50SHARIAH", "NIFTY500LMSEQL", "NIFTY500SHARIAH", "NIFTY500QLTY50", "NIFTY500LOWVOL50", "RELIANCE", "SBIN", "BAJFINANCE", "SUNPHARMA", "TATACONSUM", "TATAMOTORS", "TATASTEEL", "TITAN", "WIPRO", "HDFCLIFE", "ICICIBANK", "INDUSINDBK", "BPCL", "BRITANNIA", "AXISBANK", "CIPLA", "HCLTECH", "DRREDDY", "EICHERMOT", "NIFTY100", "NIFTY200", "NIFTY500", "NIFTYAUTO", "NIFTYCOMMODITIES", "NIFTYCONSUMPTION", "NIFTYDIVOPPS50", "NIFTYENERGY", "NIFTYFMCG", "NIFTYGROWSECT15", "NIFTYGS10YR", "NIFTYGS10YRCLN", "NIFTYGS1115YR", "NIFTYGS15YRPLUS", "NIFTYGS48YR", "NIFTYGS813YR", "NIFTYGSCOMPSITE", "NIFTYMEDIA", "NIFTYMETAL", "NIFTYMIDLIQ15", "NIFTYMIDCAP100", "NIFTYMNC", "NIFTYPHARMA", "NIFTYPSUBANK", "NIFTYPVTBANK", "NIFTYQUALITY30", "NIFTYREALTY", "NIFTYSERVSECTOR", "NIFTYSMLCAP100", "NIFTY100LIQ15", "NIFTY50DIVPOINT", "NIFTY50PR1XINV", "NIFTY50PR2XLEV", "NIFTY50TR1XINV", "NIFTY50TR2XLEV", "NIFTY50VALUE20", "NIFTYALPHA50", "NIFTY100 EQL WGT", "NIFTY100 LOWVOL30", "NIFTY50 EQL WGT", "NIFTY200QUALTY30", "NIFTYMIDCAP150", "NIFTYSMLCAP50", "NIFTYSMLCAP250", "NIFTYMIDSML400", "NIFTYFINSRV2550", "NIFTYALPHALOWVOL", "NIFTYINDIAMFG", "NIFTYM150QLTY50", "NIFTY100ESG", "NIFTY100ESGSECLDR", "NIFTY200MOMENTM30", "NIFTYHEALTHCARE", "NIFTYCONSRDURBL", "NIFTYOILANDGAS", "NIFTY500MULTICAP", "NIFTYLARGEMID250", "NIFTYTOTALMKT", "NIFTYMICROCAP250", "NIFTYINDDIGITAL"]}, "data_quality": {"error": "Textual SQL expression '\\n                    SELE...' should be explicitly declared as text('\\n                    SELE...')"}, "recommendations": ["Low NIFTY 50 coverage - consider running bulk data load"]}