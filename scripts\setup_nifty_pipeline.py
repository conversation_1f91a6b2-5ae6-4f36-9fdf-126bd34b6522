#!/usr/bin/env python3
"""
Complete setup and testing script for NIFTY real-time data pipeline.
This script demonstrates the full workflow from database setup to real-time data processing.
"""

import sys
import os
import time
import signal
from pathlib import Path
from datetime import datetime, timed<PERSON><PERSON>

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import setup_logging
from app.database.connection import get_db, check_database_connection
from app.database.init_db import setup_database
from app.services.market_data_service import MarketDataService
from app.services.aggregation_service import AggregationService
from app.services.realtime_pipeline import RealTimeDataPipeline
from app.database.models import MarketType

logger = setup_logging(level="INFO")

# Global pipeline instance for cleanup
pipeline = None


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    logger.info("Received shutdown signal, stopping pipeline...")
    if pipeline:
        pipeline.stop()
    sys.exit(0)


def setup_nifty_symbol():
    """Set up NIFTY symbol in database."""
    logger.info("Setting up NIFTY symbol...")
    
    try:
        db = next(get_db())
        market_service = MarketDataService(db)
        
        # Check if NIFTY symbol exists
        nifty_symbol = market_service.data_service.get_symbol_by_name("NIFTY")
        
        if not nifty_symbol:
            logger.info("Creating NIFTY symbol in database...")
            
            symbol_data = {
                'symbol': 'NIFTY',
                'name': 'Nifty 50 Index',
                'market_type': MarketType.INDEX,
                'exchange': 'NSE',
                'token': 'NSE:NIFTY50-INDEX',
                'lot_size': 50,
                'tick_size': 0.05,
                'is_active': True
            }
            
            nifty_symbol = market_service.data_service.create_symbol(symbol_data)
            
            if nifty_symbol:
                logger.info("✓ NIFTY symbol created successfully")
            else:
                logger.error("✗ Failed to create NIFTY symbol")
                return False
        else:
            logger.info("✓ NIFTY symbol already exists")
        
        return True
        
    except Exception as e:
        logger.error(f"Error setting up NIFTY symbol: {e}")
        return False
    finally:
        db.close()


def fetch_historical_data():
    """Fetch historical data for NIFTY."""
    logger.info("Fetching historical data for NIFTY...")
    
    try:
        db = next(get_db())
        market_service = MarketDataService(db)
        
        # Initialize Fyers connection
        if not market_service.initialize_fyers_connection():
            logger.error("Failed to initialize Fyers connection")
            return False
        
        # Fetch last 7 days of 1-minute data
        success = market_service.fetch_and_store_historical_data(
            symbol="NSE:NIFTY50-INDEX",
            timeframe="1",
            days=7
        )
        
        if success:
            logger.info("✓ Historical data fetched successfully")
            
            # Get statistics
            stats = market_service.data_service.get_data_statistics("NIFTY")
            if stats:
                logger.info(f"  Total records: {stats['total_records']}")
                logger.info(f"  Data range: {stats['data_range']['start']} to {stats['data_range']['end']}")
                logger.info(f"  Latest: {stats['latest_timestamp']}")
            
            return True
        else:
            logger.error("✗ Failed to fetch historical data")
            return False
            
    except Exception as e:
        logger.error(f"Error fetching historical data: {e}")
        return False
    finally:
        db.close()


def test_aggregation():
    """Test timeframe aggregation for NIFTY."""
    logger.info("Testing timeframe aggregation...")
    
    try:
        db = next(get_db())
        aggregation_service = AggregationService(db)
        
        # Test aggregation for different timeframes
        timeframes = ['5m', '15m', '30m', '1h']
        
        for timeframe in timeframes:
            logger.info(f"Aggregating NIFTY data for {timeframe}...")
            
            success = aggregation_service.aggregate_symbol_data(
                symbol="NIFTY",
                timeframe=timeframe,
                start_time=datetime.now() - timedelta(days=7),
                end_time=datetime.now()
            )
            
            if success:
                logger.info(f"✓ {timeframe} aggregation successful")
            else:
                logger.warning(f"⚠ {timeframe} aggregation failed")
        
        # Get aggregation statistics
        stats = aggregation_service.get_aggregation_statistics()
        logger.info("Aggregation statistics:")
        for key, value in stats.items():
            if key.endswith('_records'):
                logger.info(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing aggregation: {e}")
        return False
    finally:
        db.close()


def start_realtime_pipeline():
    """Start the real-time data pipeline for NIFTY."""
    global pipeline
    
    logger.info("Starting real-time data pipeline...")
    
    try:
        # Initialize pipeline
        pipeline = RealTimeDataPipeline()
        
        if not pipeline.initialize():
            logger.error("Failed to initialize pipeline")
            return False
        
        # Add data callback for monitoring
        def data_callback(symbol, quote_data):
            logger.info(f"Real-time: {symbol} = {quote_data.ltp} "
                       f"(Vol: {quote_data.volume}, Time: {quote_data.timestamp})")
        
        pipeline.add_data_callback(data_callback)
        
        # Start pipeline with NIFTY
        symbols = ["NSE:NIFTY50-INDEX"]
        
        if not pipeline.start(symbols):
            logger.error("Failed to start pipeline")
            return False
        
        logger.info("✓ Real-time pipeline started successfully")
        logger.info("Pipeline will run for 5 minutes to demonstrate real-time data flow...")
        
        # Monitor pipeline for 5 minutes
        start_time = time.time()
        duration = 300  # 5 minutes
        
        while time.time() - start_time < duration:
            # Print statistics every 30 seconds
            if int(time.time() - start_time) % 30 == 0:
                stats = pipeline.get_statistics()
                logger.info(f"Pipeline stats: "
                           f"Ticks received: {stats['ticks_received']}, "
                           f"Ticks processed: {stats['ticks_processed']}, "
                           f"Errors: {stats['errors']}")
            
            time.sleep(1)
        
        logger.info("Real-time demonstration completed")
        return True
        
    except KeyboardInterrupt:
        logger.info("Pipeline interrupted by user")
        return True
    except Exception as e:
        logger.error(f"Error in real-time pipeline: {e}")
        return False


def verify_data_flow():
    """Verify that data is flowing correctly through the system."""
    logger.info("Verifying data flow...")
    
    try:
        db = next(get_db())
        market_service = MarketDataService(db)
        aggregation_service = AggregationService(db)
        
        # Check latest 1-minute data
        latest_data = market_service.data_service.get_latest_ohlcv("NIFTY", count=5)
        
        if latest_data:
            logger.info("✓ Latest 1-minute data:")
            for data in latest_data:
                logger.info(f"  {data.timestamp}: O={data.open}, H={data.high}, "
                           f"L={data.low}, C={data.close}, V={data.volume}")
        else:
            logger.warning("⚠ No recent 1-minute data found")
        
        # Check aggregated data
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=2)
        
        for timeframe in ['5m', '15m', '30m']:
            agg_df = aggregation_service.get_aggregated_data_as_dataframe(
                "NIFTY", timeframe, start_time, end_time
            )
            
            if not agg_df.empty:
                logger.info(f"✓ {timeframe} aggregated data: {len(agg_df)} records")
                logger.info(f"  Latest: {agg_df.index[-1]} = {agg_df.iloc[-1]['close']}")
            else:
                logger.warning(f"⚠ No {timeframe} aggregated data found")
        
        return True
        
    except Exception as e:
        logger.error(f"Error verifying data flow: {e}")
        return False
    finally:
        db.close()


def main():
    """Main function to run the complete NIFTY pipeline setup and test."""
    global pipeline
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    logger.info("=" * 80)
    logger.info("NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST")
    logger.info("=" * 80)
    
    try:
        # Step 1: Check database connection
        logger.info("Step 1: Checking database connection...")
        if not check_database_connection():
            logger.error("Database connection failed. Please check your configuration.")
            return False
        logger.info("✓ Database connection successful")
        
        # Step 2: Setup database
        logger.info("\nStep 2: Setting up database...")
        try:
            setup_database()
            logger.info("✓ Database setup completed")
        except Exception as e:
            logger.error(f"Database setup failed: {e}")
            return False
        
        # Step 3: Setup NIFTY symbol
        logger.info("\nStep 3: Setting up NIFTY symbol...")
        if not setup_nifty_symbol():
            return False
        
        # Step 4: Fetch historical data
        logger.info("\nStep 4: Fetching historical data...")
        if not fetch_historical_data():
            return False
        
        # Step 5: Test aggregation
        logger.info("\nStep 5: Testing timeframe aggregation...")
        if not test_aggregation():
            return False
        
        # Step 6: Start real-time pipeline
        logger.info("\nStep 6: Starting real-time data pipeline...")
        if not start_realtime_pipeline():
            return False
        
        # Step 7: Verify data flow
        logger.info("\nStep 7: Verifying data flow...")
        if not verify_data_flow():
            return False
        
        logger.info("\n" + "=" * 80)
        logger.info("NIFTY PIPELINE SETUP AND TEST COMPLETED SUCCESSFULLY!")
        logger.info("=" * 80)
        logger.info("The system is now ready for:")
        logger.info("1. Real-time NIFTY data ingestion")
        logger.info("2. Automatic timeframe aggregation")
        logger.info("3. Historical data storage and retrieval")
        logger.info("4. Strategy development and backtesting")
        
        return True
        
    except Exception as e:
        logger.error(f"Pipeline setup failed: {e}")
        return False
    
    finally:
        # Cleanup
        if pipeline:
            pipeline.stop()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
