"""
Main backtesting engine for strategy testing.
"""

from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import pandas as pd
import uuid

from .strategy import BaseStrategy, TradingSignal, SignalType
from .portfolio import <PERSON><PERSON>lio
from .orders import Order, OrderType, OrderSide, OrderStatus
from .metrics import PerformanceMetrics
from app.core.logging import get_logger

logger = get_logger(__name__)


class BacktestEngine:
    """Main backtesting engine for strategy testing."""
    
    def __init__(
        self,
        initial_cash: float = 100000.0,
        commission_rate: float = 0.001,
        slippage: float = 0.0001,
        position_size_method: str = "fixed_amount",
        position_size_value: float = 10000.0
    ):
        """
        Initialize backtesting engine.
        
        Args:
            initial_cash: Initial portfolio cash
            commission_rate: Commission rate (as decimal)
            slippage: Slippage rate (as decimal)
            position_size_method: Position sizing method ('fixed_amount', 'fixed_shares', 'percentage')
            position_size_value: Position size value
        """
        self.initial_cash = initial_cash
        self.commission_rate = commission_rate
        self.slippage = slippage
        self.position_size_method = position_size_method
        self.position_size_value = position_size_value
        
        # Initialize components
        self.portfolio = Portfolio(initial_cash, commission_rate)
        self.strategy: Optional[BaseStrategy] = None
        self.data: Optional[pd.DataFrame] = None
        
        # Backtest state
        self.is_running = False
        self.current_index = 0
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        
        # Results
        self.backtest_results: Dict[str, Any] = {}
        self.performance_metrics: Optional[PerformanceMetrics] = None
    
    def set_strategy(self, strategy: BaseStrategy) -> None:
        """Set the trading strategy."""
        self.strategy = strategy
        logger.info(f"Strategy set: {strategy.name}")
    
    def set_data(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> None:
        """Set the market data for backtesting."""
        if isinstance(data, list):
            self.data = pd.DataFrame(data)
        else:
            self.data = data.copy()
        
        # Ensure required columns exist
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in self.data.columns:
                raise ValueError(f"Missing required column: {col}")
        
        # Sort by timestamp
        self.data = self.data.sort_values('timestamp').reset_index(drop=True)
        
        # Convert timestamp to datetime if needed
        if not pd.api.types.is_datetime64_any_dtype(self.data['timestamp']):
            self.data['timestamp'] = pd.to_datetime(self.data['timestamp'])
        
        logger.info(f"Data set: {len(self.data)} bars from {self.data['timestamp'].iloc[0]} to {self.data['timestamp'].iloc[-1]}")
    
    def run_backtest(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        symbol: str = "SYMBOL"
    ) -> Dict[str, Any]:
        """
        Run the backtest.
        
        Args:
            start_date: Backtest start date
            end_date: Backtest end date
            symbol: Symbol being tested
            
        Returns:
            Backtest results
        """
        try:
            if not self.strategy:
                raise ValueError("Strategy not set")
            
            if self.data is None or len(self.data) == 0:
                raise ValueError("Data not set or empty")
            
            logger.info(f"Starting backtest for strategy: {self.strategy.name}")
            self.start_time = datetime.utcnow()
            self.is_running = True
            
            # Filter data by date range if specified
            if start_date or end_date:
                self.data = self._filter_data_by_date(start_date, end_date)
            
            if len(self.data) == 0:
                raise ValueError("No data available for specified date range")
            
            # Initialize strategy
            self.strategy.initialize(self.data)
            
            # Run backtest bar by bar
            for i in range(len(self.data)):
                self.current_index = i
                current_bar = self.data.iloc[i]
                
                # Update portfolio with current prices
                current_price = float(current_bar['close'])
                self.portfolio.update_price(symbol, current_price)
                
                # Process pending orders
                current_prices = {symbol: current_price}
                filled_orders = self.portfolio.process_pending_orders(
                    current_prices, current_bar['timestamp']
                )
                
                # Generate signals from strategy
                signals = self.strategy.on_bar(self.data, i)
                
                # Process signals
                for signal in signals:
                    self._process_signal(signal, current_bar, symbol)
                
                # Take portfolio snapshot
                self.portfolio.take_snapshot(current_bar['timestamp'])
                
                # Log progress periodically
                if i % 100 == 0:
                    logger.debug(f"Processed {i}/{len(self.data)} bars")
            
            self.end_time = datetime.utcnow()
            self.is_running = False
            
            # Calculate performance metrics
            self.performance_metrics = PerformanceMetrics(self.portfolio)
            metrics = self.performance_metrics.calculate_all_metrics()
            
            # Compile results
            self.backtest_results = {
                'strategy_name': self.strategy.name,
                'strategy_parameters': self.strategy.parameters,
                'symbol': symbol,
                'backtest_config': {
                    'initial_cash': self.initial_cash,
                    'commission_rate': self.commission_rate,
                    'slippage': self.slippage,
                    'position_size_method': self.position_size_method,
                    'position_size_value': self.position_size_value
                },
                'data_info': {
                    'total_bars': len(self.data),
                    'start_date': self.data['timestamp'].iloc[0].isoformat(),
                    'end_date': self.data['timestamp'].iloc[-1].isoformat()
                },
                'portfolio_summary': self.portfolio.get_performance_summary(),
                'performance_metrics': metrics,
                'trades': [trade.__dict__ for trade in self.portfolio.trades],
                'execution_time': (self.end_time - self.start_time).total_seconds(),
                'backtest_id': str(uuid.uuid4())
            }
            
            logger.info(f"Backtest completed successfully. Total return: {metrics.get('total_return', 0):.2f}%")
            return self.backtest_results
            
        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            self.is_running = False
            raise
    
    def _filter_data_by_date(self, start_date: Optional[datetime], end_date: Optional[datetime]) -> pd.DataFrame:
        """Filter data by date range."""
        filtered_data = self.data.copy()
        
        if start_date:
            filtered_data = filtered_data[filtered_data['timestamp'] >= start_date]
        
        if end_date:
            filtered_data = filtered_data[filtered_data['timestamp'] <= end_date]
        
        return filtered_data.reset_index(drop=True)
    
    def _process_signal(self, signal: TradingSignal, current_bar: pd.Series, symbol: str) -> None:
        """Process a trading signal."""
        try:
            if signal.signal_type == SignalType.BUY:
                self._process_buy_signal(signal, current_bar, symbol)
            elif signal.signal_type == SignalType.SELL:
                self._process_sell_signal(signal, current_bar, symbol)
            elif signal.signal_type == SignalType.EXIT:
                self._process_exit_signal(signal, current_bar, symbol)
            
        except Exception as e:
            logger.error(f"Error processing signal {signal.signal_type}: {e}")
    
    def _process_buy_signal(self, signal: TradingSignal, current_bar: pd.Series, symbol: str) -> None:
        """Process a buy signal."""
        # Calculate position size
        quantity = self._calculate_position_size(signal.price, symbol)
        
        if quantity <= 0:
            return
        
        # Apply slippage
        execution_price = signal.price * (1 + self.slippage)
        
        # Create buy order
        order = Order(
            id=str(uuid.uuid4()),
            symbol=symbol,
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=quantity,
            price=execution_price,
            timestamp=signal.timestamp,
            metadata={'signal_confidence': signal.confidence}
        )
        
        # Place order
        self.portfolio.place_order(order)
    
    def _process_sell_signal(self, signal: TradingSignal, current_bar: pd.Series, symbol: str) -> None:
        """Process a sell signal."""
        # Check if we have positions to sell
        if symbol not in self.portfolio.positions:
            return
        
        position = self.portfolio.positions[symbol]
        quantity = signal.quantity or position.quantity
        
        # Apply slippage
        execution_price = signal.price * (1 - self.slippage)
        
        # Create sell order
        order = Order(
            id=str(uuid.uuid4()),
            symbol=symbol,
            side=OrderSide.SELL,
            order_type=OrderType.MARKET,
            quantity=quantity,
            price=execution_price,
            timestamp=signal.timestamp,
            metadata={'signal_confidence': signal.confidence}
        )
        
        # Place order
        self.portfolio.place_order(order)
    
    def _process_exit_signal(self, signal: TradingSignal, current_bar: pd.Series, symbol: str) -> None:
        """Process an exit signal (close all positions)."""
        if symbol in self.portfolio.positions:
            position = self.portfolio.positions[symbol]
            
            # Create exit order
            exit_signal = TradingSignal(
                symbol=symbol,
                signal_type=SignalType.SELL,
                timestamp=signal.timestamp,
                price=signal.price,
                quantity=position.quantity,
                confidence=signal.confidence
            )
            
            self._process_sell_signal(exit_signal, current_bar, symbol)
    
    def _calculate_position_size(self, price: float, symbol: str) -> int:
        """Calculate position size based on configured method."""
        try:
            if self.position_size_method == "fixed_amount":
                # Fixed dollar amount
                return int(self.position_size_value / price)
            
            elif self.position_size_method == "fixed_shares":
                # Fixed number of shares
                return int(self.position_size_value)
            
            elif self.position_size_method == "percentage":
                # Percentage of portfolio value
                portfolio_value = self.portfolio.total_value
                amount = portfolio_value * (self.position_size_value / 100)
                return int(amount / price)
            
            else:
                logger.warning(f"Unknown position size method: {self.position_size_method}")
                return 0
                
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0
    
    def get_equity_curve(self) -> pd.DataFrame:
        """Get equity curve data."""
        if not self.portfolio.snapshots:
            return pd.DataFrame()
        
        data = []
        for snapshot in self.portfolio.snapshots:
            data.append({
                'timestamp': snapshot.timestamp,
                'total_value': snapshot.total_value,
                'cash': snapshot.cash,
                'positions_value': snapshot.positions_value,
                'unrealized_pnl': snapshot.unrealized_pnl,
                'realized_pnl': snapshot.realized_pnl,
                'total_pnl': snapshot.total_pnl
            })
        
        return pd.DataFrame(data)
    
    def get_trade_analysis(self) -> Dict[str, Any]:
        """Get detailed trade analysis."""
        if not self.portfolio.trades:
            return {'message': 'No trades executed'}
        
        trades_df = pd.DataFrame([trade.__dict__ for trade in self.portfolio.trades])
        
        return {
            'total_trades': len(self.portfolio.trades),
            'profitable_trades': len([t for t in self.portfolio.trades if t.is_profitable]),
            'losing_trades': len([t for t in self.portfolio.trades if not t.is_profitable]),
            'avg_trade_duration': trades_df['duration'].mean(),
            'avg_profit_per_trade': trades_df['pnl'].mean(),
            'largest_win': trades_df['pnl'].max(),
            'largest_loss': trades_df['pnl'].min(),
            'win_rate': (len([t for t in self.portfolio.trades if t.is_profitable]) / len(self.portfolio.trades)) * 100
        }

    def reset(self) -> None:
        """Reset the backtesting engine for a new run."""
        self.portfolio = Portfolio(self.initial_cash, self.commission_rate)
        self.current_index = 0
        self.is_running = False
        self.start_time = None
        self.end_time = None
        self.backtest_results = {}
        self.performance_metrics = None

        if self.strategy:
            self.strategy.positions = []
            self.strategy.trades = []
            self.strategy.signals = []
            self.strategy.is_initialized = False
