#!/usr/bin/env python3
"""
Test runner script for Signal Stack trading platform.
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path
import time
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def run_command(command, description=""):
    """Run a command and return the result."""
    print(f"\n{'='*60}")
    print(f"Running: {description or command}")
    print(f"{'='*60}")
    
    start_time = time.time()
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    end_time = time.time()
    
    print(f"Exit code: {result.returncode}")
    print(f"Duration: {end_time - start_time:.2f} seconds")
    
    if result.stdout:
        print(f"\nSTDOUT:\n{result.stdout}")
    
    if result.stderr:
        print(f"\nSTDERR:\n{result.stderr}")
    
    return result


def install_dependencies():
    """Install test dependencies."""
    print("Installing test dependencies...")
    
    # Install main dependencies
    result = run_command("pip install -r requirements.txt", "Installing main dependencies")
    if result.returncode != 0:
        print("Failed to install main dependencies")
        return False
    
    # Install test dependencies
    test_deps = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.0.0",
        "pytest-xdist>=3.0.0",
        "pytest-html>=3.1.0",
        "pytest-mock>=3.10.0",
        "httpx>=0.24.0",
        "factory-boy>=3.2.0"
    ]
    
    for dep in test_deps:
        result = run_command(f"pip install {dep}", f"Installing {dep}")
        if result.returncode != 0:
            print(f"Failed to install {dep}")
            return False
    
    return True


def run_unit_tests(coverage=False, verbose=False):
    """Run unit tests."""
    cmd = "pytest tests/unit/"
    
    if coverage:
        cmd += " --cov=app --cov-report=html --cov-report=term"
    
    if verbose:
        cmd += " -v"
    
    cmd += " --tb=short"
    
    result = run_command(cmd, "Running unit tests")
    return result.returncode == 0


def run_integration_tests(verbose=False):
    """Run integration tests."""
    cmd = "pytest tests/integration/"
    
    if verbose:
        cmd += " -v"
    
    cmd += " --tb=short"
    
    result = run_command(cmd, "Running integration tests")
    return result.returncode == 0


def run_e2e_tests(verbose=False):
    """Run end-to-end tests."""
    cmd = "pytest tests/e2e/"
    
    if verbose:
        cmd += " -v"
    
    cmd += " --tb=short"
    
    result = run_command(cmd, "Running end-to-end tests")
    return result.returncode == 0


def run_performance_tests(verbose=False):
    """Run performance tests."""
    cmd = "pytest tests/ -m performance"
    
    if verbose:
        cmd += " -v"
    
    cmd += " --tb=short"
    
    result = run_command(cmd, "Running performance tests")
    return result.returncode == 0


def run_all_tests(coverage=False, verbose=False, parallel=False):
    """Run all tests."""
    cmd = "pytest tests/"
    
    if coverage:
        cmd += " --cov=app --cov-report=html --cov-report=term"
    
    if verbose:
        cmd += " -v"
    
    if parallel:
        cmd += " -n auto"  # Requires pytest-xdist
    
    cmd += " --tb=short --html=test_report.html --self-contained-html"
    
    result = run_command(cmd, "Running all tests")
    return result.returncode == 0


def run_specific_test(test_path, verbose=False):
    """Run a specific test file or test function."""
    cmd = f"pytest {test_path}"
    
    if verbose:
        cmd += " -v"
    
    cmd += " --tb=short"
    
    result = run_command(cmd, f"Running specific test: {test_path}")
    return result.returncode == 0


def lint_code():
    """Run code linting."""
    print("Running code linting...")
    
    # Check if flake8 is installed
    result = run_command("pip install flake8", "Installing flake8")
    if result.returncode != 0:
        print("Failed to install flake8")
        return False
    
    # Run flake8
    result = run_command("flake8 app/ tests/ --max-line-length=120 --ignore=E501,W503", "Running flake8")
    return result.returncode == 0


def type_check():
    """Run type checking."""
    print("Running type checking...")
    
    # Check if mypy is installed
    result = run_command("pip install mypy", "Installing mypy")
    if result.returncode != 0:
        print("Failed to install mypy")
        return False
    
    # Run mypy
    result = run_command("mypy app/ --ignore-missing-imports", "Running mypy")
    return result.returncode == 0


def security_check():
    """Run security checks."""
    print("Running security checks...")
    
    # Check if bandit is installed
    result = run_command("pip install bandit", "Installing bandit")
    if result.returncode != 0:
        print("Failed to install bandit")
        return False
    
    # Run bandit
    result = run_command("bandit -r app/ -f json -o security_report.json", "Running bandit")
    return result.returncode == 0


def generate_test_report():
    """Generate comprehensive test report."""
    print("Generating comprehensive test report...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_dir = f"test_reports_{timestamp}"
    os.makedirs(report_dir, exist_ok=True)
    
    # Run tests with coverage and HTML report
    cmd = f"""
    pytest tests/ \
        --cov=app \
        --cov-report=html:{report_dir}/coverage_html \
        --cov-report=xml:{report_dir}/coverage.xml \
        --cov-report=term \
        --html={report_dir}/test_report.html \
        --self-contained-html \
        --junitxml={report_dir}/junit.xml \
        -v
    """
    
    result = run_command(cmd, "Generating comprehensive test report")
    
    if result.returncode == 0:
        print(f"\nTest report generated in: {report_dir}/")
        print(f"- HTML Report: {report_dir}/test_report.html")
        print(f"- Coverage Report: {report_dir}/coverage_html/index.html")
        print(f"- JUnit XML: {report_dir}/junit.xml")
        print(f"- Coverage XML: {report_dir}/coverage.xml")
    
    return result.returncode == 0


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Signal Stack Test Runner")
    parser.add_argument("--install-deps", action="store_true", help="Install test dependencies")
    parser.add_argument("--unit", action="store_true", help="Run unit tests")
    parser.add_argument("--integration", action="store_true", help="Run integration tests")
    parser.add_argument("--e2e", action="store_true", help="Run end-to-end tests")
    parser.add_argument("--performance", action="store_true", help="Run performance tests")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    parser.add_argument("--specific", type=str, help="Run specific test file or function")
    parser.add_argument("--coverage", action="store_true", help="Generate coverage report")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--parallel", "-p", action="store_true", help="Run tests in parallel")
    parser.add_argument("--lint", action="store_true", help="Run code linting")
    parser.add_argument("--type-check", action="store_true", help="Run type checking")
    parser.add_argument("--security", action="store_true", help="Run security checks")
    parser.add_argument("--report", action="store_true", help="Generate comprehensive test report")
    parser.add_argument("--quick", action="store_true", help="Quick test run (unit + integration)")
    
    args = parser.parse_args()
    
    if not any(vars(args).values()):
        parser.print_help()
        return
    
    success = True
    
    # Install dependencies if requested
    if args.install_deps:
        if not install_dependencies():
            print("Failed to install dependencies")
            sys.exit(1)
    
    # Run specific test
    if args.specific:
        success = run_specific_test(args.specific, args.verbose)
    
    # Run quick tests
    elif args.quick:
        print("Running quick test suite (unit + integration)...")
        success = (
            run_unit_tests(args.coverage, args.verbose) and
            run_integration_tests(args.verbose)
        )
    
    # Run all tests
    elif args.all:
        success = run_all_tests(args.coverage, args.verbose, args.parallel)
    
    # Run individual test suites
    else:
        if args.unit:
            success = run_unit_tests(args.coverage, args.verbose) and success
        
        if args.integration:
            success = run_integration_tests(args.verbose) and success
        
        if args.e2e:
            success = run_e2e_tests(args.verbose) and success
        
        if args.performance:
            success = run_performance_tests(args.verbose) and success
    
    # Run code quality checks
    if args.lint:
        success = lint_code() and success
    
    if args.type_check:
        success = type_check() and success
    
    if args.security:
        success = security_check() and success
    
    # Generate comprehensive report
    if args.report:
        success = generate_test_report() and success
    
    # Print summary
    print(f"\n{'='*60}")
    print(f"TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Status: {'PASSED' if success else 'FAILED'}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if not success:
        print("\nSome tests failed. Please check the output above for details.")
        sys.exit(1)
    else:
        print("\nAll tests passed successfully!")


if __name__ == "__main__":
    main()
