"""
Portfolio management for backtesting engine.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass, field
import pandas as pd

from .strategy import Position, Trade, PositionType
from .orders import Order, OrderSide, OrderStatus
from app.core.logging import get_logger

logger = get_logger(__name__)


@dataclass
class PortfolioSnapshot:
    """Portfolio snapshot at a point in time."""
    
    timestamp: datetime
    cash: float
    total_value: float
    positions_value: float
    unrealized_pnl: float
    realized_pnl: float
    total_pnl: float
    positions_count: int
    metadata: Dict[str, Any] = field(default_factory=dict)


class Portfolio:
    """Portfolio management for backtesting."""
    
    def __init__(self, initial_cash: float = 100000.0, commission_rate: float = 0.001):
        """
        Initialize portfolio.
        
        Args:
            initial_cash: Initial cash amount
            commission_rate: Commission rate (as decimal, e.g., 0.001 = 0.1%)
        """
        self.initial_cash = initial_cash
        self.cash = initial_cash
        self.commission_rate = commission_rate
        
        # Portfolio components
        self.positions: Dict[str, Position] = {}
        self.orders: List[Order] = []
        self.trades: List[Trade] = []
        self.snapshots: List[PortfolioSnapshot] = []
        
        # Performance tracking
        self.total_commission_paid = 0.0
        self.peak_value = initial_cash
        self.max_drawdown = 0.0
        
        # Current prices for position valuation
        self.current_prices: Dict[str, float] = {}
    
    @property
    def total_value(self) -> float:
        """Get total portfolio value."""
        return self.cash + self.positions_value
    
    @property
    def positions_value(self) -> float:
        """Get total value of all positions."""
        total = 0.0
        for symbol, position in self.positions.items():
            if symbol in self.current_prices:
                position.update_price(self.current_prices[symbol])
                total += position.market_value
        return total
    
    @property
    def unrealized_pnl(self) -> float:
        """Get total unrealized P&L."""
        return sum(pos.unrealized_pnl for pos in self.positions.values())
    
    @property
    def realized_pnl(self) -> float:
        """Get total realized P&L."""
        return sum(trade.pnl for trade in self.trades)
    
    @property
    def total_pnl(self) -> float:
        """Get total P&L (realized + unrealized)."""
        return self.realized_pnl + self.unrealized_pnl
    
    @property
    def total_return(self) -> float:
        """Get total return percentage."""
        if self.initial_cash == 0:
            return 0.0
        return ((self.total_value - self.initial_cash) / self.initial_cash) * 100
    
    def update_prices(self, prices: Dict[str, float]) -> None:
        """Update current prices for portfolio valuation."""
        self.current_prices.update(prices)
        
        # Update position prices
        for symbol, position in self.positions.items():
            if symbol in prices:
                position.update_price(prices[symbol])
    
    def update_price(self, symbol: str, price: float) -> None:
        """Update price for a single symbol."""
        self.current_prices[symbol] = price
        if symbol in self.positions:
            self.positions[symbol].update_price(price)
    
    def can_buy(self, symbol: str, quantity: int, price: float) -> bool:
        """Check if portfolio can afford to buy."""
        cost = quantity * price
        commission = cost * self.commission_rate
        total_cost = cost + commission
        return self.cash >= total_cost
    
    def can_sell(self, symbol: str, quantity: int) -> bool:
        """Check if portfolio has enough shares to sell."""
        if symbol not in self.positions:
            return False
        return self.positions[symbol].quantity >= quantity
    
    def execute_buy_order(self, order: Order, price: float, timestamp: datetime) -> bool:
        """Execute a buy order."""
        if not order.is_buy or order.status != OrderStatus.PENDING:
            return False
        
        if not self.can_buy(order.symbol, order.quantity, price):
            order.reject("Insufficient funds")
            return False
        
        # Calculate costs
        cost = order.quantity * price
        commission = cost * self.commission_rate
        total_cost = cost + commission
        
        # Update cash
        self.cash -= total_cost
        self.total_commission_paid += commission
        
        # Create or update position
        if order.symbol in self.positions:
            # Add to existing position
            existing_pos = self.positions[order.symbol]
            total_quantity = existing_pos.quantity + order.quantity
            weighted_price = ((existing_pos.quantity * existing_pos.entry_price) + 
                            (order.quantity * price)) / total_quantity
            
            existing_pos.quantity = total_quantity
            existing_pos.entry_price = weighted_price
            existing_pos.commission += commission
        else:
            # Create new position
            position = Position(
                symbol=order.symbol,
                position_type=PositionType.LONG,
                quantity=order.quantity,
                entry_price=price,
                entry_timestamp=timestamp,
                current_price=price,
                commission=commission
            )
            self.positions[order.symbol] = position
        
        # Fill order
        order.fill(order.quantity, price, timestamp, commission)
        return True
    
    def execute_sell_order(self, order: Order, price: float, timestamp: datetime) -> bool:
        """Execute a sell order."""
        if not order.is_sell or order.status != OrderStatus.PENDING:
            return False
        
        if not self.can_sell(order.symbol, order.quantity):
            order.reject("Insufficient shares")
            return False
        
        position = self.positions[order.symbol]
        
        # Calculate proceeds
        proceeds = order.quantity * price
        commission = proceeds * self.commission_rate
        net_proceeds = proceeds - commission
        
        # Update cash
        self.cash += net_proceeds
        self.total_commission_paid += commission
        
        # Update or close position
        if position.quantity == order.quantity:
            # Close entire position
            position.close(price, timestamp, commission)
            trade = Trade.from_position(position)
            self.trades.append(trade)
            del self.positions[order.symbol]
        else:
            # Partial sale
            position.quantity -= order.quantity
            position.commission += commission
        
        # Fill order
        order.fill(order.quantity, price, timestamp, commission)
        return True
    
    def place_order(self, order: Order) -> bool:
        """Place an order in the portfolio."""
        self.orders.append(order)
        return True
    
    def process_pending_orders(self, current_prices: Dict[str, float], timestamp: datetime) -> List[Order]:
        """Process all pending orders."""
        filled_orders = []
        
        for order in self.orders:
            if order.status != OrderStatus.PENDING:
                continue
            
            if order.symbol not in current_prices:
                continue
            
            current_price = current_prices[order.symbol]
            
            # Simple market order execution
            if order.order_type.value == "market":
                if order.is_buy:
                    success = self.execute_buy_order(order, current_price, timestamp)
                else:
                    success = self.execute_sell_order(order, current_price, timestamp)
                
                if success:
                    filled_orders.append(order)
        
        return filled_orders
    
    def take_snapshot(self, timestamp: datetime) -> PortfolioSnapshot:
        """Take a portfolio snapshot."""
        snapshot = PortfolioSnapshot(
            timestamp=timestamp,
            cash=self.cash,
            total_value=self.total_value,
            positions_value=self.positions_value,
            unrealized_pnl=self.unrealized_pnl,
            realized_pnl=self.realized_pnl,
            total_pnl=self.total_pnl,
            positions_count=len(self.positions)
        )
        
        self.snapshots.append(snapshot)
        
        # Update drawdown tracking
        if self.total_value > self.peak_value:
            self.peak_value = self.total_value
        
        current_drawdown = ((self.peak_value - self.total_value) / self.peak_value) * 100
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
        
        return snapshot
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get portfolio performance summary."""
        return {
            'initial_cash': self.initial_cash,
            'final_value': self.total_value,
            'total_return': self.total_return,
            'total_pnl': self.total_pnl,
            'realized_pnl': self.realized_pnl,
            'unrealized_pnl': self.unrealized_pnl,
            'total_trades': len(self.trades),
            'total_commission': self.total_commission_paid,
            'max_drawdown': self.max_drawdown,
            'peak_value': self.peak_value,
            'current_positions': len(self.positions),
            'win_rate': self._calculate_win_rate(),
            'profit_factor': self._calculate_profit_factor()
        }
    
    def _calculate_win_rate(self) -> float:
        """Calculate win rate from completed trades."""
        if not self.trades:
            return 0.0
        
        winning_trades = sum(1 for trade in self.trades if trade.is_profitable)
        return (winning_trades / len(self.trades)) * 100
    
    def _calculate_profit_factor(self) -> float:
        """Calculate profit factor."""
        if not self.trades:
            return 0.0
        
        gross_profit = sum(trade.pnl for trade in self.trades if trade.is_profitable)
        gross_loss = abs(sum(trade.pnl for trade in self.trades if not trade.is_profitable))
        
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 0.0
        
        return gross_profit / gross_loss
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert portfolio to dictionary."""
        return {
            'cash': self.cash,
            'total_value': self.total_value,
            'positions_value': self.positions_value,
            'total_pnl': self.total_pnl,
            'positions': {symbol: {
                'quantity': pos.quantity,
                'entry_price': pos.entry_price,
                'current_price': pos.current_price,
                'unrealized_pnl': pos.unrealized_pnl,
                'market_value': pos.market_value
            } for symbol, pos in self.positions.items()},
            'performance': self.get_performance_summary()
        }
