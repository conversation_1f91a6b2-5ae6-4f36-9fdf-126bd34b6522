"""
Strategy optimization module for parameter tuning and walk-forward analysis.
"""

from typing import Dict, Any, List, Tuple, Optional, Callable
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from itertools import product
from concurrent.futures import ProcessPoolExecutor, as_completed
import json

from app.core.logging import get_logger
from .advanced_engine import <PERSON>BacktestEngine, BacktestConfig, BacktestResult
from .strategy import BaseStrategy

logger = get_logger(__name__)


class ParameterRange:
    """Parameter range for optimization."""
    
    def __init__(self, start: float, end: float, step: float = None, values: List = None):
        """
        Initialize parameter range.
        
        Args:
            start: Start value
            end: End value
            step: Step size (if not using explicit values)
            values: Explicit list of values to test
        """
        self.start = start
        self.end = end
        self.step = step
        self.values = values
    
    def get_values(self) -> List[float]:
        """Get list of values to test."""
        if self.values is not None:
            return self.values
        
        if self.step is None:
            return [self.start, self.end]
        
        values = []
        current = self.start
        while current <= self.end:
            values.append(current)
            current += self.step
        
        return values


class OptimizationResult:
    """Results from strategy optimization."""
    
    def __init__(self):
        """Initialize optimization result."""
        self.best_parameters: Dict[str, Any] = {}
        self.best_score: float = float('-inf')
        self.best_result: Optional[BacktestResult] = None
        self.all_results: List[Tuple[Dict[str, Any], BacktestResult]] = []
        self.optimization_metric: str = "sharpe_ratio"
        self.total_combinations: int = 0
        self.completed_combinations: int = 0
        self.start_time: datetime = datetime.now()
        self.end_time: Optional[datetime] = None
    
    def add_result(self, parameters: Dict[str, Any], result: BacktestResult):
        """Add a backtest result."""
        self.all_results.append((parameters, result))
        self.completed_combinations += 1
        
        # Check if this is the best result
        score = getattr(result, self.optimization_metric, float('-inf'))
        if score > self.best_score:
            self.best_score = score
            self.best_parameters = parameters.copy()
            self.best_result = result
    
    def finalize(self):
        """Finalize optimization results."""
        self.end_time = datetime.now()
    
    def get_summary(self) -> Dict[str, Any]:
        """Get optimization summary."""
        duration = (self.end_time - self.start_time).total_seconds() if self.end_time else 0
        
        return {
            'best_parameters': self.best_parameters,
            'best_score': self.best_score,
            'optimization_metric': self.optimization_metric,
            'total_combinations': self.total_combinations,
            'completed_combinations': self.completed_combinations,
            'duration_seconds': duration,
            'success_rate': self.completed_combinations / self.total_combinations if self.total_combinations > 0 else 0
        }


class StrategyOptimizer:
    """Strategy parameter optimizer."""
    
    def __init__(self, config: BacktestConfig = None):
        """Initialize optimizer."""
        self.config = config or BacktestConfig()
        self.engine = AdvancedBacktestEngine(self.config)
    
    def optimize_parameters(
        self,
        strategy_class: type,
        data: pd.DataFrame,
        parameter_ranges: Dict[str, ParameterRange],
        optimization_metric: str = "sharpe_ratio",
        max_workers: int = 4,
        progress_callback: Optional[Callable] = None
    ) -> OptimizationResult:
        """
        Optimize strategy parameters using grid search.
        
        Args:
            strategy_class: Strategy class to optimize
            data: Historical data for backtesting
            parameter_ranges: Dictionary of parameter ranges
            optimization_metric: Metric to optimize
            max_workers: Maximum number of parallel workers
            progress_callback: Optional callback for progress updates
            
        Returns:
            Optimization results
        """
        logger.info(f"Starting parameter optimization for {strategy_class.__name__}")
        
        # Generate parameter combinations
        param_names = list(parameter_ranges.keys())
        param_values = [param_ranges[name].get_values() for name in param_names]
        combinations = list(product(*param_values))
        
        result = OptimizationResult()
        result.optimization_metric = optimization_metric
        result.total_combinations = len(combinations)
        
        logger.info(f"Testing {len(combinations)} parameter combinations")
        
        # Run optimizations
        if max_workers == 1:
            # Single-threaded execution
            for i, combination in enumerate(combinations):
                params = dict(zip(param_names, combination))
                try:
                    backtest_result = self._run_single_backtest(strategy_class, data, params)
                    result.add_result(params, backtest_result)
                except Exception as e:
                    logger.warning(f"Backtest failed for parameters {params}: {e}")
                
                if progress_callback:
                    progress_callback(i + 1, len(combinations))
        
        else:
            # Multi-threaded execution
            with ProcessPoolExecutor(max_workers=max_workers) as executor:
                # Submit all jobs
                future_to_params = {}
                for combination in combinations:
                    params = dict(zip(param_names, combination))
                    future = executor.submit(self._run_single_backtest, strategy_class, data, params)
                    future_to_params[future] = params
                
                # Collect results
                completed = 0
                for future in as_completed(future_to_params):
                    params = future_to_params[future]
                    try:
                        backtest_result = future.result()
                        result.add_result(params, backtest_result)
                    except Exception as e:
                        logger.warning(f"Backtest failed for parameters {params}: {e}")
                    
                    completed += 1
                    if progress_callback:
                        progress_callback(completed, len(combinations))
        
        result.finalize()
        logger.info(f"Optimization completed. Best {optimization_metric}: {result.best_score:.4f}")
        
        return result
    
    def _run_single_backtest(
        self,
        strategy_class: type,
        data: pd.DataFrame,
        parameters: Dict[str, Any]
    ) -> BacktestResult:
        """Run a single backtest with given parameters."""
        # Create strategy instance with parameters
        strategy = strategy_class(**parameters)
        
        # Create new engine instance for thread safety
        engine = AdvancedBacktestEngine(self.config)
        
        # Run backtest
        return engine.run_backtest(strategy, data)
    
    def walk_forward_analysis(
        self,
        strategy_class: type,
        data: pd.DataFrame,
        parameter_ranges: Dict[str, ParameterRange],
        optimization_window: int = 252,  # 1 year
        reoptimization_frequency: int = 63,  # Quarterly
        optimization_metric: str = "sharpe_ratio"
    ) -> Dict[str, Any]:
        """
        Perform walk-forward analysis.
        
        Args:
            strategy_class: Strategy class to test
            data: Historical data
            parameter_ranges: Parameter ranges for optimization
            optimization_window: Number of bars for optimization
            reoptimization_frequency: How often to reoptimize
            optimization_metric: Metric to optimize
            
        Returns:
            Walk-forward analysis results
        """
        logger.info("Starting walk-forward analysis")
        
        results = {
            'periods': [],
            'optimizations': [],
            'out_of_sample_results': [],
            'equity_curve': [],
            'parameter_stability': {}
        }
        
        current_start = 0
        period_number = 0
        
        while current_start + optimization_window < len(data):
            period_number += 1
            
            # Define optimization period
            opt_end = current_start + optimization_window
            optimization_data = data.iloc[current_start:opt_end]
            
            # Define out-of-sample period
            oos_start = opt_end
            oos_end = min(oos_start + reoptimization_frequency, len(data))
            oos_data = data.iloc[oos_start:oos_end]
            
            if len(oos_data) == 0:
                break
            
            logger.info(f"Period {period_number}: Optimizing on {len(optimization_data)} bars, "
                       f"testing on {len(oos_data)} bars")
            
            # Optimize parameters
            optimization_result = self.optimize_parameters(
                strategy_class=strategy_class,
                data=optimization_data,
                parameter_ranges=parameter_ranges,
                optimization_metric=optimization_metric,
                max_workers=1  # Single-threaded for walk-forward
            )
            
            # Test on out-of-sample data
            best_strategy = strategy_class(**optimization_result.best_parameters)
            oos_engine = AdvancedBacktestEngine(self.config)
            oos_result = oos_engine.run_backtest(best_strategy, oos_data)
            
            # Store results
            results['periods'].append({
                'period': period_number,
                'optimization_start': optimization_data.index[0],
                'optimization_end': optimization_data.index[-1],
                'oos_start': oos_data.index[0],
                'oos_end': oos_data.index[-1]
            })
            
            results['optimizations'].append({
                'period': period_number,
                'best_parameters': optimization_result.best_parameters,
                'best_score': optimization_result.best_score,
                'total_combinations': optimization_result.total_combinations
            })
            
            results['out_of_sample_results'].append({
                'period': period_number,
                'total_return': oos_result.total_return,
                'sharpe_ratio': oos_result.sharpe_ratio,
                'max_drawdown': oos_result.max_drawdown,
                'total_trades': oos_result.total_trades
            })
            
            # Extend equity curve
            if period_number == 1:
                results['equity_curve'] = oos_result.equity_curve
            else:
                # Normalize and append
                last_value = results['equity_curve'][-1]
                normalized_curve = [last_value * (1 + (v - oos_result.equity_curve[0]) / oos_result.equity_curve[0]) 
                                  for v in oos_result.equity_curve[1:]]
                results['equity_curve'].extend(normalized_curve)
            
            # Move to next period
            current_start += reoptimization_frequency
        
        # Calculate parameter stability
        all_optimizations = results['optimizations']
        if len(all_optimizations) > 1:
            for param_name in parameter_ranges.keys():
                param_values = [opt['best_parameters'].get(param_name) for opt in all_optimizations]
                param_values = [v for v in param_values if v is not None]
                
                if param_values:
                    results['parameter_stability'][param_name] = {
                        'mean': np.mean(param_values),
                        'std': np.std(param_values),
                        'min': min(param_values),
                        'max': max(param_values),
                        'coefficient_of_variation': np.std(param_values) / np.mean(param_values) if np.mean(param_values) != 0 else 0
                    }
        
        logger.info(f"Walk-forward analysis completed. {period_number} periods analyzed.")
        
        return results
    
    def monte_carlo_analysis(
        self,
        strategy: BaseStrategy,
        data: pd.DataFrame,
        num_simulations: int = 1000,
        confidence_levels: List[float] = [0.05, 0.95]
    ) -> Dict[str, Any]:
        """
        Perform Monte Carlo analysis on strategy returns.
        
        Args:
            strategy: Strategy to analyze
            data: Historical data
            num_simulations: Number of Monte Carlo simulations
            confidence_levels: Confidence levels for analysis
            
        Returns:
            Monte Carlo analysis results
        """
        logger.info(f"Starting Monte Carlo analysis with {num_simulations} simulations")
        
        # Run initial backtest to get trade returns
        initial_result = self.engine.run_backtest(strategy, data)
        
        if not initial_result.trade_log:
            raise ValueError("No trades found in initial backtest")
        
        # Extract trade returns
        trade_returns = []
        for i in range(1, len(initial_result.trade_log)):
            if initial_result.trade_log[i]['side'] == 'SELL':
                # Find corresponding buy
                for j in range(i-1, -1, -1):
                    if initial_result.trade_log[j]['side'] == 'BUY':
                        buy_price = initial_result.trade_log[j]['price']
                        sell_price = initial_result.trade_log[i]['price']
                        trade_return = (sell_price - buy_price) / buy_price
                        trade_returns.append(trade_return)
                        break
        
        if not trade_returns:
            raise ValueError("No complete trades found for Monte Carlo analysis")
        
        # Run Monte Carlo simulations
        simulation_results = []
        
        for sim in range(num_simulations):
            # Randomly sample trade returns with replacement
            simulated_returns = np.random.choice(trade_returns, size=len(trade_returns), replace=True)
            
            # Calculate cumulative return
            cumulative_return = np.prod(1 + np.array(simulated_returns)) - 1
            simulation_results.append(cumulative_return)
        
        # Calculate statistics
        simulation_results = np.array(simulation_results)
        
        results = {
            'num_simulations': num_simulations,
            'original_return': initial_result.total_return,
            'mean_return': np.mean(simulation_results),
            'std_return': np.std(simulation_results),
            'min_return': np.min(simulation_results),
            'max_return': np.max(simulation_results),
            'confidence_intervals': {},
            'probability_of_loss': np.sum(simulation_results < 0) / num_simulations,
            'probability_of_outperforming_original': np.sum(simulation_results > initial_result.total_return) / num_simulations
        }
        
        # Calculate confidence intervals
        for level in confidence_levels:
            lower_percentile = (1 - level) / 2 * 100
            upper_percentile = (1 + level) / 2 * 100
            
            results['confidence_intervals'][f'{level:.0%}'] = {
                'lower': np.percentile(simulation_results, lower_percentile),
                'upper': np.percentile(simulation_results, upper_percentile)
            }
        
        logger.info(f"Monte Carlo analysis completed. Mean return: {results['mean_return']:.2%}")
        
        return results
