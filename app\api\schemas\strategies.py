"""
Strategy management API schemas.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field

from app.api.schemas.common import BaseResponse


class CreateStrategyRequest(BaseModel):
    """Create strategy request."""
    name: str = Field(description="Strategy name")
    description: Optional[str] = Field(default=None, description="Strategy description")
    strategy_type: str = Field(description="Strategy type")
    parameters: Dict[str, Any] = Field(description="Strategy parameters")
    symbols: List[str] = Field(description="Symbols to trade")
    is_active: bool = Field(default=True, description="Whether strategy is active")


class UpdateStrategyRequest(BaseModel):
    """Update strategy request."""
    name: Optional[str] = Field(default=None, description="Strategy name")
    description: Optional[str] = Field(default=None, description="Strategy description")
    parameters: Optional[Dict[str, Any]] = Field(default=None, description="Strategy parameters")
    symbols: Optional[List[str]] = Field(default=None, description="Symbols to trade")
    is_active: Optional[bool] = Field(default=None, description="Whether strategy is active")


class StrategyInfo(BaseModel):
    """Strategy information."""
    id: int
    name: str
    description: Optional[str]
    strategy_type: str
    parameters: Dict[str, Any]
    symbols: List[str]
    is_active: bool
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None


class StrategyResponse(BaseResponse):
    """Strategy response."""
    strategy: StrategyInfo


class StrategyListResponse(BaseResponse):
    """Strategy list response."""
    strategies: List[StrategyInfo]
    total_count: int


class StrategyTypeInfo(BaseModel):
    """Strategy type information."""
    key: str
    name: str
    description: str
    default_parameters: Dict[str, Any]
    required_parameters: List[str]
    parameter_descriptions: Dict[str, str]


class StrategyTypesResponse(BaseResponse):
    """Strategy types response."""
    strategy_types: List[StrategyTypeInfo]


class ValidateStrategyRequest(BaseModel):
    """Validate strategy request."""
    strategy_type: str = Field(description="Strategy type")
    parameters: Dict[str, Any] = Field(description="Strategy parameters")
    symbols: List[str] = Field(description="Symbols to validate")


class ValidationIssue(BaseModel):
    """Validation issue."""
    type: str  # 'error', 'warning', 'info'
    field: Optional[str] = None
    message: str


class ValidateStrategyResponse(BaseResponse):
    """Validate strategy response."""
    is_valid: bool
    issues: List[ValidationIssue]
    recommendations: List[str] = []


class StrategyPerformanceRequest(BaseModel):
    """Strategy performance request."""
    start_date: Optional[datetime] = Field(default=None, description="Start date")
    end_date: Optional[datetime] = Field(default=None, description="End date")
    include_trades: bool = Field(default=False, description="Include trade details")


class StrategyPerformanceResponse(BaseResponse):
    """Strategy performance response."""
    strategy_id: int
    performance_metrics: Dict[str, Any]
    backtest_results: List[Dict[str, Any]]
    paper_trading_results: List[Dict[str, Any]]
    trade_summary: Optional[Dict[str, Any]] = None


class CloneStrategyRequest(BaseModel):
    """Clone strategy request."""
    new_name: str = Field(description="New strategy name")
    new_description: Optional[str] = Field(default=None, description="New strategy description")
    modify_parameters: Optional[Dict[str, Any]] = Field(default=None, description="Parameters to modify")


class StrategyExecutionRequest(BaseModel):
    """Strategy execution request."""
    execution_type: str = Field(description="Execution type (backtest/paper_trading)")
    start_date: Optional[datetime] = Field(default=None, description="Start date")
    end_date: Optional[datetime] = Field(default=None, description="End date")
    initial_cash: float = Field(default=100000.0, description="Initial cash")
    commission_rate: float = Field(default=0.001, description="Commission rate")
    additional_config: Optional[Dict[str, Any]] = Field(default=None, description="Additional configuration")


class StrategyExecutionResponse(BaseResponse):
    """Strategy execution response."""
    execution_id: str
    strategy_id: int
    execution_type: str
    status: str
    started_at: datetime
    estimated_completion: Optional[datetime] = None
