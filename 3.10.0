Collecting pytest-mock
  Downloading pytest_mock-3.14.1-py3-none-any.whl.metadata (3.9 kB)
Requirement already satisfied: pytest>=6.2.5 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from pytest-mock) (7.4.3)
Requirement already satisfied: iniconfig in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from pytest>=6.2.5->pytest-mock) (2.1.0)
Requirement already satisfied: packaging in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from pytest>=6.2.5->pytest-mock) (24.2)
Requirement already satisfied: pluggy<2.0,>=0.12 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from pytest>=6.2.5->pytest-mock) (1.5.0)
Requirement already satisfied: exceptiongroup>=1.0.0rc8 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from pytest>=6.2.5->pytest-mock) (1.2.2)
Requirement already satisfied: tomli>=1.0.0 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from pytest>=6.2.5->pytest-mock) (2.2.1)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from pytest>=6.2.5->pytest-mock) (0.4.6)
Downloading pytest_mock-3.14.1-py3-none-any.whl (9.9 kB)
Installing collected packages: pytest-mock
Successfully installed pytest-mock-3.14.1
