"""
Backtesting API schemas.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field

from app.api.schemas.common import BaseResponse


class BacktestRequest(BaseModel):
    """Backtest execution request."""
    strategy_type: str = Field(description="Strategy type")
    symbol: str = Field(description="Symbol to backtest")
    start_date: datetime = Field(description="Backtest start date")
    end_date: datetime = Field(description="Backtest end date")
    timeframe: str = Field(default="1d", description="Data timeframe")
    initial_cash: float = Field(default=100000.0, description="Initial capital")
    commission_rate: float = Field(default=0.001, description="Commission rate")
    strategy_parameters: Optional[Dict[str, Any]] = Field(default=None, description="Strategy parameters")
    position_size_method: str = Field(default="fixed_amount", description="Position sizing method")
    position_size_value: float = Field(default=10000.0, description="Position size value")


class StrategyInfo(BaseModel):
    """Strategy information."""
    key: str
    name: str
    class_name: str
    description: str
    default_parameters: Dict[str, Any]


class StrategiesListResponse(BaseResponse):
    """Available strategies list response."""
    strategies: List[StrategyInfo]


class TradeRecord(BaseModel):
    """Individual trade record."""
    symbol: str
    position_type: str
    quantity: int
    entry_price: float
    exit_price: float
    entry_timestamp: datetime
    exit_timestamp: datetime
    pnl: float
    return_percentage: float
    duration: float
    commission: float


class EquityCurvePoint(BaseModel):
    """Equity curve data point."""
    timestamp: datetime
    total_value: float
    cash: float
    positions_value: float
    unrealized_pnl: float
    realized_pnl: float
    total_pnl: float


class BacktestResults(BaseModel):
    """Comprehensive backtest results."""
    strategy_name: str
    strategy_parameters: Dict[str, Any]
    symbol: str
    backtest_config: Dict[str, Any]
    data_info: Dict[str, Any]
    portfolio_summary: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    trades: List[Dict[str, Any]]
    equity_curve: List[EquityCurvePoint]
    trade_analysis: Dict[str, Any]
    execution_time: float
    backtest_id: str


class BacktestResponse(BaseResponse):
    """Backtest execution response."""
    results: BacktestResults


class BacktestHistoryItem(BaseModel):
    """Backtest history item."""
    id: int
    strategy_id: int
    symbol: str
    start_date: datetime
    end_date: datetime
    total_return: float
    total_trades: int
    win_rate: float
    max_drawdown: float
    profit_factor: float
    created_at: datetime


class BacktestHistoryResponse(BaseResponse):
    """Backtest history response."""
    history: List[BacktestHistoryItem]
    total_count: int


class SaveBacktestRequest(BaseModel):
    """Save backtest results request."""
    strategy_id: int = Field(description="Strategy ID")
    symbol: str = Field(description="Symbol")
    results: Dict[str, Any] = Field(description="Backtest results")


class SaveBacktestResponse(BaseResponse):
    """Save backtest results response."""
    saved: bool
    backtest_id: Optional[int] = None


class StrategyComparisonRequest(BaseModel):
    """Strategy comparison request."""
    strategies: List[Dict[str, Any]] = Field(description="Strategy configurations")
    symbol: str = Field(description="Symbol to test")
    start_date: datetime = Field(description="Start date")
    end_date: datetime = Field(description="End date")
    timeframe: str = Field(default="1d", description="Data timeframe")


class StrategyComparisonResult(BaseModel):
    """Individual strategy comparison result."""
    strategy_name: str
    total_return: float
    max_drawdown: float
    sharpe_ratio: float
    total_trades: int
    win_rate: float


class StrategyComparisonResponse(BaseResponse):
    """Strategy comparison response."""
    symbol: str
    period: str
    timeframe: str
    strategies: List[StrategyComparisonResult]
    summary: Dict[str, Any]


class BacktestValidationRequest(BaseModel):
    """Backtest validation request."""
    symbol: str = Field(description="Symbol to validate")
    start_date: datetime = Field(description="Start date")
    end_date: datetime = Field(description="End date")
    timeframe: str = Field(default="1d", description="Data timeframe")


class BacktestValidationResponse(BaseResponse):
    """Backtest validation response."""
    is_valid: bool
    data_points: int
    date_range: Dict[str, str]
    issues: List[str] = []
    recommendations: List[str] = []


class HybridBacktestRequest(BaseModel):
    """Hybrid backtest execution request."""
    symbol: str = Field(default="NIFTY50", description="Symbol to backtest")
    timeframe: int = Field(default=30, description="Timeframe in minutes")
    start_date: Optional[str] = Field(default=None, description="Start date (YYYY-MM-DD)")
    end_date: Optional[str] = Field(default=None, description="End date (YYYY-MM-DD)")
    initial_capital: float = Field(default=30000.0, description="Initial capital")
    margin: float = Field(default=0.1, description="Margin requirement")
    commission: float = Field(default=0.0, description="Commission rate")
    generate_files: bool = Field(default=False, description="Generate output files")
    strategy_config: Optional[Dict[str, Any]] = Field(default=None, description="Strategy configuration")


class HybridBacktestResults(BaseModel):
    """Hybrid backtest results."""
    symbol: str
    timeframe: int
    execution_time: float
    data_shape: List[int]
    backtest_library_results: Dict[str, Any]
    custom_metrics: Dict[str, Any]
    reports: Dict[str, str]
    validation: Dict[str, Any]
    timestamp: str


class HybridBacktestResponse(BaseResponse):
    """Hybrid backtest execution response."""
    results: HybridBacktestResults
