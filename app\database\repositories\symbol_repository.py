"""
Repository for Symbol model operations.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.database.models import Symbol, MarketType
from app.database.repositories.base import BaseRepository


class SymbolRepository(BaseRepository[Symbol]):
    """Repository for Symbol operations."""
    
    def __init__(self, db: Session):
        super().__init__(Symbol, db)
    
    def get_by_symbol(self, symbol: str) -> Optional[Symbol]:
        """
        Get symbol by symbol name.
        
        Args:
            symbol: Symbol name (e.g., 'NIFTY', 'RELIANCE')
            
        Returns:
            Symbol record if found, None otherwise
        """
        return self.db.query(Symbol).filter(Symbol.symbol == symbol).first()
    
    def get_by_token(self, token: str) -> Optional[Symbol]:
        """
        Get symbol by token.
        
        Args:
            token: Symbol token
            
        Returns:
            Symbol record if found, None otherwise
        """
        return self.db.query(Symbol).filter(Symbol.token == token).first()
    
    def get_by_market_type(self, market_type: MarketType) -> List[Symbol]:
        """
        Get symbols by market type.
        
        Args:
            market_type: Market type (EQUITY, INDEX, FUTURES, OPTIONS)
            
        Returns:
            List of symbols
        """
        return self.db.query(Symbol).filter(
            and_(Symbol.market_type == market_type, Symbol.is_active == True)
        ).all()
    
    def get_active_symbols(self) -> List[Symbol]:
        """
        Get all active symbols.
        
        Returns:
            List of active symbols
        """
        return self.db.query(Symbol).filter(Symbol.is_active == True).all()
    
    def search_symbols(self, search_term: str, limit: int = 50) -> List[Symbol]:
        """
        Search symbols by name or symbol.
        
        Args:
            search_term: Search term
            limit: Maximum number of results
            
        Returns:
            List of matching symbols
        """
        search_pattern = f"%{search_term.upper()}%"
        return self.db.query(Symbol).filter(
            and_(
                Symbol.is_active == True,
                or_(
                    Symbol.symbol.ilike(search_pattern),
                    Symbol.name.ilike(search_pattern)
                )
            )
        ).limit(limit).all()
    
    def bulk_create_symbols(self, symbols_data: List[Dict[str, Any]]) -> List[Symbol]:
        """
        Bulk create symbols.
        
        Args:
            symbols_data: List of symbol data dictionaries
            
        Returns:
            List of created symbols
        """
        symbols = []
        for symbol_data in symbols_data:
            # Check if symbol already exists
            existing = self.get_by_symbol(symbol_data['symbol'])
            if not existing:
                symbol = Symbol(**symbol_data)
                symbols.append(symbol)
                self.db.add(symbol)
        
        if symbols:
            self.db.commit()
            for symbol in symbols:
                self.db.refresh(symbol)
        
        return symbols
    
    def update_symbol_status(self, symbol_id: int, is_active: bool) -> Optional[Symbol]:
        """
        Update symbol active status.
        
        Args:
            symbol_id: Symbol ID
            is_active: Active status
            
        Returns:
            Updated symbol if found, None otherwise
        """
        return self.update(symbol_id, {'is_active': is_active})
    
    def get_symbols_by_exchange(self, exchange: str) -> List[Symbol]:
        """
        Get symbols by exchange.
        
        Args:
            exchange: Exchange name (e.g., 'NSE', 'BSE')
            
        Returns:
            List of symbols
        """
        return self.db.query(Symbol).filter(
            and_(Symbol.exchange == exchange, Symbol.is_active == True)
        ).all()
    
    def get_symbol_stats(self) -> Dict[str, int]:
        """
        Get symbol statistics.
        
        Returns:
            Dictionary with symbol counts by market type
        """
        stats = {}
        
        # Total symbols
        stats['total'] = self.db.query(Symbol).count()
        stats['active'] = self.db.query(Symbol).filter(Symbol.is_active == True).count()
        
        # By market type
        for market_type in MarketType:
            stats[market_type.value.lower()] = self.db.query(Symbol).filter(
                and_(Symbol.market_type == market_type, Symbol.is_active == True)
            ).count()
        
        return stats
