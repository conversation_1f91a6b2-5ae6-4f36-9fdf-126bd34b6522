# Fyers API Configuration
FYERS_CLIENT_ID=your_fyers_client_id
FYERS_SECRET_KEY=your_fyers_secret_key
FYERS_REDIRECT_URI=http://localhost:8080/callback
FYERS_ACCESS_TOKEN=your_access_token

# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=nse_db
DATABASE_USER=postgres
DATABASE_PASSWORD=your_database_password

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# Application Configuration
DEBUG=True
LOG_LEVEL=INFO
SECRET_KEY=your_secret_key_for_jwt

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Frontend Configuration
FRONTEND_URL=http://localhost:3000
