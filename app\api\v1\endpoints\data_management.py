"""
Data management API endpoints.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import time
import asyncio
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session

from app.api.dependencies import validate_symbol
from app.api.schemas.data_management import (
    IngestSymbolsRequest,
    IngestSymbolsResponse,
    IngestHistoricalDataRequest,
    IngestHistoricalDataResponse,
    IngestionStatusResponse,
    DataCoverageInfo,
    DataQualityResponse,
    DataQualityCheck,
    DataMaintenanceRequest,
    DataMaintenanceResponse
)
from app.services.data_ingestion_service import DataIngestionService
from app.database.connection import get_db
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


def get_data_ingestion_service(db: Session = Depends(get_db)) -> DataIngestionService:
    """Get data ingestion service instance."""
    return DataIngestionService(db)


@router.post("/ingest-symbols", response_model=IngestSymbolsResponse)
async def ingest_symbols(
    request: IngestSymbolsRequest,
    background_tasks: BackgroundTasks,
    service: DataIngestionService = Depends(get_data_ingestion_service)
):
    """Ingest symbol list from market data provider."""
    try:
        start_time = time.time()
        
        # Run ingestion
        symbols_ingested = await service.ingest_symbol_list(
            market_types=request.market_types
        )
        
        execution_time = time.time() - start_time
        
        # Get total symbols count
        total_symbols = service.symbol_repo.count()
        
        return IngestSymbolsResponse(
            symbols_ingested=symbols_ingested,
            total_symbols=total_symbols,
            execution_time=execution_time
        )
        
    except Exception as e:
        logger.error(f"Error ingesting symbols: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to ingest symbols"
        )


@router.post("/ingest-historical", response_model=IngestHistoricalDataResponse)
async def ingest_historical_data(
    request: IngestHistoricalDataRequest,
    background_tasks: BackgroundTasks,
    service: DataIngestionService = Depends(get_data_ingestion_service)
):
    """Ingest historical OHLCV data for specified symbols."""
    try:
        # Validate inputs
        if request.start_date >= request.end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date"
            )
        
        # Validate symbols
        validated_symbols = []
        for symbol in request.symbols:
            try:
                validated_symbols.append(validate_symbol(symbol))
            except Exception as e:
                logger.warning(f"Invalid symbol {symbol}: {e}")
        
        if not validated_symbols:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No valid symbols provided"
            )
        
        start_time = time.time()
        
        # Run bulk ingestion
        ingestion_results = await service.bulk_ingest_historical_data(
            symbols=validated_symbols,
            start_date=request.start_date,
            end_date=request.end_date,
            timeframe=request.timeframe,
            max_concurrent=request.max_concurrent
        )
        
        execution_time = time.time() - start_time
        
        # Calculate totals
        total_records = sum(ingestion_results.values())
        failed_symbols = [symbol for symbol, count in ingestion_results.items() if count == 0]
        
        return IngestHistoricalDataResponse(
            ingestion_results=ingestion_results,
            total_records=total_records,
            execution_time=execution_time,
            failed_symbols=failed_symbols
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error ingesting historical data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to ingest historical data"
        )


@router.get("/status", response_model=IngestionStatusResponse)
async def get_ingestion_status(
    symbol: Optional[str] = None,
    service: DataIngestionService = Depends(get_data_ingestion_service)
):
    """Get data ingestion status and coverage information."""
    try:
        if symbol:
            symbol = validate_symbol(symbol)
        
        status_data = service.get_ingestion_status(symbol)
        
        if 'error' in status_data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=status_data['error']
            )
        
        # Convert data coverage to response format
        data_coverage = {}
        for symbol_name, coverage_data in status_data.get('data_coverage', {}).items():
            data_coverage[symbol_name] = DataCoverageInfo(
                symbol=symbol_name,
                earliest_date=coverage_data.get('earliest_date'),
                latest_date=coverage_data.get('latest_date'),
                total_records=coverage_data.get('record_count', 0),
                gaps=coverage_data.get('gaps', [])
            )
        
        return IngestionStatusResponse(
            total_symbols=status_data['total_symbols'],
            active_symbols=status_data['active_symbols'],
            total_ohlcv_records=status_data['total_ohlcv_records'],
            last_update=datetime.fromisoformat(status_data['last_update']) if status_data.get('last_update') else None,
            data_coverage=data_coverage
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting ingestion status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get ingestion status"
        )


@router.get("/quality-check/{symbol}", response_model=DataQualityResponse)
async def check_data_quality(
    symbol: str,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    service: DataIngestionService = Depends(get_data_ingestion_service)
):
    """Perform data quality checks for a symbol."""
    try:
        symbol = validate_symbol(symbol)
        
        # Set default date range if not provided
        if not end_date:
            end_date = datetime.utcnow()
        if not start_date:
            start_date = end_date - timedelta(days=30)
        
        # Get symbol from database
        symbol_obj = service.symbol_repo.get_by_symbol(symbol)
        if not symbol_obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Symbol not found: {symbol}"
            )
        
        # Perform quality checks
        checks = []
        overall_status = "pass"
        recommendations = []
        
        # Check 1: Data availability
        data_count = service.ohlcv_repo.count({'symbol_id': symbol_obj.id})
        if data_count == 0:
            checks.append(DataQualityCheck(
                symbol=symbol,
                check_type="data_availability",
                status="fail",
                message="No historical data found"
            ))
            overall_status = "fail"
        else:
            checks.append(DataQualityCheck(
                symbol=symbol,
                check_type="data_availability",
                status="pass",
                message=f"Found {data_count} historical records"
            ))
        
        # Check 2: Data freshness
        if data_count > 0:
            latest_data = service.ohlcv_repo.get_latest_data(symbol_obj.id)
            if latest_data:
                days_old = (datetime.utcnow() - latest_data.timestamp).days
                if days_old > 7:
                    checks.append(DataQualityCheck(
                        symbol=symbol,
                        check_type="data_freshness",
                        status="warning",
                        message=f"Latest data is {days_old} days old"
                    ))
                    recommendations.append("Consider updating historical data")
                    if overall_status == "pass":
                        overall_status = "warning"
                else:
                    checks.append(DataQualityCheck(
                        symbol=symbol,
                        check_type="data_freshness",
                        status="pass",
                        message=f"Data is current (latest: {latest_data.timestamp.date()})"
                    ))
        
        # Check 3: Data gaps
        data_range = service.ohlcv_repo.get_data_range(symbol_obj.id, start_date, end_date)
        if data_range:
            expected_records = (end_date - start_date).days * 375  # Approximate for 1-min data
            actual_records = len(data_range)
            coverage_ratio = actual_records / expected_records if expected_records > 0 else 0
            
            if coverage_ratio < 0.8:
                checks.append(DataQualityCheck(
                    symbol=symbol,
                    check_type="data_completeness",
                    status="warning",
                    message=f"Data coverage is {coverage_ratio:.1%}",
                    details={"expected": expected_records, "actual": actual_records}
                ))
                recommendations.append("Consider filling data gaps")
                if overall_status == "pass":
                    overall_status = "warning"
            else:
                checks.append(DataQualityCheck(
                    symbol=symbol,
                    check_type="data_completeness",
                    status="pass",
                    message=f"Good data coverage: {coverage_ratio:.1%}"
                ))
        
        return DataQualityResponse(
            symbol=symbol,
            overall_status=overall_status,
            checks=checks,
            recommendations=recommendations
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking data quality for {symbol}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check data quality"
        )


@router.post("/maintenance", response_model=DataMaintenanceResponse)
async def perform_data_maintenance(
    request: DataMaintenanceRequest,
    background_tasks: BackgroundTasks,
    service: DataIngestionService = Depends(get_data_ingestion_service)
):
    """Perform data maintenance operations."""
    try:
        start_time = time.time()
        records_affected = 0
        details = {}
        
        if request.operation == "cleanup":
            # Clean up old data
            if request.older_than_days:
                cutoff_date = datetime.utcnow() - timedelta(days=request.older_than_days)
                
                if request.symbols:
                    # Clean specific symbols
                    for symbol in request.symbols:
                        symbol_obj = service.symbol_repo.get_by_symbol(symbol)
                        if symbol_obj:
                            deleted = service.ohlcv_repo.delete_old_data(symbol_obj.id, cutoff_date)
                            records_affected += deleted
                            details[symbol] = deleted
                else:
                    # Clean all symbols
                    records_affected = service.ohlcv_repo.delete_old_data(None, cutoff_date)
                    details['total_deleted'] = records_affected
        
        elif request.operation == "reindex":
            # Reindex database tables
            service.db.execute("REINDEX INDEX CONCURRENTLY idx_ohlcv_symbol_time;")
            service.db.execute("REINDEX INDEX CONCURRENTLY idx_ohlcv_timestamp;")
            service.db.commit()
            details['operation'] = 'Database indexes rebuilt'
        
        elif request.operation == "vacuum":
            # Vacuum database
            service.db.execute("VACUUM ANALYZE ohlcv_data;")
            service.db.execute("VACUUM ANALYZE symbols;")
            service.db.commit()
            details['operation'] = 'Database vacuumed and analyzed'
        
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unknown maintenance operation: {request.operation}"
            )
        
        execution_time = time.time() - start_time
        
        return DataMaintenanceResponse(
            operation=request.operation,
            records_affected=records_affected,
            execution_time=execution_time,
            details=details
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error performing maintenance: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform maintenance operation"
        )


@router.post("/ingest-single/{symbol}")
async def ingest_single_symbol(
    symbol: str,
    start_date: datetime,
    end_date: datetime,
    timeframe: str = "1",
    force_update: bool = False,
    service: DataIngestionService = Depends(get_data_ingestion_service)
):
    """Ingest historical data for a single symbol."""
    try:
        symbol = validate_symbol(symbol)
        
        if start_date >= end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date"
            )
        
        records_ingested = await service.ingest_historical_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            timeframe=timeframe,
            force_update=force_update
        )
        
        return {
            "success": True,
            "symbol": symbol,
            "records_ingested": records_ingested,
            "timeframe": timeframe,
            "date_range": f"{start_date.date()} to {end_date.date()}"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error ingesting data for {symbol}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to ingest data for {symbol}"
        )


@router.get("/symbols-without-data")
async def get_symbols_without_data(
    limit: int = 100,
    service: DataIngestionService = Depends(get_data_ingestion_service)
):
    """Get symbols that don't have any historical data."""
    try:
        # Query symbols without OHLCV data
        symbols_without_data = service.db.query(
            service.symbol_repo.model.symbol
        ).outerjoin(
            service.ohlcv_repo.model
        ).filter(
            service.ohlcv_repo.model.id.is_(None),
            service.symbol_repo.model.is_active == True
        ).limit(limit).all()
        
        symbol_list = [symbol[0] for symbol in symbols_without_data]
        
        return {
            "success": True,
            "symbols_without_data": symbol_list,
            "count": len(symbol_list),
            "message": f"Found {len(symbol_list)} symbols without historical data"
        }
        
    except Exception as e:
        logger.error(f"Error getting symbols without data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get symbols without data"
        )
