{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Python\\\\signal_stack\\\\frontend\\\\src\\\\components\\\\BacktestingDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, Typography, Button, Grid, Card, CardContent, FormControl, InputLabel, Select, MenuItem, TextField, CircularProgress, Alert, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, LinearProgress } from '@mui/material';\nimport { PlayArrow, Assessment, TrendingUp, TrendingDown, Speed } from '@mui/icons-material';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BacktestingDashboard = () => {\n  _s();\n  const [strategies, setStrategies] = useState([]);\n  const [selectedStrategy, setSelectedStrategy] = useState('');\n  const [backtestParams, setBacktestParams] = useState({\n    symbol: 'NIFTY50',\n    timeframe: 30,\n    start_date: '2024-01-01',\n    end_date: '2024-12-31',\n    initial_capital: 30000,\n    margin: 0.1,\n    commission: 0.0,\n    strategy_config: {},\n    generate_files: false\n  });\n  const [isRunning, setIsRunning] = useState(false);\n  const [results, setResults] = useState(null);\n  const [error, setError] = useState(null);\n  const [progress, setProgress] = useState(0);\n  useEffect(() => {\n    fetchStrategies();\n  }, []);\n  const fetchStrategies = async () => {\n    try {\n      const response = await axios.get('http://localhost:8000/api/v1/backtesting/strategies');\n      setStrategies(response.data.strategies);\n      if (response.data.strategies.length > 0) {\n        setSelectedStrategy(response.data.strategies[0].name);\n      }\n    } catch (error) {\n      console.error('Error fetching strategies:', error);\n      setError('Failed to fetch strategies');\n    }\n  };\n  const runBacktest = async () => {\n    if (!selectedStrategy) {\n      setError('Please select a strategy');\n      return;\n    }\n    setIsRunning(true);\n    setError(null);\n    setResults(null);\n    setProgress(0);\n\n    // Simulate progress updates\n    const progressInterval = setInterval(() => {\n      setProgress(prev => Math.min(prev + 10, 90));\n    }, 500);\n    try {\n      const response = await axios.post('http://localhost:8000/api/v1/backtesting/reference/run', backtestParams, {\n        timeout: 120000\n      });\n      if (response.data.success) {\n        setResults(response.data.results);\n        setProgress(100);\n      } else {\n        setError(response.data.message || 'Backtest failed');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error running backtest:', error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Failed to run backtest');\n    } finally {\n      clearInterval(progressInterval);\n      setIsRunning(false);\n      setProgress(0);\n    }\n  };\n  const formatNumber = (value, decimals = 2) => {\n    return value.toFixed(decimals);\n  };\n  const formatDuration = minutes => {\n    if (minutes < 60) {\n      return `${minutes.toFixed(0)}m`;\n    }\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins.toFixed(0)}m`;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Reference Backtesting Framework\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"subtitle1\",\n      color: \"text.secondary\",\n      gutterBottom: true,\n      children: \"High-performance backtesting with SimplePriceActionStrategy\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Backtest Configuration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            margin: \"normal\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Strategy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedStrategy,\n              onChange: e => setSelectedStrategy(e.target.value),\n              label: \"Strategy\",\n              children: strategies.map(strategy => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: strategy.name,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: strategy.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: strategy.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 21\n                }, this)\n              }, strategy.name, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            margin: \"normal\",\n            label: \"Symbol\",\n            value: backtestParams.symbol,\n            onChange: e => setBacktestParams({\n              ...backtestParams,\n              symbol: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            margin: \"normal\",\n            label: \"Timeframe (minutes)\",\n            type: \"number\",\n            value: backtestParams.timeframe,\n            onChange: e => setBacktestParams({\n              ...backtestParams,\n              timeframe: parseInt(e.target.value)\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            margin: \"normal\",\n            label: \"Start Date\",\n            type: \"date\",\n            value: backtestParams.start_date,\n            onChange: e => setBacktestParams({\n              ...backtestParams,\n              start_date: e.target.value\n            }),\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            margin: \"normal\",\n            label: \"End Date\",\n            type: \"date\",\n            value: backtestParams.end_date,\n            onChange: e => setBacktestParams({\n              ...backtestParams,\n              end_date: e.target.value\n            }),\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            margin: \"normal\",\n            label: \"Initial Capital\",\n            type: \"number\",\n            value: backtestParams.initial_capital,\n            onChange: e => setBacktestParams({\n              ...backtestParams,\n              initial_capital: parseFloat(e.target.value)\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            margin: \"normal\",\n            label: \"Margin\",\n            type: \"number\",\n            step: \"0.01\",\n            value: backtestParams.margin,\n            onChange: e => setBacktestParams({\n              ...backtestParams,\n              margin: parseFloat(e.target.value)\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"contained\",\n            size: \"large\",\n            startIcon: isRunning ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(PlayArrow, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 71\n            }, this),\n            onClick: runBacktest,\n            disabled: isRunning,\n            sx: {\n              mt: 3\n            },\n            children: isRunning ? 'Running Backtest...' : 'Run Backtest'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), isRunning && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(LinearProgress, {\n              variant: \"determinate\",\n              value: progress\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              sx: {\n                mt: 1\n              },\n              children: [\"Progress: \", progress, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 8,\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this), results && /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Performance Summary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  sm: 3,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    children: /*#__PURE__*/_jsxDEV(CardContent, {\n                      sx: {\n                        textAlign: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                        color: \"primary\",\n                        sx: {\n                          fontSize: 40\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 313,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        children: [formatNumber(results.performance_summary.total_return), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 314,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        children: \"Total Return\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 317,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 312,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  sm: 3,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    children: /*#__PURE__*/_jsxDEV(CardContent, {\n                      sx: {\n                        textAlign: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Assessment, {\n                        color: \"secondary\",\n                        sx: {\n                          fontSize: 40\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 324,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        children: [formatNumber(results.performance_summary.win_rate), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 325,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        children: \"Win Rate\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 328,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  sm: 3,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    children: /*#__PURE__*/_jsxDEV(CardContent, {\n                      sx: {\n                        textAlign: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(TrendingDown, {\n                        color: \"error\",\n                        sx: {\n                          fontSize: 40\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 335,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        children: [formatNumber(Math.abs(results.performance_summary.max_drawdown)), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 336,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        children: \"Max Drawdown\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 339,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  sm: 3,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    children: /*#__PURE__*/_jsxDEV(CardContent, {\n                      sx: {\n                        textAlign: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Speed, {\n                        color: \"info\",\n                        sx: {\n                          fontSize: 40\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 346,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        children: [formatNumber(results.execution_time), \"s\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 347,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        children: \"Execution Time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 350,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Detailed Metrics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n                children: /*#__PURE__*/_jsxDEV(Table, {\n                  size: \"small\",\n                  children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                    children: /*#__PURE__*/_jsxDEV(TableRow, {\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        children: \"Metric\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 368,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        align: \"right\",\n                        children: \"Value\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 369,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 367,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                    children: [/*#__PURE__*/_jsxDEV(TableRow, {\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        children: \"Total Trades\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 374,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        align: \"right\",\n                        children: results.performance_summary.total_trades\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 375,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        children: \"Win Rate\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 378,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        align: \"right\",\n                        children: [formatNumber(results.performance_summary.win_rate), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 379,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        children: \"Total Return\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 382,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        align: \"right\",\n                        children: [formatNumber(results.performance_summary.total_return), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 383,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 381,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        children: \"Max Drawdown\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 386,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        align: \"right\",\n                        children: [formatNumber(Math.abs(results.performance_summary.max_drawdown)), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 387,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        children: \"Sharpe Ratio\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 390,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        align: \"right\",\n                        children: formatNumber(results.performance_summary.sharpe_ratio)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 391,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 389,\n                      columnNumber: 25\n                    }, this), results.custom_metrics && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(TableRow, {\n                        children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                          children: \"Profit Factor\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 396,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          align: \"right\",\n                          children: formatNumber(results.custom_metrics.profit_factor)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 397,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 395,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                        children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                          children: \"Risk-to-Reward Ratio\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 400,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          align: \"right\",\n                          children: formatNumber(results.custom_metrics.risk_reward_ratio)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 401,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 399,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                        children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                          children: \"Avg Trade Duration\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 404,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          align: \"right\",\n                          children: formatDuration(results.custom_metrics.avg_trade_duration_minutes)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 405,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 403,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                        children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                          children: \"Total P&L\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 408,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          align: \"right\",\n                          children: [\"\\u20B9\", formatNumber(results.custom_metrics.total_pnl)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 409,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 407,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Execution Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  sm: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\"Symbol: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: results.symbol\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\"Timeframe: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [results.timeframe, \" minutes\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 431,\n                      columnNumber: 36\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\"Data Points: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: results.data_points.toLocaleString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 434,\n                      columnNumber: 38\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  sm: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\"Execution Time: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [formatNumber(results.execution_time), \" seconds\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 439,\n                      columnNumber: 41\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\"Processing Rate: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [formatNumber(results.data_points / results.execution_time), \" bars/sec\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 42\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: \"Reference Framework\",\n                    color: \"primary\",\n                    size: \"small\",\n                    sx: {\n                      mt: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 166,\n    columnNumber: 5\n  }, this);\n};\n_s(BacktestingDashboard, \"T6b3BjLtQXEG9cI8skDtt5JS2X8=\");\n_c = BacktestingDashboard;\nexport default BacktestingDashboard;\nvar _c;\n$RefreshReg$(_c, \"BacktestingDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Typography", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "CircularProgress", "<PERSON><PERSON>", "Chip", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "LinearProgress", "PlayArrow", "Assessment", "TrendingUp", "TrendingDown", "Speed", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BacktestingDashboard", "_s", "strategies", "setStrategies", "selectedStrategy", "setSelectedStrategy", "backtestParams", "setBacktestParams", "symbol", "timeframe", "start_date", "end_date", "initial_capital", "margin", "commission", "strategy_config", "generate_files", "isRunning", "setIsRunning", "results", "setResults", "error", "setError", "progress", "setProgress", "fetchStrategies", "response", "get", "data", "length", "name", "console", "runBacktest", "progressInterval", "setInterval", "prev", "Math", "min", "post", "timeout", "success", "message", "_error$response", "_error$response$data", "detail", "clearInterval", "formatNumber", "value", "decimals", "toFixed", "formatDuration", "minutes", "hours", "floor", "mins", "sx", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "container", "spacing", "item", "xs", "md", "fullWidth", "onChange", "e", "target", "label", "map", "strategy", "description", "type", "parseInt", "InputLabelProps", "shrink", "parseFloat", "step", "size", "startIcon", "onClick", "disabled", "mt", "severity", "mb", "sm", "textAlign", "fontSize", "performance_summary", "total_return", "win_rate", "abs", "max_drawdown", "execution_time", "align", "total_trades", "sharpe_ratio", "custom_metrics", "profit_factor", "risk_reward_ratio", "avg_trade_duration_minutes", "total_pnl", "data_points", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/src/components/BacktestingDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  CircularProgress,\n  Alert,\n  Chip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  LinearProgress,\n  Divider,\n} from '@mui/material';\nimport {\n  PlayArrow,\n  Assessment,\n  TrendingUp,\n  TrendingDown,\n  Timeline,\n  Speed,\n} from '@mui/icons-material';\nimport axios from 'axios';\n\ninterface Strategy {\n  name: string;\n  description: string;\n  category: string;\n  risk_level: string;\n  validated: boolean;\n}\n\ninterface BacktestRequest {\n  symbol: string;\n  timeframe: number;\n  start_date: string;\n  end_date: string;\n  initial_capital: number;\n  margin: number;\n  commission: number;\n  strategy_config: Record<string, any>;\n  generate_files: boolean;\n}\n\ninterface BacktestResults {\n  execution_time: number;\n  symbol: string;\n  timeframe: number;\n  data_points: number;\n  performance_summary: {\n    total_trades: number;\n    win_rate: number;\n    total_return: number;\n    max_drawdown: number;\n    sharpe_ratio: number;\n  };\n  custom_metrics?: {\n    profit_factor: number;\n    risk_reward_ratio: number;\n    avg_trade_duration_minutes: number;\n    total_pnl: number;\n  };\n}\n\nconst BacktestingDashboard: React.FC = () => {\n  const [strategies, setStrategies] = useState<Strategy[]>([]);\n  const [selectedStrategy, setSelectedStrategy] = useState<string>('');\n  const [backtestParams, setBacktestParams] = useState<BacktestRequest>({\n    symbol: 'NIFTY50',\n    timeframe: 30,\n    start_date: '2024-01-01',\n    end_date: '2024-12-31',\n    initial_capital: 30000,\n    margin: 0.1,\n    commission: 0.0,\n    strategy_config: {},\n    generate_files: false,\n  });\n  const [isRunning, setIsRunning] = useState(false);\n  const [results, setResults] = useState<BacktestResults | null>(null);\n  const [error, setError] = useState<string | null>(null);\n  const [progress, setProgress] = useState(0);\n\n  useEffect(() => {\n    fetchStrategies();\n  }, []);\n\n  const fetchStrategies = async () => {\n    try {\n      const response = await axios.get('http://localhost:8000/api/v1/backtesting/strategies');\n      setStrategies(response.data.strategies);\n      if (response.data.strategies.length > 0) {\n        setSelectedStrategy(response.data.strategies[0].name);\n      }\n    } catch (error) {\n      console.error('Error fetching strategies:', error);\n      setError('Failed to fetch strategies');\n    }\n  };\n\n  const runBacktest = async () => {\n    if (!selectedStrategy) {\n      setError('Please select a strategy');\n      return;\n    }\n\n    setIsRunning(true);\n    setError(null);\n    setResults(null);\n    setProgress(0);\n\n    // Simulate progress updates\n    const progressInterval = setInterval(() => {\n      setProgress((prev) => Math.min(prev + 10, 90));\n    }, 500);\n\n    try {\n      const response = await axios.post(\n        'http://localhost:8000/api/v1/backtesting/reference/run',\n        backtestParams,\n        { timeout: 120000 }\n      );\n\n      if (response.data.success) {\n        setResults(response.data.results);\n        setProgress(100);\n      } else {\n        setError(response.data.message || 'Backtest failed');\n      }\n    } catch (error: any) {\n      console.error('Error running backtest:', error);\n      setError(error.response?.data?.detail || 'Failed to run backtest');\n    } finally {\n      clearInterval(progressInterval);\n      setIsRunning(false);\n      setProgress(0);\n    }\n  };\n\n  const formatNumber = (value: number, decimals: number = 2): string => {\n    return value.toFixed(decimals);\n  };\n\n  const formatDuration = (minutes: number): string => {\n    if (minutes < 60) {\n      return `${minutes.toFixed(0)}m`;\n    }\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins.toFixed(0)}m`;\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Typography variant=\"h4\" gutterBottom>\n        Reference Backtesting Framework\n      </Typography>\n      <Typography variant=\"subtitle1\" color=\"text.secondary\" gutterBottom>\n        High-performance backtesting with SimplePriceActionStrategy\n      </Typography>\n\n      <Grid container spacing={3}>\n        {/* Configuration Panel */}\n        <Grid item xs={12} md={4}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Backtest Configuration\n            </Typography>\n\n            <FormControl fullWidth margin=\"normal\">\n              <InputLabel>Strategy</InputLabel>\n              <Select\n                value={selectedStrategy}\n                onChange={(e) => setSelectedStrategy(e.target.value)}\n                label=\"Strategy\"\n              >\n                {strategies.map((strategy) => (\n                  <MenuItem key={strategy.name} value={strategy.name}>\n                    <Box>\n                      <Typography variant=\"body2\">{strategy.name}</Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {strategy.description}\n                      </Typography>\n                    </Box>\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n\n            <TextField\n              fullWidth\n              margin=\"normal\"\n              label=\"Symbol\"\n              value={backtestParams.symbol}\n              onChange={(e) =>\n                setBacktestParams({ ...backtestParams, symbol: e.target.value })\n              }\n            />\n\n            <TextField\n              fullWidth\n              margin=\"normal\"\n              label=\"Timeframe (minutes)\"\n              type=\"number\"\n              value={backtestParams.timeframe}\n              onChange={(e) =>\n                setBacktestParams({ ...backtestParams, timeframe: parseInt(e.target.value) })\n              }\n            />\n\n            <TextField\n              fullWidth\n              margin=\"normal\"\n              label=\"Start Date\"\n              type=\"date\"\n              value={backtestParams.start_date}\n              onChange={(e) =>\n                setBacktestParams({ ...backtestParams, start_date: e.target.value })\n              }\n              InputLabelProps={{ shrink: true }}\n            />\n\n            <TextField\n              fullWidth\n              margin=\"normal\"\n              label=\"End Date\"\n              type=\"date\"\n              value={backtestParams.end_date}\n              onChange={(e) =>\n                setBacktestParams({ ...backtestParams, end_date: e.target.value })\n              }\n              InputLabelProps={{ shrink: true }}\n            />\n\n            <TextField\n              fullWidth\n              margin=\"normal\"\n              label=\"Initial Capital\"\n              type=\"number\"\n              value={backtestParams.initial_capital}\n              onChange={(e) =>\n                setBacktestParams({ ...backtestParams, initial_capital: parseFloat(e.target.value) })\n              }\n            />\n\n            <TextField\n              fullWidth\n              margin=\"normal\"\n              label=\"Margin\"\n              type=\"number\"\n              step=\"0.01\"\n              value={backtestParams.margin}\n              onChange={(e) =>\n                setBacktestParams({ ...backtestParams, margin: parseFloat(e.target.value) })\n              }\n            />\n\n            <Button\n              fullWidth\n              variant=\"contained\"\n              size=\"large\"\n              startIcon={isRunning ? <CircularProgress size={20} /> : <PlayArrow />}\n              onClick={runBacktest}\n              disabled={isRunning}\n              sx={{ mt: 3 }}\n            >\n              {isRunning ? 'Running Backtest...' : 'Run Backtest'}\n            </Button>\n\n            {isRunning && (\n              <Box sx={{ mt: 2 }}>\n                <LinearProgress variant=\"determinate\" value={progress} />\n                <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\n                  Progress: {progress}%\n                </Typography>\n              </Box>\n            )}\n          </Paper>\n        </Grid>\n\n        {/* Results Panel */}\n        <Grid item xs={12} md={8}>\n          {error && (\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\n              {error}\n            </Alert>\n          )}\n\n          {results && (\n            <Grid container spacing={2}>\n              {/* Performance Summary Cards */}\n              <Grid item xs={12}>\n                <Paper sx={{ p: 3 }}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Performance Summary\n                  </Typography>\n                  <Grid container spacing={2}>\n                    <Grid item xs={6} sm={3}>\n                      <Card>\n                        <CardContent sx={{ textAlign: 'center' }}>\n                          <TrendingUp color=\"primary\" sx={{ fontSize: 40 }} />\n                          <Typography variant=\"h6\">\n                            {formatNumber(results.performance_summary.total_return)}%\n                          </Typography>\n                          <Typography variant=\"caption\">Total Return</Typography>\n                        </CardContent>\n                      </Card>\n                    </Grid>\n                    <Grid item xs={6} sm={3}>\n                      <Card>\n                        <CardContent sx={{ textAlign: 'center' }}>\n                          <Assessment color=\"secondary\" sx={{ fontSize: 40 }} />\n                          <Typography variant=\"h6\">\n                            {formatNumber(results.performance_summary.win_rate)}%\n                          </Typography>\n                          <Typography variant=\"caption\">Win Rate</Typography>\n                        </CardContent>\n                      </Card>\n                    </Grid>\n                    <Grid item xs={6} sm={3}>\n                      <Card>\n                        <CardContent sx={{ textAlign: 'center' }}>\n                          <TrendingDown color=\"error\" sx={{ fontSize: 40 }} />\n                          <Typography variant=\"h6\">\n                            {formatNumber(Math.abs(results.performance_summary.max_drawdown))}%\n                          </Typography>\n                          <Typography variant=\"caption\">Max Drawdown</Typography>\n                        </CardContent>\n                      </Card>\n                    </Grid>\n                    <Grid item xs={6} sm={3}>\n                      <Card>\n                        <CardContent sx={{ textAlign: 'center' }}>\n                          <Speed color=\"info\" sx={{ fontSize: 40 }} />\n                          <Typography variant=\"h6\">\n                            {formatNumber(results.execution_time)}s\n                          </Typography>\n                          <Typography variant=\"caption\">Execution Time</Typography>\n                        </CardContent>\n                      </Card>\n                    </Grid>\n                  </Grid>\n                </Paper>\n              </Grid>\n\n              {/* Detailed Metrics Table */}\n              <Grid item xs={12}>\n                <Paper sx={{ p: 3 }}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Detailed Metrics\n                  </Typography>\n                  <TableContainer>\n                    <Table size=\"small\">\n                      <TableHead>\n                        <TableRow>\n                          <TableCell>Metric</TableCell>\n                          <TableCell align=\"right\">Value</TableCell>\n                        </TableRow>\n                      </TableHead>\n                      <TableBody>\n                        <TableRow>\n                          <TableCell>Total Trades</TableCell>\n                          <TableCell align=\"right\">{results.performance_summary.total_trades}</TableCell>\n                        </TableRow>\n                        <TableRow>\n                          <TableCell>Win Rate</TableCell>\n                          <TableCell align=\"right\">{formatNumber(results.performance_summary.win_rate)}%</TableCell>\n                        </TableRow>\n                        <TableRow>\n                          <TableCell>Total Return</TableCell>\n                          <TableCell align=\"right\">{formatNumber(results.performance_summary.total_return)}%</TableCell>\n                        </TableRow>\n                        <TableRow>\n                          <TableCell>Max Drawdown</TableCell>\n                          <TableCell align=\"right\">{formatNumber(Math.abs(results.performance_summary.max_drawdown))}%</TableCell>\n                        </TableRow>\n                        <TableRow>\n                          <TableCell>Sharpe Ratio</TableCell>\n                          <TableCell align=\"right\">{formatNumber(results.performance_summary.sharpe_ratio)}</TableCell>\n                        </TableRow>\n                        {results.custom_metrics && (\n                          <>\n                            <TableRow>\n                              <TableCell>Profit Factor</TableCell>\n                              <TableCell align=\"right\">{formatNumber(results.custom_metrics.profit_factor)}</TableCell>\n                            </TableRow>\n                            <TableRow>\n                              <TableCell>Risk-to-Reward Ratio</TableCell>\n                              <TableCell align=\"right\">{formatNumber(results.custom_metrics.risk_reward_ratio)}</TableCell>\n                            </TableRow>\n                            <TableRow>\n                              <TableCell>Avg Trade Duration</TableCell>\n                              <TableCell align=\"right\">{formatDuration(results.custom_metrics.avg_trade_duration_minutes)}</TableCell>\n                            </TableRow>\n                            <TableRow>\n                              <TableCell>Total P&L</TableCell>\n                              <TableCell align=\"right\">₹{formatNumber(results.custom_metrics.total_pnl)}</TableCell>\n                            </TableRow>\n                          </>\n                        )}\n                      </TableBody>\n                    </Table>\n                  </TableContainer>\n                </Paper>\n              </Grid>\n\n              {/* Execution Info */}\n              <Grid item xs={12}>\n                <Paper sx={{ p: 3 }}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Execution Information\n                  </Typography>\n                  <Grid container spacing={2}>\n                    <Grid item xs={12} sm={6}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Symbol: <strong>{results.symbol}</strong>\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Timeframe: <strong>{results.timeframe} minutes</strong>\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Data Points: <strong>{results.data_points.toLocaleString()}</strong>\n                      </Typography>\n                    </Grid>\n                    <Grid item xs={12} sm={6}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Execution Time: <strong>{formatNumber(results.execution_time)} seconds</strong>\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Processing Rate: <strong>{formatNumber(results.data_points / results.execution_time)} bars/sec</strong>\n                      </Typography>\n                      <Chip\n                        label=\"Reference Framework\"\n                        color=\"primary\"\n                        size=\"small\"\n                        sx={{ mt: 1 }}\n                      />\n                    </Grid>\n                  </Grid>\n                </Paper>\n              </Grid>\n            </Grid>\n          )}\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default BacktestingDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,gBAAgB,EAChBC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,cAAc,QAET,eAAe;AACtB,SACEC,SAAS,EACTC,UAAU,EACVC,UAAU,EACVC,YAAY,EAEZC,KAAK,QACA,qBAAqB;AAC5B,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA0C1B,MAAMC,oBAA8B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3C,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAACsC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAkB;IACpE0C,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,YAAY;IACtBC,eAAe,EAAE,KAAK;IACtBC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,GAAG;IACfC,eAAe,EAAE,CAAC,CAAC;IACnBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAyB,IAAI,CAAC;EACpE,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC;EAE3CC,SAAS,CAAC,MAAM;IACd0D,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM/B,KAAK,CAACgC,GAAG,CAAC,qDAAqD,CAAC;MACvFxB,aAAa,CAACuB,QAAQ,CAACE,IAAI,CAAC1B,UAAU,CAAC;MACvC,IAAIwB,QAAQ,CAACE,IAAI,CAAC1B,UAAU,CAAC2B,MAAM,GAAG,CAAC,EAAE;QACvCxB,mBAAmB,CAACqB,QAAQ,CAACE,IAAI,CAAC1B,UAAU,CAAC,CAAC,CAAC,CAAC4B,IAAI,CAAC;MACvD;IACF,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDC,QAAQ,CAAC,4BAA4B,CAAC;IACxC;EACF,CAAC;EAED,MAAMU,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAC5B,gBAAgB,EAAE;MACrBkB,QAAQ,CAAC,0BAA0B,CAAC;MACpC;IACF;IAEAJ,YAAY,CAAC,IAAI,CAAC;IAClBI,QAAQ,CAAC,IAAI,CAAC;IACdF,UAAU,CAAC,IAAI,CAAC;IAChBI,WAAW,CAAC,CAAC,CAAC;;IAEd;IACA,MAAMS,gBAAgB,GAAGC,WAAW,CAAC,MAAM;MACzCV,WAAW,CAAEW,IAAI,IAAKC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;IAChD,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAM/B,KAAK,CAAC2C,IAAI,CAC/B,wDAAwD,EACxDhC,cAAc,EACd;QAAEiC,OAAO,EAAE;MAAO,CACpB,CAAC;MAED,IAAIb,QAAQ,CAACE,IAAI,CAACY,OAAO,EAAE;QACzBpB,UAAU,CAACM,QAAQ,CAACE,IAAI,CAACT,OAAO,CAAC;QACjCK,WAAW,CAAC,GAAG,CAAC;MAClB,CAAC,MAAM;QACLF,QAAQ,CAACI,QAAQ,CAACE,IAAI,CAACa,OAAO,IAAI,iBAAiB,CAAC;MACtD;IACF,CAAC,CAAC,OAAOpB,KAAU,EAAE;MAAA,IAAAqB,eAAA,EAAAC,oBAAA;MACnBZ,OAAO,CAACV,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,EAAAoB,eAAA,GAAArB,KAAK,CAACK,QAAQ,cAAAgB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBd,IAAI,cAAAe,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,wBAAwB,CAAC;IACpE,CAAC,SAAS;MACRC,aAAa,CAACZ,gBAAgB,CAAC;MAC/Bf,YAAY,CAAC,KAAK,CAAC;MACnBM,WAAW,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAMsB,YAAY,GAAGA,CAACC,KAAa,EAAEC,QAAgB,GAAG,CAAC,KAAa;IACpE,OAAOD,KAAK,CAACE,OAAO,CAACD,QAAQ,CAAC;EAChC,CAAC;EAED,MAAME,cAAc,GAAIC,OAAe,IAAa;IAClD,IAAIA,OAAO,GAAG,EAAE,EAAE;MAChB,OAAO,GAAGA,OAAO,CAACF,OAAO,CAAC,CAAC,CAAC,GAAG;IACjC;IACA,MAAMG,KAAK,GAAGhB,IAAI,CAACiB,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMG,IAAI,GAAGH,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,KAAK,KAAKE,IAAI,CAACL,OAAO,CAAC,CAAC,CAAC,GAAG;EACxC,CAAC;EAED,oBACEpD,OAAA,CAAC7B,GAAG;IAACuF,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAChB5D,OAAA,CAAC3B,UAAU;MAACwF,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACblE,OAAA,CAAC3B,UAAU;MAACwF,OAAO,EAAC,WAAW;MAACM,KAAK,EAAC,gBAAgB;MAACL,YAAY;MAAAF,QAAA,EAAC;IAEpE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEblE,OAAA,CAACzB,IAAI;MAAC6F,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAT,QAAA,gBAEzB5D,OAAA,CAACzB,IAAI;QAAC+F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eACvB5D,OAAA,CAAC5B,KAAK;UAACsF,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAC,QAAA,gBAClB5D,OAAA,CAAC3B,UAAU;YAACwF,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEblE,OAAA,CAACtB,WAAW;YAAC+F,SAAS;YAACzD,MAAM,EAAC,QAAQ;YAAA4C,QAAA,gBACpC5D,OAAA,CAACrB,UAAU;cAAAiF,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACjClE,OAAA,CAACpB,MAAM;cACLsE,KAAK,EAAE3C,gBAAiB;cACxBmE,QAAQ,EAAGC,CAAC,IAAKnE,mBAAmB,CAACmE,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE;cACrD2B,KAAK,EAAC,UAAU;cAAAjB,QAAA,EAEfvD,UAAU,CAACyE,GAAG,CAAEC,QAAQ,iBACvB/E,OAAA,CAACnB,QAAQ;gBAAqBqE,KAAK,EAAE6B,QAAQ,CAAC9C,IAAK;gBAAA2B,QAAA,eACjD5D,OAAA,CAAC7B,GAAG;kBAAAyF,QAAA,gBACF5D,OAAA,CAAC3B,UAAU;oBAACwF,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAEmB,QAAQ,CAAC9C;kBAAI;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,eACxDlE,OAAA,CAAC3B,UAAU;oBAACwF,OAAO,EAAC,SAAS;oBAACM,KAAK,EAAC,gBAAgB;oBAAAP,QAAA,EACjDmB,QAAQ,CAACC;kBAAW;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GANOa,QAAQ,CAAC9C,IAAI;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOlB,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEdlE,OAAA,CAAClB,SAAS;YACR2F,SAAS;YACTzD,MAAM,EAAC,QAAQ;YACf6D,KAAK,EAAC,QAAQ;YACd3B,KAAK,EAAEzC,cAAc,CAACE,MAAO;YAC7B+D,QAAQ,EAAGC,CAAC,IACVjE,iBAAiB,CAAC;cAAE,GAAGD,cAAc;cAAEE,MAAM,EAAEgE,CAAC,CAACC,MAAM,CAAC1B;YAAM,CAAC;UAChE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFlE,OAAA,CAAClB,SAAS;YACR2F,SAAS;YACTzD,MAAM,EAAC,QAAQ;YACf6D,KAAK,EAAC,qBAAqB;YAC3BI,IAAI,EAAC,QAAQ;YACb/B,KAAK,EAAEzC,cAAc,CAACG,SAAU;YAChC8D,QAAQ,EAAGC,CAAC,IACVjE,iBAAiB,CAAC;cAAE,GAAGD,cAAc;cAAEG,SAAS,EAAEsE,QAAQ,CAACP,CAAC,CAACC,MAAM,CAAC1B,KAAK;YAAE,CAAC;UAC7E;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFlE,OAAA,CAAClB,SAAS;YACR2F,SAAS;YACTzD,MAAM,EAAC,QAAQ;YACf6D,KAAK,EAAC,YAAY;YAClBI,IAAI,EAAC,MAAM;YACX/B,KAAK,EAAEzC,cAAc,CAACI,UAAW;YACjC6D,QAAQ,EAAGC,CAAC,IACVjE,iBAAiB,CAAC;cAAE,GAAGD,cAAc;cAAEI,UAAU,EAAE8D,CAAC,CAACC,MAAM,CAAC1B;YAAM,CAAC,CACpE;YACDiC,eAAe,EAAE;cAAEC,MAAM,EAAE;YAAK;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAEFlE,OAAA,CAAClB,SAAS;YACR2F,SAAS;YACTzD,MAAM,EAAC,QAAQ;YACf6D,KAAK,EAAC,UAAU;YAChBI,IAAI,EAAC,MAAM;YACX/B,KAAK,EAAEzC,cAAc,CAACK,QAAS;YAC/B4D,QAAQ,EAAGC,CAAC,IACVjE,iBAAiB,CAAC;cAAE,GAAGD,cAAc;cAAEK,QAAQ,EAAE6D,CAAC,CAACC,MAAM,CAAC1B;YAAM,CAAC,CAClE;YACDiC,eAAe,EAAE;cAAEC,MAAM,EAAE;YAAK;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAEFlE,OAAA,CAAClB,SAAS;YACR2F,SAAS;YACTzD,MAAM,EAAC,QAAQ;YACf6D,KAAK,EAAC,iBAAiB;YACvBI,IAAI,EAAC,QAAQ;YACb/B,KAAK,EAAEzC,cAAc,CAACM,eAAgB;YACtC2D,QAAQ,EAAGC,CAAC,IACVjE,iBAAiB,CAAC;cAAE,GAAGD,cAAc;cAAEM,eAAe,EAAEsE,UAAU,CAACV,CAAC,CAACC,MAAM,CAAC1B,KAAK;YAAE,CAAC;UACrF;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFlE,OAAA,CAAClB,SAAS;YACR2F,SAAS;YACTzD,MAAM,EAAC,QAAQ;YACf6D,KAAK,EAAC,QAAQ;YACdI,IAAI,EAAC,QAAQ;YACbK,IAAI,EAAC,MAAM;YACXpC,KAAK,EAAEzC,cAAc,CAACO,MAAO;YAC7B0D,QAAQ,EAAGC,CAAC,IACVjE,iBAAiB,CAAC;cAAE,GAAGD,cAAc;cAAEO,MAAM,EAAEqE,UAAU,CAACV,CAAC,CAACC,MAAM,CAAC1B,KAAK;YAAE,CAAC;UAC5E;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFlE,OAAA,CAAC1B,MAAM;YACLmG,SAAS;YACTZ,OAAO,EAAC,WAAW;YACnB0B,IAAI,EAAC,OAAO;YACZC,SAAS,EAAEpE,SAAS,gBAAGpB,OAAA,CAACjB,gBAAgB;cAACwG,IAAI,EAAE;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlE,OAAA,CAACP,SAAS;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtEuB,OAAO,EAAEtD,WAAY;YACrBuD,QAAQ,EAAEtE,SAAU;YACpBsC,EAAE,EAAE;cAAEiC,EAAE,EAAE;YAAE,CAAE;YAAA/B,QAAA,EAEbxC,SAAS,GAAG,qBAAqB,GAAG;UAAc;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,EAER9C,SAAS,iBACRpB,OAAA,CAAC7B,GAAG;YAACuF,EAAE,EAAE;cAAEiC,EAAE,EAAE;YAAE,CAAE;YAAA/B,QAAA,gBACjB5D,OAAA,CAACR,cAAc;cAACqE,OAAO,EAAC,aAAa;cAACX,KAAK,EAAExB;YAAS;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzDlE,OAAA,CAAC3B,UAAU;cAACwF,OAAO,EAAC,SAAS;cAACM,KAAK,EAAC,gBAAgB;cAACT,EAAE,EAAE;gBAAEiC,EAAE,EAAE;cAAE,CAAE;cAAA/B,QAAA,GAAC,YACxD,EAAClC,QAAQ,EAAC,GACtB;YAAA;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGPlE,OAAA,CAACzB,IAAI;QAAC+F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,GACtBpC,KAAK,iBACJxB,OAAA,CAAChB,KAAK;UAAC4G,QAAQ,EAAC,OAAO;UAAClC,EAAE,EAAE;YAAEmC,EAAE,EAAE;UAAE,CAAE;UAAAjC,QAAA,EACnCpC;QAAK;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAEA5C,OAAO,iBACNtB,OAAA,CAACzB,IAAI;UAAC6F,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAT,QAAA,gBAEzB5D,OAAA,CAACzB,IAAI;YAAC+F,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAX,QAAA,eAChB5D,OAAA,CAAC5B,KAAK;cAACsF,EAAE,EAAE;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAAAC,QAAA,gBAClB5D,OAAA,CAAC3B,UAAU;gBAACwF,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAF,QAAA,EAAC;cAEtC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblE,OAAA,CAACzB,IAAI;gBAAC6F,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAT,QAAA,gBACzB5D,OAAA,CAACzB,IAAI;kBAAC+F,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAACuB,EAAE,EAAE,CAAE;kBAAAlC,QAAA,eACtB5D,OAAA,CAACxB,IAAI;oBAAAoF,QAAA,eACH5D,OAAA,CAACvB,WAAW;sBAACiF,EAAE,EAAE;wBAAEqC,SAAS,EAAE;sBAAS,CAAE;sBAAAnC,QAAA,gBACvC5D,OAAA,CAACL,UAAU;wBAACwE,KAAK,EAAC,SAAS;wBAACT,EAAE,EAAE;0BAAEsC,QAAQ,EAAE;wBAAG;sBAAE;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACpDlE,OAAA,CAAC3B,UAAU;wBAACwF,OAAO,EAAC,IAAI;wBAAAD,QAAA,GACrBX,YAAY,CAAC3B,OAAO,CAAC2E,mBAAmB,CAACC,YAAY,CAAC,EAAC,GAC1D;sBAAA;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACblE,OAAA,CAAC3B,UAAU;wBAACwF,OAAO,EAAC,SAAS;wBAAAD,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACPlE,OAAA,CAACzB,IAAI;kBAAC+F,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAACuB,EAAE,EAAE,CAAE;kBAAAlC,QAAA,eACtB5D,OAAA,CAACxB,IAAI;oBAAAoF,QAAA,eACH5D,OAAA,CAACvB,WAAW;sBAACiF,EAAE,EAAE;wBAAEqC,SAAS,EAAE;sBAAS,CAAE;sBAAAnC,QAAA,gBACvC5D,OAAA,CAACN,UAAU;wBAACyE,KAAK,EAAC,WAAW;wBAACT,EAAE,EAAE;0BAAEsC,QAAQ,EAAE;wBAAG;sBAAE;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACtDlE,OAAA,CAAC3B,UAAU;wBAACwF,OAAO,EAAC,IAAI;wBAAAD,QAAA,GACrBX,YAAY,CAAC3B,OAAO,CAAC2E,mBAAmB,CAACE,QAAQ,CAAC,EAAC,GACtD;sBAAA;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACblE,OAAA,CAAC3B,UAAU;wBAACwF,OAAO,EAAC,SAAS;wBAAAD,QAAA,EAAC;sBAAQ;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACPlE,OAAA,CAACzB,IAAI;kBAAC+F,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAACuB,EAAE,EAAE,CAAE;kBAAAlC,QAAA,eACtB5D,OAAA,CAACxB,IAAI;oBAAAoF,QAAA,eACH5D,OAAA,CAACvB,WAAW;sBAACiF,EAAE,EAAE;wBAAEqC,SAAS,EAAE;sBAAS,CAAE;sBAAAnC,QAAA,gBACvC5D,OAAA,CAACJ,YAAY;wBAACuE,KAAK,EAAC,OAAO;wBAACT,EAAE,EAAE;0BAAEsC,QAAQ,EAAE;wBAAG;sBAAE;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACpDlE,OAAA,CAAC3B,UAAU;wBAACwF,OAAO,EAAC,IAAI;wBAAAD,QAAA,GACrBX,YAAY,CAACV,IAAI,CAAC6D,GAAG,CAAC9E,OAAO,CAAC2E,mBAAmB,CAACI,YAAY,CAAC,CAAC,EAAC,GACpE;sBAAA;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACblE,OAAA,CAAC3B,UAAU;wBAACwF,OAAO,EAAC,SAAS;wBAAAD,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACPlE,OAAA,CAACzB,IAAI;kBAAC+F,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAACuB,EAAE,EAAE,CAAE;kBAAAlC,QAAA,eACtB5D,OAAA,CAACxB,IAAI;oBAAAoF,QAAA,eACH5D,OAAA,CAACvB,WAAW;sBAACiF,EAAE,EAAE;wBAAEqC,SAAS,EAAE;sBAAS,CAAE;sBAAAnC,QAAA,gBACvC5D,OAAA,CAACH,KAAK;wBAACsE,KAAK,EAAC,MAAM;wBAACT,EAAE,EAAE;0BAAEsC,QAAQ,EAAE;wBAAG;sBAAE;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC5ClE,OAAA,CAAC3B,UAAU;wBAACwF,OAAO,EAAC,IAAI;wBAAAD,QAAA,GACrBX,YAAY,CAAC3B,OAAO,CAACgF,cAAc,CAAC,EAAC,GACxC;sBAAA;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACblE,OAAA,CAAC3B,UAAU;wBAACwF,OAAO,EAAC,SAAS;wBAAAD,QAAA,EAAC;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGPlE,OAAA,CAACzB,IAAI;YAAC+F,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAX,QAAA,eAChB5D,OAAA,CAAC5B,KAAK;cAACsF,EAAE,EAAE;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAAAC,QAAA,gBAClB5D,OAAA,CAAC3B,UAAU;gBAACwF,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAF,QAAA,EAAC;cAEtC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblE,OAAA,CAACX,cAAc;gBAAAuE,QAAA,eACb5D,OAAA,CAACd,KAAK;kBAACqG,IAAI,EAAC,OAAO;kBAAA3B,QAAA,gBACjB5D,OAAA,CAACV,SAAS;oBAAAsE,QAAA,eACR5D,OAAA,CAACT,QAAQ;sBAAAqE,QAAA,gBACP5D,OAAA,CAACZ,SAAS;wBAAAwE,QAAA,EAAC;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAC7BlE,OAAA,CAACZ,SAAS;wBAACmH,KAAK,EAAC,OAAO;wBAAA3C,QAAA,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACZlE,OAAA,CAACb,SAAS;oBAAAyE,QAAA,gBACR5D,OAAA,CAACT,QAAQ;sBAAAqE,QAAA,gBACP5D,OAAA,CAACZ,SAAS;wBAAAwE,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACnClE,OAAA,CAACZ,SAAS;wBAACmH,KAAK,EAAC,OAAO;wBAAA3C,QAAA,EAAEtC,OAAO,CAAC2E,mBAAmB,CAACO;sBAAY;wBAAAzC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC,eACXlE,OAAA,CAACT,QAAQ;sBAAAqE,QAAA,gBACP5D,OAAA,CAACZ,SAAS;wBAAAwE,QAAA,EAAC;sBAAQ;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAC/BlE,OAAA,CAACZ,SAAS;wBAACmH,KAAK,EAAC,OAAO;wBAAA3C,QAAA,GAAEX,YAAY,CAAC3B,OAAO,CAAC2E,mBAAmB,CAACE,QAAQ,CAAC,EAAC,GAAC;sBAAA;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF,CAAC,eACXlE,OAAA,CAACT,QAAQ;sBAAAqE,QAAA,gBACP5D,OAAA,CAACZ,SAAS;wBAAAwE,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACnClE,OAAA,CAACZ,SAAS;wBAACmH,KAAK,EAAC,OAAO;wBAAA3C,QAAA,GAAEX,YAAY,CAAC3B,OAAO,CAAC2E,mBAAmB,CAACC,YAAY,CAAC,EAAC,GAAC;sBAAA;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF,CAAC,eACXlE,OAAA,CAACT,QAAQ;sBAAAqE,QAAA,gBACP5D,OAAA,CAACZ,SAAS;wBAAAwE,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACnClE,OAAA,CAACZ,SAAS;wBAACmH,KAAK,EAAC,OAAO;wBAAA3C,QAAA,GAAEX,YAAY,CAACV,IAAI,CAAC6D,GAAG,CAAC9E,OAAO,CAAC2E,mBAAmB,CAACI,YAAY,CAAC,CAAC,EAAC,GAAC;sBAAA;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChG,CAAC,eACXlE,OAAA,CAACT,QAAQ;sBAAAqE,QAAA,gBACP5D,OAAA,CAACZ,SAAS;wBAAAwE,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACnClE,OAAA,CAACZ,SAAS;wBAACmH,KAAK,EAAC,OAAO;wBAAA3C,QAAA,EAAEX,YAAY,CAAC3B,OAAO,CAAC2E,mBAAmB,CAACQ,YAAY;sBAAC;wBAAA1C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF,CAAC,EACV5C,OAAO,CAACoF,cAAc,iBACrB1G,OAAA,CAAAE,SAAA;sBAAA0D,QAAA,gBACE5D,OAAA,CAACT,QAAQ;wBAAAqE,QAAA,gBACP5D,OAAA,CAACZ,SAAS;0BAAAwE,QAAA,EAAC;wBAAa;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAW,CAAC,eACpClE,OAAA,CAACZ,SAAS;0BAACmH,KAAK,EAAC,OAAO;0BAAA3C,QAAA,EAAEX,YAAY,CAAC3B,OAAO,CAACoF,cAAc,CAACC,aAAa;wBAAC;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjF,CAAC,eACXlE,OAAA,CAACT,QAAQ;wBAAAqE,QAAA,gBACP5D,OAAA,CAACZ,SAAS;0BAAAwE,QAAA,EAAC;wBAAoB;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAW,CAAC,eAC3ClE,OAAA,CAACZ,SAAS;0BAACmH,KAAK,EAAC,OAAO;0BAAA3C,QAAA,EAAEX,YAAY,CAAC3B,OAAO,CAACoF,cAAc,CAACE,iBAAiB;wBAAC;0BAAA7C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrF,CAAC,eACXlE,OAAA,CAACT,QAAQ;wBAAAqE,QAAA,gBACP5D,OAAA,CAACZ,SAAS;0BAAAwE,QAAA,EAAC;wBAAkB;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAW,CAAC,eACzClE,OAAA,CAACZ,SAAS;0BAACmH,KAAK,EAAC,OAAO;0BAAA3C,QAAA,EAAEP,cAAc,CAAC/B,OAAO,CAACoF,cAAc,CAACG,0BAA0B;wBAAC;0BAAA9C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChG,CAAC,eACXlE,OAAA,CAACT,QAAQ;wBAAAqE,QAAA,gBACP5D,OAAA,CAACZ,SAAS;0BAAAwE,QAAA,EAAC;wBAAS;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAW,CAAC,eAChClE,OAAA,CAACZ,SAAS;0BAACmH,KAAK,EAAC,OAAO;0BAAA3C,QAAA,GAAC,QAAC,EAACX,YAAY,CAAC3B,OAAO,CAACoF,cAAc,CAACI,SAAS,CAAC;wBAAA;0BAAA/C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9E,CAAC;oBAAA,eACX,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGPlE,OAAA,CAACzB,IAAI;YAAC+F,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAX,QAAA,eAChB5D,OAAA,CAAC5B,KAAK;cAACsF,EAAE,EAAE;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAAAC,QAAA,gBAClB5D,OAAA,CAAC3B,UAAU;gBAACwF,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAF,QAAA,EAAC;cAEtC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblE,OAAA,CAACzB,IAAI;gBAAC6F,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAT,QAAA,gBACzB5D,OAAA,CAACzB,IAAI;kBAAC+F,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACuB,EAAE,EAAE,CAAE;kBAAAlC,QAAA,gBACvB5D,OAAA,CAAC3B,UAAU;oBAACwF,OAAO,EAAC,OAAO;oBAACM,KAAK,EAAC,gBAAgB;oBAAAP,QAAA,GAAC,UACzC,eAAA5D,OAAA;sBAAA4D,QAAA,EAAStC,OAAO,CAACX;oBAAM;sBAAAoD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACblE,OAAA,CAAC3B,UAAU;oBAACwF,OAAO,EAAC,OAAO;oBAACM,KAAK,EAAC,gBAAgB;oBAAAP,QAAA,GAAC,aACtC,eAAA5D,OAAA;sBAAA4D,QAAA,GAAStC,OAAO,CAACV,SAAS,EAAC,UAAQ;oBAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACblE,OAAA,CAAC3B,UAAU;oBAACwF,OAAO,EAAC,OAAO;oBAACM,KAAK,EAAC,gBAAgB;oBAAAP,QAAA,GAAC,eACpC,eAAA5D,OAAA;sBAAA4D,QAAA,EAAStC,OAAO,CAACyF,WAAW,CAACC,cAAc,CAAC;oBAAC;sBAAAjD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACPlE,OAAA,CAACzB,IAAI;kBAAC+F,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACuB,EAAE,EAAE,CAAE;kBAAAlC,QAAA,gBACvB5D,OAAA,CAAC3B,UAAU;oBAACwF,OAAO,EAAC,OAAO;oBAACM,KAAK,EAAC,gBAAgB;oBAAAP,QAAA,GAAC,kBACjC,eAAA5D,OAAA;sBAAA4D,QAAA,GAASX,YAAY,CAAC3B,OAAO,CAACgF,cAAc,CAAC,EAAC,UAAQ;oBAAA;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CAAC,eACblE,OAAA,CAAC3B,UAAU;oBAACwF,OAAO,EAAC,OAAO;oBAACM,KAAK,EAAC,gBAAgB;oBAAAP,QAAA,GAAC,mBAChC,eAAA5D,OAAA;sBAAA4D,QAAA,GAASX,YAAY,CAAC3B,OAAO,CAACyF,WAAW,GAAGzF,OAAO,CAACgF,cAAc,CAAC,EAAC,WAAS;oBAAA;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7F,CAAC,eACblE,OAAA,CAACf,IAAI;oBACH4F,KAAK,EAAC,qBAAqB;oBAC3BV,KAAK,EAAC,SAAS;oBACfoB,IAAI,EAAC,OAAO;oBACZ7B,EAAE,EAAE;sBAAEiC,EAAE,EAAE;oBAAE;kBAAE;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC9D,EAAA,CA/XID,oBAA8B;AAAA8G,EAAA,GAA9B9G,oBAA8B;AAiYpC,eAAeA,oBAAoB;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}