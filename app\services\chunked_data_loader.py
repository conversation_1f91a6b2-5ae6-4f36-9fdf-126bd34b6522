"""
Chunked Data Loading Strategy for Large Historical Datasets.
Implements intelligent chunking, progress persistence, and error recovery.
"""

import logging
import json
import pickle
from typing import Dict, List, Optional, Tuple, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import time
import hashlib

from app.core.rate_limiter import AdvancedRateLimiter, RateLimitConfig, RateLimitStrategy, RateLimitedExecutor
from app.integrations.fyers.fyers_client import FyersClient, OHLCData
from app.services.symbol_mapping_service import SymbolMappingService
from app.database.repositories.ohlcv_repository import OHLCVRepository
from app.database.models import Symbol
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

@dataclass
class ChunkInfo:
    """Information about a data chunk."""
    chunk_id: str
    symbol: str
    start_date: datetime
    end_date: datetime
    status: str  # 'pending', 'loading', 'completed', 'failed'
    records_loaded: int = 0
    error_message: Optional[str] = None
    attempts: int = 0
    last_attempt: Optional[datetime] = None
    completion_time: Optional[datetime] = None

@dataclass
class LoadingSession:
    """Information about a loading session."""
    session_id: str
    symbol: str
    total_start_date: datetime
    total_end_date: datetime
    chunk_size_days: int
    chunks: List[ChunkInfo]
    created_at: datetime
    updated_at: datetime
    status: str  # 'active', 'completed', 'paused', 'failed'
    total_records_loaded: int = 0

class ChunkedDataLoader:
    """Chunked data loader for large historical datasets."""
    
    def __init__(
        self, 
        db: Session,
        chunk_size_days: int = 30,
        max_retries_per_chunk: int = 3,
        progress_dir: str = "progress"
    ):
        """
        Initialize the chunked data loader.
        
        Args:
            db: Database session
            chunk_size_days: Size of each chunk in days
            max_retries_per_chunk: Maximum retries per chunk
            progress_dir: Directory to store progress files
        """
        self.db = db
        self.chunk_size_days = chunk_size_days
        self.max_retries_per_chunk = max_retries_per_chunk
        self.progress_dir = Path(progress_dir)
        self.progress_dir.mkdir(exist_ok=True)
        
        # Initialize services
        self.fyers_client = FyersClient()
        self.symbol_mapping_service = SymbolMappingService()
        self.ohlcv_repo = OHLCVRepository(db)
        
        # Initialize rate limiter with adaptive strategy for long operations
        rate_config = RateLimitConfig(
            strategy=RateLimitStrategy.ADAPTIVE,
            base_delay=0.5,  # Start with 0.5s delay for historical data
            max_delay=30.0,  # Max 30s delay
            max_retries=5,
            backoff_multiplier=1.5,
            jitter_factor=0.2,
            success_threshold=0.85,
            adaptation_window=50,
            failure_threshold=5,
            recovery_timeout=300.0
        )
        
        self.rate_limiter = AdvancedRateLimiter(rate_config)
        self.rate_limited_executor = RateLimitedExecutor(self.rate_limiter)
        
        # Callbacks
        self.progress_callback: Optional[Callable[[LoadingSession], None]] = None
        self.chunk_callback: Optional[Callable[[ChunkInfo], None]] = None
        
    def set_progress_callback(self, callback: Callable[[LoadingSession], None]):
        """Set callback for session progress updates."""
        self.progress_callback = callback
    
    def set_chunk_callback(self, callback: Callable[[ChunkInfo], None]):
        """Set callback for chunk progress updates."""
        self.chunk_callback = callback
    
    def create_loading_session(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        chunk_size_days: Optional[int] = None
    ) -> LoadingSession:
        """
        Create a new loading session with chunks.
        
        Args:
            symbol: Symbol to load
            start_date: Start date
            end_date: End date
            chunk_size_days: Override default chunk size
            
        Returns:
            LoadingSession object
        """
        chunk_size = chunk_size_days or self.chunk_size_days
        session_id = self._generate_session_id(symbol, start_date, end_date)
        
        # Create chunks
        chunks = []
        current_date = start_date
        chunk_index = 0
        
        while current_date < end_date:
            chunk_end = min(current_date + timedelta(days=chunk_size), end_date)
            
            chunk_id = f"{session_id}_chunk_{chunk_index:04d}"
            chunk = ChunkInfo(
                chunk_id=chunk_id,
                symbol=symbol,
                start_date=current_date,
                end_date=chunk_end,
                status='pending'
            )
            chunks.append(chunk)
            
            current_date = chunk_end
            chunk_index += 1
        
        session = LoadingSession(
            session_id=session_id,
            symbol=symbol,
            total_start_date=start_date,
            total_end_date=end_date,
            chunk_size_days=chunk_size,
            chunks=chunks,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            status='active'
        )
        
        # Save session
        self._save_session(session)
        
        logger.info(f"Created loading session {session_id} with {len(chunks)} chunks")
        logger.info(f"Date range: {start_date.date()} to {end_date.date()}")
        logger.info(f"Chunk size: {chunk_size} days")
        
        return session
    
    def load_session_data(self, session: LoadingSession) -> bool:
        """
        Load data for an entire session.
        
        Args:
            session: Loading session
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Starting data loading for session {session.session_id}")
            
            # Authenticate Fyers client
            if not self.fyers_client.authenticate():
                logger.error("Failed to authenticate with Fyers API")
                return False
            
            # Get symbol mapping
            mapping = self.symbol_mapping_service.get_symbol_mapping(session.symbol)
            if not mapping:
                logger.error(f"No symbol mapping found for {session.symbol}")
                return False
            
            fyers_symbol = mapping.fyers_symbol
            logger.info(f"Using Fyers symbol: {fyers_symbol}")
            
            # Process chunks
            completed_chunks = 0
            failed_chunks = 0
            
            for chunk in session.chunks:
                if chunk.status == 'completed':
                    completed_chunks += 1
                    continue
                
                success = self._load_chunk_data(chunk, fyers_symbol)
                
                if success:
                    completed_chunks += 1
                    chunk.status = 'completed'
                    chunk.completion_time = datetime.now()
                else:
                    failed_chunks += 1
                    chunk.status = 'failed'
                
                # Update session
                session.updated_at = datetime.now()
                session.total_records_loaded = sum(c.records_loaded for c in session.chunks)
                
                # Save progress
                self._save_session(session)
                
                # Callbacks
                if self.chunk_callback:
                    self.chunk_callback(chunk)
                
                if self.progress_callback:
                    self.progress_callback(session)
                
                # Log progress
                total_chunks = len(session.chunks)
                progress_pct = (completed_chunks / total_chunks) * 100
                logger.info(f"Progress: {completed_chunks}/{total_chunks} chunks "
                          f"({progress_pct:.1f}%) - {session.total_records_loaded:,} records loaded")
            
            # Update session status
            if failed_chunks == 0:
                session.status = 'completed'
                logger.info(f"Session {session.session_id} completed successfully")
            else:
                session.status = 'failed'
                logger.error(f"Session {session.session_id} completed with {failed_chunks} failed chunks")
            
            session.updated_at = datetime.now()
            self._save_session(session)
            
            return failed_chunks == 0
            
        except Exception as e:
            logger.error(f"Error loading session data: {e}")
            session.status = 'failed'
            session.updated_at = datetime.now()
            self._save_session(session)
            return False
    
    def _load_chunk_data(self, chunk: ChunkInfo, fyers_symbol: str) -> bool:
        """
        Load data for a single chunk.
        
        Args:
            chunk: Chunk information
            fyers_symbol: Fyers symbol string
            
        Returns:
            True if successful, False otherwise
        """
        chunk.attempts += 1
        chunk.last_attempt = datetime.now()
        chunk.status = 'loading'
        
        try:
            logger.info(f"Loading chunk {chunk.chunk_id}: "
                       f"{chunk.start_date.date()} to {chunk.end_date.date()}")
            
            # Calculate days for this chunk
            days_to_fetch = (chunk.end_date - chunk.start_date).days

            # Use rate-limited executor to fetch data with specific date range
            ohlcv_data = self.rate_limited_executor.execute(
                self.fyers_client.get_historical_data,
                fyers_symbol,
                1,  # 1-minute interval
                days_to_fetch,
                chunk.start_date,  # specific start date
                chunk.end_date     # specific end date
            )
            
            if not ohlcv_data:
                chunk.error_message = "No data received from Fyers API"
                logger.warning(f"No data received for chunk {chunk.chunk_id}")
                return False
            
            # Convert to storage format
            storage_data = []
            for record in ohlcv_data:
                # Convert timestamp to datetime if needed
                if isinstance(record.timestamp, int):
                    timestamp = datetime.fromtimestamp(record.timestamp)
                else:
                    timestamp = record.timestamp
                
                # Filter data to chunk date range
                if chunk.start_date <= timestamp <= chunk.end_date:
                    storage_data.append({
                        'timestamp': timestamp,
                        'open': float(record.open),
                        'high': float(record.high),
                        'low': float(record.low),
                        'close': float(record.close),
                        'volume': int(record.volume)
                    })
            
            if not storage_data:
                chunk.error_message = "No data in chunk date range"
                logger.warning(f"No data in date range for chunk {chunk.chunk_id}")
                return False
            
            # Get symbol ID
            symbol_obj = self._get_or_create_symbol(chunk.symbol)
            if not symbol_obj:
                chunk.error_message = "Failed to get symbol from database"
                return False
            
            # Store data in database
            inserted, skipped = self.ohlcv_repo.bulk_insert_with_conflict_handling(
                symbol_obj.id, storage_data, on_conflict="skip"
            )
            
            chunk.records_loaded = inserted
            logger.info(f"Chunk {chunk.chunk_id} completed: "
                       f"{inserted} inserted, {skipped} skipped")
            
            return True
            
        except Exception as e:
            error_msg = str(e)
            chunk.error_message = error_msg
            logger.error(f"Error loading chunk {chunk.chunk_id}: {error_msg}")
            
            # Check if we should retry
            if chunk.attempts < self.max_retries_per_chunk:
                logger.info(f"Will retry chunk {chunk.chunk_id} "
                          f"(attempt {chunk.attempts}/{self.max_retries_per_chunk})")
                chunk.status = 'pending'
                return False
            else:
                logger.error(f"Max retries exceeded for chunk {chunk.chunk_id}")
                return False
    
    def _get_or_create_symbol(self, symbol: str) -> Optional[Symbol]:
        """Get or create symbol in database."""
        from app.database.repositories.symbol_repository import SymbolRepository
        
        symbol_repo = SymbolRepository(self.db)
        existing_symbol = symbol_repo.get_by_symbol(symbol)
        
        if existing_symbol:
            return existing_symbol
        
        # Create new symbol using mapping
        mapping = self.symbol_mapping_service.get_symbol_mapping(symbol)
        if not mapping:
            return None
        
        from app.services.data_service import DataService
        data_service = DataService(self.db)
        
        symbol_data = {
            'symbol': mapping.db_symbol,
            'name': mapping.name,
            'exchange': mapping.exchange,
            'segment': mapping.segment,
            'instrument_type': mapping.instrument_type,
            'tick_size': mapping.tick_size,
            'lot_size': mapping.lot_size,
            'is_active': True,
            'fyers_symbol': mapping.fyers_symbol
        }
        
        return data_service.create_symbol(symbol_data)
    
    def _generate_session_id(self, symbol: str, start_date: datetime, end_date: datetime) -> str:
        """Generate unique session ID."""
        content = f"{symbol}_{start_date.isoformat()}_{end_date.isoformat()}"
        hash_obj = hashlib.md5(content.encode())
        return f"session_{hash_obj.hexdigest()[:12]}"
    
    def _save_session(self, session: LoadingSession):
        """Save session to disk."""
        session_file = self.progress_dir / f"{session.session_id}.json"
        
        # Convert to serializable format
        session_dict = asdict(session)
        
        # Convert datetime objects to ISO strings
        for key, value in session_dict.items():
            if isinstance(value, datetime):
                session_dict[key] = value.isoformat()
        
        # Convert chunk datetime objects
        for chunk_dict in session_dict['chunks']:
            for key, value in chunk_dict.items():
                if isinstance(value, datetime):
                    chunk_dict[key] = value.isoformat()
        
        with open(session_file, 'w') as f:
            json.dump(session_dict, f, indent=2)
    
    def load_session(self, session_id: str) -> Optional[LoadingSession]:
        """Load session from disk."""
        session_file = self.progress_dir / f"{session_id}.json"
        
        if not session_file.exists():
            return None
        
        with open(session_file, 'r') as f:
            session_dict = json.load(f)
        
        # Convert ISO strings back to datetime objects
        datetime_fields = ['created_at', 'updated_at', 'total_start_date', 'total_end_date']
        for field in datetime_fields:
            if session_dict.get(field):
                session_dict[field] = datetime.fromisoformat(session_dict[field])
        
        # Convert chunk datetime objects
        chunk_datetime_fields = ['start_date', 'end_date', 'last_attempt', 'completion_time']
        for chunk_dict in session_dict['chunks']:
            for field in chunk_datetime_fields:
                if chunk_dict.get(field):
                    chunk_dict[field] = datetime.fromisoformat(chunk_dict[field])
        
        # Convert chunks to ChunkInfo objects
        chunks = [ChunkInfo(**chunk_dict) for chunk_dict in session_dict['chunks']]
        session_dict['chunks'] = chunks
        
        return LoadingSession(**session_dict)
    
    def list_sessions(self) -> List[str]:
        """List all available sessions."""
        session_files = list(self.progress_dir.glob("session_*.json"))
        return [f.stem for f in session_files]
    
    def get_rate_limiter_stats(self) -> Dict:
        """Get rate limiter statistics."""
        return self.rate_limiter.get_stats()
