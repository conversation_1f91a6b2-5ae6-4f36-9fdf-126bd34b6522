#!/usr/bin/env python3
"""
Clear all existing mock data and load real historical data from Fyers API.
This script ensures only real market data is stored in the database.
"""

import sys
import os
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.database.connection import get_db, check_database_connection
from app.database.models import StockOHLCV, Symbol, StockOHLCVAgg
from app.services.data_service import DataService
from app.integrations.fyers.fyers_client import FyersClient
from sqlalchemy import text

logger = get_logger(__name__)

class RealDataManager:
    """Manager for clearing mock data and loading real data."""
    
    def __init__(self):
        """Initialize the manager."""
        self.db = None
        self.data_service = None
        self.fyers_client = None
    
    def initialize_services(self) -> bool:
        """Initialize all required services."""
        try:
            # Check database connection
            if not check_database_connection():
                logger.error("Database connection failed")
                return False
            
            # Initialize database session
            self.db = next(get_db())
            
            # Initialize services
            self.data_service = DataService(self.db)
            self.fyers_client = FyersClient()
            
            # Authenticate with Fyers
            if not self.fyers_client.authenticate():
                logger.error("Failed to authenticate with Fyers API")
                return False
            
            logger.info("✓ Services initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            return False
    
    def clear_all_existing_data(self) -> bool:
        """Clear all existing data from database."""
        try:
            logger.info("🗑️  Clearing all existing data from database...")
            
            # Get counts before deletion
            ohlcv_count = self.db.query(StockOHLCV).count()
            ohlcv_agg_count = self.db.query(StockOHLCVAgg).count()
            symbol_count = self.db.query(Symbol).count()
            
            logger.info(f"Found {ohlcv_count} OHLCV records, {ohlcv_agg_count} aggregated records, {symbol_count} symbols")
            
            if ohlcv_count == 0 and ohlcv_agg_count == 0 and symbol_count == 0:
                logger.info("Database is already clean")
                return True
            
            # Delete all data
            logger.info("Deleting OHLCV data...")
            self.db.query(StockOHLCV).delete()
            
            logger.info("Deleting aggregated OHLCV data...")
            self.db.query(StockOHLCVAgg).delete()
            
            logger.info("Deleting symbols...")
            self.db.query(Symbol).delete()
            
            # Commit changes
            self.db.commit()
            
            # Vacuum database for performance
            logger.info("Vacuuming database...")
            self.db.execute(text("VACUUM ANALYZE stock_ohlcv;"))
            self.db.execute(text("VACUUM ANALYZE stock_ohlcv_agg;"))
            self.db.execute(text("VACUUM ANALYZE symbols;"))
            self.db.commit()
            
            logger.info("✅ All existing data cleared successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear existing data: {e}")
            self.db.rollback()
            return False
    
    def load_real_nifty_data(self, years: int = 15) -> bool:
        """Load real NIFTY historical data."""
        try:
            logger.info(f"📈 Loading {years} years of real NIFTY data...")
            
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=years * 365)
            
            logger.info(f"Date range: {start_date.date()} to {end_date.date()}")
            
            # Create NIFTY symbol if it doesn't exist
            symbol_data = {
                'symbol': 'NIFTY',
                'name': 'NIFTY 50',
                'exchange': 'NSE',
                'instrument_type': 'INDEX',
                'token': 'NSE:NIFTY50-INDEX',
                'is_active': True
            }
            
            symbol_obj = self.data_service.create_symbol(symbol_data)
            logger.info(f"Created symbol: {symbol_obj.symbol}")
            
            # Load historical data in chunks (90-day chunks to stay within API limits)
            chunk_days = 90
            current_start = start_date
            total_records = 0
            
            while current_start < end_date:
                current_end = min(current_start + timedelta(days=chunk_days), end_date)
                
                logger.info(f"Loading chunk: {current_start.date()} to {current_end.date()}")
                
                # Fetch data from Fyers API
                ohlc_data = self.fyers_client.get_historical_data(
                    symbol='NSE:NIFTY50-INDEX',
                    interval=1,  # 1-minute data
                    days_to_fetch=chunk_days,
                    start_date=current_start,
                    end_date=current_end
                )
                
                if ohlc_data:
                    # Convert to format expected by data service
                    ohlcv_records = []
                    for record in ohlc_data:
                        ohlcv_records.append({
                            'timestamp': datetime.fromtimestamp(record.timestamp),
                            'open': record.open,
                            'high': record.high,
                            'low': record.low,
                            'close': record.close,
                            'volume': record.volume
                        })
                    
                    # Store data with upsert to avoid duplicates
                    success = self.data_service.store_ohlcv_data('NIFTY', ohlcv_records, upsert=True)
                    if success:
                        total_records += len(ohlcv_records)
                        logger.info(f"✅ Stored {len(ohlcv_records)} records for chunk")
                    else:
                        logger.error(f"❌ Failed to store chunk data")
                else:
                    logger.warning(f"⚠️  No data received for chunk")
                
                current_start = current_end
            
            logger.info(f"🎉 Successfully loaded {total_records} real NIFTY records")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load real NIFTY data: {e}")
            return False
    
    def verify_real_data(self) -> bool:
        """Verify that only real data exists in database."""
        try:
            logger.info("🔍 Verifying real data in database...")
            
            # Check data statistics
            stats = self.data_service.get_data_statistics('NIFTY')
            if not stats:
                logger.error("No NIFTY data found in database")
                return False
            
            logger.info(f"NIFTY data statistics:")
            logger.info(f"  Total records: {stats.get('total_records', 0):,}")
            logger.info(f"  Date range: {stats.get('start_date')} to {stats.get('end_date')}")
            logger.info(f"  Latest price: {stats.get('latest_close', 0):.2f}")
            
            # Verify data is recent (within last 7 days)
            if stats.get('end_date'):
                latest_date = datetime.strptime(stats['end_date'], '%Y-%m-%d')
                days_old = (datetime.now() - latest_date).days
                
                if days_old <= 7:
                    logger.info(f"✅ Data is recent (last updated {days_old} days ago)")
                    return True
                else:
                    logger.warning(f"⚠️  Data is {days_old} days old")
                    return True  # Still valid, just not recent
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to verify real data: {e}")
            return False
    
    def cleanup(self):
        """Cleanup resources."""
        if self.db:
            self.db.close()
            logger.info("✓ Database connection closed")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Clear mock data and load real historical data")
    parser.add_argument("--years", type=int, default=15, 
                       help="Number of years to load (default: 15)")
    parser.add_argument("--skip-clear", action="store_true",
                       help="Skip clearing existing data")
    parser.add_argument("--verify-only", action="store_true",
                       help="Only verify existing data without loading")
    
    args = parser.parse_args()
    
    logger.info("🚀 Real Data Loading Script")
    logger.info("=" * 60)
    
    manager = RealDataManager()
    
    try:
        # Initialize services
        logger.info("Step 1: Initializing services...")
        if not manager.initialize_services():
            return False
        
        if args.verify_only:
            # Only verify existing data
            logger.info("Step 2: Verifying existing data...")
            return manager.verify_real_data()
        
        # Clear existing data
        if not args.skip_clear:
            logger.info("Step 2: Clearing existing mock data...")
            if not manager.clear_all_existing_data():
                return False
        
        # Load real data
        logger.info("Step 3: Loading real historical data...")
        if not manager.load_real_nifty_data(args.years):
            return False
        
        # Verify loaded data
        logger.info("Step 4: Verifying loaded data...")
        if not manager.verify_real_data():
            return False
        
        logger.info("🎉 Real data loading completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return False
    finally:
        manager.cleanup()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
