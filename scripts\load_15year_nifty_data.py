#!/usr/bin/env python3
"""
15-Year NIFTY Historical Data Loading Script
Loads 15 years of 1-minute NIFTY data using chunked loading strategy with progress persistence.
"""

import sys
import os
import argparse
import signal
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.database.connection import get_db, check_database_connection
from app.services.chunked_data_loader import ChunkedDataLoader, LoadingSession, ChunkInfo
from app.services.progress_manager import ProgressManager, ProgressSnapshot
from app.services.data_service import DataService

logger = get_logger(__name__)

class FifteenYearDataManager:
    """Manager for 15-year NIFTY historical data loading."""
    
    def __init__(self):
        """Initialize the manager."""
        self.db = None
        self.chunked_loader = None
        self.progress_manager = None
        self.data_service = None
        self.current_session = None
        self.operation_id = None
        self.interrupted = False
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle interrupt signals gracefully."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.interrupted = True
        
        if self.progress_manager and self.operation_id:
            self.progress_manager.pause_operation(self.operation_id)
            logger.info("Operation paused - can be resumed later")
    
    def initialize_services(self) -> bool:
        """Initialize all required services."""
        try:
            # Check database connection
            if not check_database_connection():
                logger.error("Database connection failed")
                return False
            
            # Initialize database session
            self.db = next(get_db())
            
            # Initialize services
            self.chunked_loader = ChunkedDataLoader(
                db=self.db,
                chunk_size_days=30,  # 30-day chunks for manageable API calls
                max_retries_per_chunk=3
            )
            
            self.progress_manager = ProgressManager()
            self.data_service = DataService(self.db)
            
            # Set up callbacks
            self.chunked_loader.set_progress_callback(self._session_progress_callback)
            self.chunked_loader.set_chunk_callback(self._chunk_progress_callback)
            
            logger.info("✓ Services initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            return False
    
    def _session_progress_callback(self, session: LoadingSession):
        """Callback for session progress updates."""
        if self.progress_manager and self.operation_id:
            completed_chunks = sum(1 for chunk in session.chunks if chunk.status == 'completed')
            
            self.progress_manager.update_progress(
                self.operation_id,
                completed_steps=completed_chunks,
                current_step=f"Loading chunks - {session.total_records_loaded:,} records loaded",
                metadata_update={
                    'total_records_loaded': session.total_records_loaded,
                    'session_status': session.status
                }
            )
            
            self.progress_manager.update_metrics(
                self.operation_id,
                records_processed=session.total_records_loaded
            )
    
    def _chunk_progress_callback(self, chunk: ChunkInfo):
        """Callback for chunk progress updates."""
        if chunk.status == 'completed':
            logger.info(f"✓ Chunk completed: {chunk.chunk_id} - {chunk.records_loaded:,} records")
        elif chunk.status == 'failed':
            logger.error(f"✗ Chunk failed: {chunk.chunk_id} - {chunk.error_message}")
    
    def calculate_date_range(self, years: int = 15) -> tuple:
        """Calculate start and end dates for data loading."""
        end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        start_date = end_date - timedelta(days=years * 365)
        
        logger.info(f"Date range: {start_date.date()} to {end_date.date()} ({years} years)")
        return start_date, end_date
    
    def check_existing_data(self) -> dict:
        """Check existing NIFTY data in database."""
        try:
            logger.info("📊 Checking existing data...")
            
            stats = self.data_service.get_data_statistics("NIFTY")
            if stats and stats.get('total_records', 0) > 0:
                logger.info(f"   Existing records: {stats['total_records']:,}")
                logger.info(f"   Data range: {stats['data_range']['start']} to {stats['data_range']['end']}")
                logger.info(f"   Latest price: ₹{stats['latest_price']}")
                return stats
            else:
                logger.info("   No existing data found")
                return {}
                
        except Exception as e:
            logger.error(f"Error checking existing data: {e}")
            return {}
    
    def create_or_resume_session(self, session_id: str = None, years: int = 15) -> LoadingSession:
        """Create new session or resume existing one."""
        try:
            if session_id:
                # Try to resume existing session
                session = self.chunked_loader.load_session(session_id)
                if session:
                    logger.info(f"📋 Resuming session: {session_id}")
                    logger.info(f"   Status: {session.status}")
                    logger.info(f"   Progress: {len([c for c in session.chunks if c.status == 'completed'])}/{len(session.chunks)} chunks")
                    logger.info(f"   Records loaded: {session.total_records_loaded:,}")
                    return session
                else:
                    logger.error(f"Session {session_id} not found")
                    return None
            
            # Create new session
            start_date, end_date = self.calculate_date_range(years)
            
            session = self.chunked_loader.create_loading_session(
                symbol="NIFTY",
                start_date=start_date,
                end_date=end_date,
                chunk_size_days=30
            )
            
            logger.info(f"📋 Created new session: {session.session_id}")
            logger.info(f"   Total chunks: {len(session.chunks)}")
            logger.info(f"   Estimated records: {len(session.chunks) * 30 * 375:,} (approx)")
            
            return session
            
        except Exception as e:
            logger.error(f"Error creating/resuming session: {e}")
            return None
    
    def start_data_loading(self, session: LoadingSession) -> bool:
        """Start the data loading process."""
        try:
            self.current_session = session
            
            # Start progress tracking
            self.operation_id = f"load_15year_nifty_{session.session_id}"
            
            self.progress_manager.start_operation(
                operation_id=self.operation_id,
                operation_type="15_year_historical_data_load",
                total_steps=len(session.chunks),
                metadata={
                    'symbol': session.symbol,
                    'start_date': session.total_start_date.isoformat(),
                    'end_date': session.total_end_date.isoformat(),
                    'session_id': session.session_id
                }
            )
            
            logger.info("🚀 Starting 15-year historical data loading...")
            logger.info("=" * 80)
            
            # Start loading
            success = self.chunked_loader.load_session_data(session)
            
            # Complete progress tracking
            if success:
                self.progress_manager.complete_operation(self.operation_id, success=True)
                logger.info("🎉 15-year data loading completed successfully!")
            else:
                error_msg = "Data loading failed - check logs for details"
                self.progress_manager.complete_operation(self.operation_id, success=False, error_message=error_msg)
                logger.error("❌ 15-year data loading failed")
            
            return success
            
        except Exception as e:
            logger.error(f"Error during data loading: {e}")
            if self.progress_manager and self.operation_id:
                self.progress_manager.complete_operation(self.operation_id, success=False, error_message=str(e))
            return False
    
    def show_final_statistics(self):
        """Show final loading statistics."""
        try:
            logger.info("\n" + "=" * 80)
            logger.info("📈 FINAL STATISTICS")
            logger.info("=" * 80)
            
            # Get updated data statistics
            stats = self.data_service.get_data_statistics("NIFTY")
            if stats:
                logger.info(f"Total records in database: {stats['total_records']:,}")
                logger.info(f"Data range: {stats['data_range']['start']} to {stats['data_range']['end']}")
                logger.info(f"Latest price: ₹{stats['latest_price']}")
                
                # Calculate data completeness
                total_days = (datetime.now() - datetime(2010, 1, 1)).days
                trading_days = total_days * 5 / 7  # Rough estimate
                expected_records = trading_days * 375  # 375 minutes per trading day
                completeness = (stats['total_records'] / expected_records) * 100
                
                logger.info(f"Estimated completeness: {completeness:.1f}%")
            
            # Get rate limiter statistics
            rate_stats = self.chunked_loader.get_rate_limiter_stats()
            logger.info(f"\nRate Limiter Statistics:")
            logger.info(f"  Total requests: {rate_stats['total_requests']}")
            logger.info(f"  Success rate: {rate_stats['success_rate']:.1%}")
            logger.info(f"  Current delay: {rate_stats['current_delay']:.2f}s")
            logger.info(f"  Circuit state: {rate_stats['circuit_state']}")
            
            # Get operation metrics
            if self.progress_manager and self.operation_id:
                metrics = self.progress_manager.get_operation_metrics(self.operation_id)
                if metrics:
                    logger.info(f"\nOperation Metrics:")
                    logger.info(f"  Records processed: {metrics.records_processed:,}")
                    logger.info(f"  Processing rate: {metrics.records_per_second:.1f} records/sec")
                    logger.info(f"  API calls made: {metrics.api_calls_made}")
                    logger.info(f"  Errors encountered: {metrics.errors_encountered}")
                    logger.info(f"  Retries attempted: {metrics.retries_attempted}")
            
        except Exception as e:
            logger.error(f"Error showing statistics: {e}")
    
    def cleanup(self):
        """Cleanup resources."""
        if self.db:
            self.db.close()
            logger.info("✓ Database connection closed")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Load 15 years of NIFTY historical data")
    parser.add_argument("--years", type=int, default=15, 
                       help="Number of years to load (default: 15)")
    parser.add_argument("--resume", type=str, 
                       help="Resume existing session by session ID")
    parser.add_argument("--list-sessions", action="store_true",
                       help="List available sessions")
    parser.add_argument("--chunk-size", type=int, default=30,
                       help="Chunk size in days (default: 30)")
    
    args = parser.parse_args()
    
    logger.info("🚀 15-Year NIFTY Historical Data Loading Script")
    logger.info("=" * 80)
    
    manager = FifteenYearDataManager()
    success = False
    
    try:
        # Initialize services
        logger.info("Step 1: Initializing services...")
        if not manager.initialize_services():
            return False
        
        # List sessions if requested
        if args.list_sessions:
            sessions = manager.chunked_loader.list_sessions()
            if sessions:
                logger.info("Available sessions:")
                for session_id in sessions:
                    session = manager.chunked_loader.load_session(session_id)
                    if session:
                        logger.info(f"  {session_id}: {session.status} - "
                                  f"{len([c for c in session.chunks if c.status == 'completed'])}/{len(session.chunks)} chunks")
            else:
                logger.info("No sessions found")
            return True
        
        # Check existing data
        logger.info("\nStep 2: Checking existing data...")
        existing_stats = manager.check_existing_data()
        
        # Create or resume session
        logger.info("\nStep 3: Setting up loading session...")
        session = manager.create_or_resume_session(args.resume, args.years)
        if not session:
            return False
        
        # Starting automated data loading for nightly runs
        if not args.resume and existing_stats:
            logger.info(f"\nExisting data found ({existing_stats['total_records']:,} records). "
                       "Continuing with automated 15-year load for nightly processing.")
        
        # Start loading
        logger.info("\nStep 4: Starting data loading...")
        logger.info("⚠️  This operation may take several hours. You can interrupt with Ctrl+C to pause and resume later.")
        
        success = manager.start_data_loading(session)
        
        # Show final statistics
        logger.info("\nStep 5: Final statistics...")
        manager.show_final_statistics()
        
        if success:
            logger.info("\n🎉 15-year NIFTY data loading completed successfully!")
            logger.info("✓ Data is ready for analysis and backtesting")
        else:
            logger.error("\n❌ 15-year data loading completed with errors")
            logger.info("💡 You can resume the operation later using --resume")
        
        return success
        
    except KeyboardInterrupt:
        logger.info("\n⚠️  Operation interrupted by user")
        logger.info("💡 Progress has been saved. Resume with: --resume <session_id>")
        return False
    except Exception as e:
        logger.error(f"\n❌ Unexpected error: {e}")
        return False
    finally:
        manager.cleanup()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
