#!/usr/bin/env python3
"""
Frontend startup script for Signal Stack trading platform.
"""

import sys
import os
import subprocess
import time
from pathlib import Path
import argparse
import json

# Add project root to path
project_root = Path(__file__).parent.parent
frontend_dir = project_root / "frontend"


def check_node_installation():
    """Check if Node.js is installed."""
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✓ Node.js installed: {version}")
            return True
        else:
            print("✗ Node.js not found")
            return False
    except FileNotFoundError:
        print("✗ Node.js not installed")
        return False


def check_npm_installation():
    """Check if npm is installed."""
    try:
        result = subprocess.run(["npm", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✓ npm installed: {version}")
            return True
        else:
            print("✗ npm not found")
            return False
    except FileNotFoundError:
        print("✗ npm not installed")
        return False


def check_frontend_directory():
    """Check if frontend directory exists."""
    if frontend_dir.exists():
        print(f"✓ Frontend directory found: {frontend_dir}")
        return True
    else:
        print(f"✗ Frontend directory not found: {frontend_dir}")
        return False


def check_package_json():
    """Check if package.json exists."""
    package_json = frontend_dir / "package.json"
    if package_json.exists():
        print("✓ package.json found")
        return True
    else:
        print("✗ package.json not found")
        return False


def install_dependencies():
    """Install npm dependencies."""
    try:
        print("📦 Installing npm dependencies...")
        os.chdir(frontend_dir)
        
        result = subprocess.run(["npm", "install"], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Dependencies installed successfully")
            return True
        else:
            print(f"✗ Failed to install dependencies: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ Error installing dependencies: {e}")
        return False


def check_dependencies():
    """Check if node_modules exists."""
    node_modules = frontend_dir / "node_modules"
    if node_modules.exists():
        print("✓ node_modules found")
        return True
    else:
        print("⚠️  node_modules not found, will install dependencies")
        return install_dependencies()


def create_env_file():
    """Create .env file if it doesn't exist."""
    env_file = frontend_dir / ".env"
    
    if env_file.exists():
        print("✓ .env file found")
        return True
    
    print("📝 Creating .env file...")
    
    env_content = """# Signal Stack Frontend Environment Variables
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development
"""
    
    try:
        with open(env_file, 'w') as f:
            f.write(env_content)
        print("✓ .env file created")
        return True
    except Exception as e:
        print(f"✗ Failed to create .env file: {e}")
        return False


def start_development_server(port=3000):
    """Start the React development server."""
    try:
        print(f"🚀 Starting React Development Server on port {port}")
        
        os.chdir(frontend_dir)
        
        # Set environment variables
        env = os.environ.copy()
        env['PORT'] = str(port)
        env['BROWSER'] = 'none'  # Don't auto-open browser
        
        # Start development server
        subprocess.run(["npm", "start"], env=env)
        
    except KeyboardInterrupt:
        print("\n🛑 Development server stopped by user")
    except Exception as e:
        print(f"✗ Failed to start development server: {e}")
        sys.exit(1)


def build_production():
    """Build production bundle."""
    try:
        print("🏗️  Building production bundle...")
        
        os.chdir(frontend_dir)
        
        result = subprocess.run(["npm", "run", "build"], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Production build completed successfully")
            print(f"📁 Build files are in: {frontend_dir / 'build'}")
            return True
        else:
            print(f"✗ Build failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ Error during build: {e}")
        return False


def serve_production(port=3000):
    """Serve production build."""
    try:
        # Check if serve is installed globally
        result = subprocess.run(["serve", "--version"], capture_output=True, text=True)
        
        if result.returncode != 0:
            print("📦 Installing serve globally...")
            subprocess.run(["npm", "install", "-g", "serve"])
        
        print(f"🚀 Serving production build on port {port}")
        
        os.chdir(frontend_dir)
        subprocess.run(["serve", "-s", "build", "-l", str(port)])
        
    except KeyboardInterrupt:
        print("\n🛑 Production server stopped by user")
    except Exception as e:
        print(f"✗ Failed to serve production build: {e}")
        sys.exit(1)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Signal Stack Frontend Server")
    parser.add_argument("--port", type=int, default=3000, help="Port to run on")
    parser.add_argument("--build", action="store_true", help="Build production bundle")
    parser.add_argument("--serve", action="store_true", help="Serve production build")
    parser.add_argument("--install", action="store_true", help="Install dependencies only")
    parser.add_argument("--skip-checks", action="store_true", help="Skip pre-flight checks")
    
    args = parser.parse_args()
    
    if not args.skip_checks:
        print("🔍 Running pre-flight checks...")
        
        # Check Node.js
        if not check_node_installation():
            print("Please install Node.js from https://nodejs.org/")
            sys.exit(1)
        
        # Check npm
        if not check_npm_installation():
            print("npm should be installed with Node.js")
            sys.exit(1)
        
        # Check frontend directory
        if not check_frontend_directory():
            print("Frontend directory not found. Please ensure you're in the correct project directory.")
            sys.exit(1)
        
        # Check package.json
        if not check_package_json():
            print("package.json not found in frontend directory")
            sys.exit(1)
        
        # Check dependencies
        if not check_dependencies():
            sys.exit(1)
        
        # Create .env file
        if not create_env_file():
            sys.exit(1)
        
        print("✅ All pre-flight checks passed")
    
    # Handle different modes
    if args.install:
        install_dependencies()
    elif args.build:
        if build_production():
            print("🎉 Build completed successfully!")
        else:
            sys.exit(1)
    elif args.serve:
        if not (frontend_dir / "build").exists():
            print("⚠️  Production build not found. Building first...")
            if not build_production():
                sys.exit(1)
        serve_production(args.port)
    else:
        # Default: start development server
        start_development_server(args.port)


if __name__ == "__main__":
    main()
