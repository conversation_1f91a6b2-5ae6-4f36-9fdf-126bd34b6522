# Signal Stack Trading Platform - Complete Overview

## 🚀 Introduction

Signal Stack is a comprehensive algorithmic trading platform built with Python (FastAPI) backend and React frontend. It provides real-time market data processing, technical analysis, backtesting, paper trading, and strategy management capabilities.

## 📋 Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Module Documentation](#module-documentation)
3. [Installation & Setup](#installation--setup)
4. [Execution Instructions](#execution-instructions)
5. [API Documentation](#api-documentation)
6. [Testing Guide](#testing-guide)
7. [Deployment Guide](#deployment-guide)
8. [Troubleshooting](#troubleshooting)

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React UI      │    │   FastAPI       │    │   PostgreSQL    │
│   (Frontend)    │◄──►│   (Backend)     │◄──►│   (Database)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   WebSocket     │    │   Fyers API     │    │   TimescaleDB   │
│   (Real-time)   │    │   (Market Data) │    │   (Time Series) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Core Components

- **Frontend**: React TypeScript application with Material-UI
- **Backend**: FastAPI with async support and WebSocket capabilities
- **Database**: PostgreSQL with TimescaleDB for time-series data
- **Market Data**: Fyers API integration for real-time and historical data
- **Authentication**: JWT-based authentication with API key support
- **Real-time**: WebSocket connections for live data streaming

## 📦 Module Documentation

### 1. Historical Data Storage Module
**Location**: `app/services/data_service.py`, `app/services/market_data_service.py`

**Purpose**: Manages historical market data storage and retrieval.

**Key Features**:
- 3-month NIFTY historical data storage (1-minute timeframe)
- Data validation and integrity checks
- Multiple timeframe aggregation (1m, 5m, 15m, 30m, 1h, 4h, 1d)
- Gap detection and data quality metrics

**Usage**:
```python
from app.services.data_service import DataService
from app.services.market_data_service import MarketDataService

# Initialize services
data_service = DataService(db)
market_service = MarketDataService(db)

# Fetch and store historical data
success = market_service.fetch_and_store_historical_data(
    symbol="NSE:NIFTY50-INDEX",
    timeframe="1",
    days=90
)
```

### 2. React Frontend UI Module
**Location**: `frontend/`

**Purpose**: Provides interactive web interface for trading operations.

**Key Components**:
- **Dashboard**: Real-time market overview and portfolio summary
- **Market Data**: Live quotes, charts, and market analysis
- **Strategy Manager**: Create, edit, and manage trading strategies
- **Backtesting**: Historical strategy performance analysis
- **Paper Trading**: Virtual trading environment

**Key Features**:
- Real-time data visualization with Recharts
- Material-UI components for professional interface
- WebSocket integration for live updates
- Responsive design for desktop and mobile

### 3. Technical Indicators Module
**Location**: `app/services/indicators/`, `app/services/realtime_indicators.py`

**Purpose**: Calculates technical indicators for market analysis.

**Available Indicators**:
- **Trend**: SMA, EMA, MACD, Bollinger Bands
- **Momentum**: RSI, Stochastic, Williams %R
- **Volume**: Volume SMA, On-Balance Volume
- **Volatility**: Average True Range, Bollinger Bands

**Real-time Features**:
- Live indicator calculation on market data updates
- Configurable update frequencies (fast/medium/slow)
- WebSocket broadcasting of indicator updates
- Signal generation and alerts

### 4. Backtesting Module
**Location**: `app/services/backtesting/`

**Purpose**: Historical strategy performance testing and optimization.

**Key Features**:
- **Advanced Engine**: Event-driven and vectorized backtesting
- **Strategy Optimization**: Grid search parameter optimization
- **Walk-Forward Analysis**: Out-of-sample testing
- **Monte Carlo Analysis**: Risk assessment and simulation
- **Performance Metrics**: Comprehensive risk and return analysis

**Supported Strategies**:
- Moving Average Crossover
- RSI Mean Reversion
- Bollinger Bands Breakout
- Custom strategy framework

### 5. Paper Trading Module
**Location**: `app/services/paper_trading/`

**Purpose**: Virtual trading environment for strategy testing.

**Key Features**:
- **Real-time Engine**: Live market data integration
- **Order Management**: Market, limit, stop, and stop-limit orders
- **Risk Management**: Position sizing, drawdown limits, exposure controls
- **Portfolio Tracking**: Real-time P&L, positions, and performance
- **Strategy Integration**: Automated and manual trading modes

### 6. Web API Backend Module
**Location**: `app/api/`

**Purpose**: RESTful API with authentication and comprehensive endpoints.

**Key Endpoints**:
- **Authentication**: `/api/v1/auth/*` - JWT and API key authentication
- **Market Data**: `/api/v1/market-data/*` - Real-time and historical data
- **Indicators**: `/api/v1/indicators/*` - Technical analysis calculations
- **Backtesting**: `/api/v1/backtesting/*` - Strategy testing and optimization
- **Paper Trading**: `/api/v1/paper-trading/*` - Virtual trading operations

**Security Features**:
- JWT token authentication
- API key validation
- Rate limiting
- Permission-based access control

## 🛠️ Installation & Setup

### Prerequisites

- Python 3.9+
- Node.js 16+
- PostgreSQL 14+
- Git

### Backend Setup

1. **Clone Repository**:
```bash
git clone <repository-url>
cd signal_stack
```

2. **Create Virtual Environment**:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install Dependencies**:
```bash
pip install -r requirements.txt
```

4. **Database Setup**:
```bash
# Install PostgreSQL and TimescaleDB
# Create database
createdb signal_stack_db

# Run migrations
python scripts/setup_database.py
```

5. **Configuration**:
```bash
# Copy and edit configuration
cp config.yaml.example config.yaml
# Edit database connection and API keys
```

6. **Fyers API Setup**:
```bash
# Add your Fyers credentials
echo "your_client_id" > fyers_client_id.txt
# Follow Fyers authentication flow
python scripts/setup_fyers_auth.py
```

### Frontend Setup

1. **Navigate to Frontend**:
```bash
cd frontend
```

2. **Install Dependencies**:
```bash
npm install
```

3. **Environment Configuration**:
```bash
# Create .env file
echo "REACT_APP_API_URL=http://localhost:8000" > .env
```

## 🚀 Execution Instructions

### Starting the Backend

1. **Activate Virtual Environment**:
```bash
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. **Start FastAPI Server**:
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

3. **Verify Backend**:
```bash
curl http://localhost:8000/health
```

### Starting the Frontend

1. **Navigate to Frontend Directory**:
```bash
cd frontend
```

2. **Start React Development Server**:
```bash
npm start
```

3. **Access Application**:
Open browser to `http://localhost:3000`

### Running Individual Modules

#### Historical Data Storage
```bash
# Setup NIFTY 3-month data
python scripts/setup_nifty_historical_3months.py

# Check data statistics
python scripts/check_data_status.py
```

#### Technical Indicators
```bash
# Calculate indicators for a symbol
python scripts/calculate_indicators.py --symbol NIFTY --indicators RSI,SMA,MACD

# Start real-time indicator engine
python scripts/start_realtime_indicators.py
```

#### Backtesting
```bash
# Run a simple backtest
python scripts/run_backtest.py --strategy ma_crossover --symbol NIFTY --days 90

# Run strategy optimization
python scripts/optimize_strategy.py --strategy rsi_strategy --symbol NIFTY
```

#### Paper Trading
```bash
# Create paper trading session
python scripts/create_paper_session.py --name "Test Session" --capital 100000

# Start automated trading
python scripts/start_paper_trading.py --session-id <session_id>
```

## 📚 API Documentation

### Authentication

All API endpoints require authentication via API key or JWT token.

**API Key Authentication**:
```bash
curl -H "X-API-Key: dev-key-123" http://localhost:8000/api/v1/market-data/symbols
```

**JWT Authentication**:
```bash
# Get token
TOKEN=$(curl -X POST http://localhost:8000/api/v1/auth/demo-token | jq -r .access_token)

# Use token
curl -H "Authorization: Bearer $TOKEN" http://localhost:8000/api/v1/market-data/symbols
```

### Key API Endpoints

#### Market Data
```bash
# Get symbols
GET /api/v1/market-data/symbols

# Get OHLCV data
GET /api/v1/market-data/ohlcv?symbol=NIFTY&start_date=2024-01-01&end_date=2024-04-10

# Get real-time quotes
GET /api/v1/market-data/quotes
```

#### Technical Indicators
```bash
# Calculate RSI
POST /api/v1/indicators/calculate
{
  "symbol": "NIFTY",
  "indicator": "RSI",
  "parameters": {"period": 14},
  "start_date": "2024-01-01",
  "end_date": "2024-04-10"
}

# Bulk calculation
POST /api/v1/indicators/bulk-calculate
{
  "symbol": "NIFTY",
  "indicators": [
    {"name": "RSI", "parameters": {"period": 14}},
    {"name": "SMA", "parameters": {"period": 20}}
  ]
}
```

#### Backtesting
```bash
# Run backtest
POST /api/v1/backtesting/run
{
  "strategy_name": "ma_crossover",
  "symbol": "NIFTY",
  "start_date": "2024-01-01",
  "end_date": "2024-04-10",
  "parameters": {"short_window": 10, "long_window": 20}
}

# Get results
GET /api/v1/backtesting/results/{backtest_id}
```

#### Paper Trading
```bash
# Create session
POST /api/v1/paper-trading/sessions
{
  "name": "Test Session",
  "initial_capital": 100000,
  "symbols": ["NIFTY"]
}

# Place order
POST /api/v1/paper-trading/sessions/{session_id}/orders
{
  "symbol": "NIFTY",
  "side": "BUY",
  "quantity": 1,
  "order_type": "MARKET"
}
```

## 🧪 Testing Guide

### Running Tests

The platform includes comprehensive testing suite:

```bash
# Install test dependencies
python run_tests.py --install-deps

# Run all tests
python run_tests.py --all --coverage

# Run specific test suites
python run_tests.py --unit          # Unit tests
python run_tests.py --integration   # Integration tests
python run_tests.py --e2e          # End-to-end tests

# Quick test run
python run_tests.py --quick

# Generate comprehensive report
python run_tests.py --report
```

### Test Categories

1. **Unit Tests** (`tests/unit/`):
   - Individual service testing
   - Data validation
   - Algorithm correctness

2. **Integration Tests** (`tests/integration/`):
   - API endpoint testing
   - Database integration
   - Service interaction

3. **End-to-End Tests** (`tests/e2e/`):
   - Complete workflow testing
   - User journey simulation
   - Performance testing

### Test Coverage

Target coverage: 85%+
- Core services: 90%+
- API endpoints: 85%+
- Utilities: 80%+

## 🚀 Deployment Guide

### Production Deployment

1. **Environment Setup**:
```bash
# Production configuration
cp config.yaml.prod config.yaml

# Set environment variables
export ENVIRONMENT=production
export DATABASE_URL=********************************/signal_stack_prod
export SECRET_KEY=your-secret-key
```

2. **Database Migration**:
```bash
# Run production migrations
python scripts/migrate_database.py --env production
```

3. **Backend Deployment**:
```bash
# Using Gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# Using Docker
docker build -t signal-stack-backend .
docker run -p 8000:8000 signal-stack-backend
```

4. **Frontend Deployment**:
```bash
# Build production bundle
cd frontend
npm run build

# Serve with nginx or deploy to CDN
```

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# Scale services
docker-compose up -d --scale backend=3
```

## 🔧 Troubleshooting

### Common Issues

1. **Database Connection Issues**:
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Test connection
psql -h localhost -U username -d signal_stack_db
```

2. **Fyers API Issues**:
```bash
# Check API credentials
python scripts/test_fyers_connection.py

# Refresh access token
python scripts/refresh_fyers_token.py
```

3. **Frontend Build Issues**:
```bash
# Clear cache and reinstall
cd frontend
rm -rf node_modules package-lock.json
npm install
```

4. **Memory Issues**:
```bash
# Monitor memory usage
python scripts/monitor_performance.py

# Optimize database queries
python scripts/optimize_database.py
```

### Performance Optimization

1. **Database Optimization**:
   - Index optimization for time-series queries
   - Connection pooling configuration
   - Query performance monitoring

2. **API Optimization**:
   - Response caching
   - Async request handling
   - Rate limiting configuration

3. **Frontend Optimization**:
   - Code splitting
   - Lazy loading
   - Bundle size optimization

## 📞 Support & Maintenance

### Monitoring

- **Health Checks**: `/health` endpoint
- **Metrics**: Prometheus integration
- **Logging**: Structured logging with rotation
- **Alerts**: Critical error notifications

### Backup & Recovery

```bash
# Database backup
python scripts/backup_database.py

# Data export
python scripts/export_data.py --symbol NIFTY --days 30

# System restore
python scripts/restore_system.py --backup-file backup.sql
```

### Updates & Maintenance

```bash
# Update dependencies
pip install -r requirements.txt --upgrade

# Database maintenance
python scripts/maintain_database.py

# Performance tuning
python scripts/tune_performance.py
```

---

## 📈 Next Steps

1. **Enhanced Features**:
   - Options trading support
   - Advanced order types
   - Portfolio optimization
   - Risk management dashboard

2. **Integrations**:
   - Additional broker APIs
   - News sentiment analysis
   - Social trading features
   - Mobile application

3. **Scalability**:
   - Microservices architecture
   - Kubernetes deployment
   - Multi-region support
   - Real-time streaming optimization

For detailed technical documentation, see the `/docs` directory.
For API reference, visit `http://localhost:8000/docs` when the server is running.
