"""
Base strategy class and trading components for backtesting.
"""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from dataclasses import dataclass
import pandas as pd

from app.core.logging import get_logger

logger = get_logger(__name__)


class SignalType(Enum):
    """Trading signal types."""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    EXIT = "exit"


class PositionType(Enum):
    """Position types."""
    LONG = "long"
    SHORT = "short"


@dataclass
class TradingSignal:
    """Trading signal generated by strategy."""
    
    symbol: str
    signal_type: SignalType
    timestamp: datetime
    price: float
    quantity: Optional[int] = None
    confidence: float = 1.0
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Initialize signal after creation."""
        if self.metadata is None:
            self.metadata = {}


@dataclass
class Position:
    """Trading position."""
    
    symbol: str
    position_type: PositionType
    quantity: int
    entry_price: float
    entry_timestamp: datetime
    current_price: float = 0.0
    exit_price: Optional[float] = None
    exit_timestamp: Optional[datetime] = None
    commission: float = 0.0
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Initialize position after creation."""
        if self.metadata is None:
            self.metadata = {}
        if self.current_price == 0.0:
            self.current_price = self.entry_price
    
    @property
    def is_open(self) -> bool:
        """Check if position is open."""
        return self.exit_price is None
    
    @property
    def is_closed(self) -> bool:
        """Check if position is closed."""
        return self.exit_price is not None
    
    @property
    def is_long(self) -> bool:
        """Check if position is long."""
        return self.position_type == PositionType.LONG
    
    @property
    def is_short(self) -> bool:
        """Check if position is short."""
        return self.position_type == PositionType.SHORT
    
    @property
    def market_value(self) -> float:
        """Get current market value of position."""
        return self.quantity * self.current_price
    
    @property
    def unrealized_pnl(self) -> float:
        """Get unrealized P&L."""
        if self.is_closed:
            return 0.0
        
        if self.is_long:
            return (self.current_price - self.entry_price) * self.quantity
        else:
            return (self.entry_price - self.current_price) * self.quantity
    
    @property
    def realized_pnl(self) -> float:
        """Get realized P&L."""
        if not self.is_closed:
            return 0.0
        
        if self.is_long:
            return (self.exit_price - self.entry_price) * self.quantity - self.commission
        else:
            return (self.entry_price - self.exit_price) * self.quantity - self.commission
    
    @property
    def total_pnl(self) -> float:
        """Get total P&L (realized + unrealized)."""
        return self.realized_pnl + self.unrealized_pnl
    
    @property
    def return_percentage(self) -> float:
        """Get return percentage."""
        cost_basis = self.quantity * self.entry_price
        if cost_basis == 0:
            return 0.0
        
        if self.is_closed:
            return (self.realized_pnl / cost_basis) * 100
        else:
            return (self.unrealized_pnl / cost_basis) * 100
    
    def update_price(self, price: float) -> None:
        """Update current price."""
        self.current_price = price
    
    def close(self, price: float, timestamp: datetime, commission: float = 0.0) -> None:
        """Close position."""
        self.exit_price = price
        self.exit_timestamp = timestamp
        self.commission += commission
        self.current_price = price


@dataclass
class Trade:
    """Completed trade record."""
    
    symbol: str
    position_type: PositionType
    quantity: int
    entry_price: float
    exit_price: float
    entry_timestamp: datetime
    exit_timestamp: datetime
    commission: float = 0.0
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Initialize trade after creation."""
        if self.metadata is None:
            self.metadata = {}
    
    @property
    def duration(self) -> float:
        """Get trade duration in days."""
        return (self.exit_timestamp - self.entry_timestamp).total_seconds() / 86400
    
    @property
    def pnl(self) -> float:
        """Get trade P&L."""
        if self.position_type == PositionType.LONG:
            return (self.exit_price - self.entry_price) * self.quantity - self.commission
        else:
            return (self.entry_price - self.exit_price) * self.quantity - self.commission
    
    @property
    def return_percentage(self) -> float:
        """Get trade return percentage."""
        cost_basis = self.quantity * self.entry_price
        if cost_basis == 0:
            return 0.0
        return (self.pnl / cost_basis) * 100
    
    @property
    def is_profitable(self) -> bool:
        """Check if trade is profitable."""
        return self.pnl > 0
    
    @classmethod
    def from_position(cls, position: Position) -> 'Trade':
        """Create trade from closed position."""
        if not position.is_closed:
            raise ValueError("Cannot create trade from open position")
        
        return cls(
            symbol=position.symbol,
            position_type=position.position_type,
            quantity=position.quantity,
            entry_price=position.entry_price,
            exit_price=position.exit_price,
            entry_timestamp=position.entry_timestamp,
            exit_timestamp=position.exit_timestamp,
            commission=position.commission,
            metadata=position.metadata.copy()
        )


class BaseStrategy(ABC):
    """Base class for trading strategies."""
    
    def __init__(self, name: str, parameters: Dict[str, Any] = None):
        """
        Initialize strategy.
        
        Args:
            name: Strategy name
            parameters: Strategy parameters
        """
        self.name = name
        self.parameters = parameters or {}
        self.positions: List[Position] = []
        self.trades: List[Trade] = []
        self.signals: List[TradingSignal] = []
        self.is_initialized = False
    
    @abstractmethod
    def initialize(self, data: pd.DataFrame) -> None:
        """
        Initialize strategy with historical data.
        
        Args:
            data: Historical OHLCV data
        """
        pass
    
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame, current_index: int) -> List[TradingSignal]:
        """
        Generate trading signals based on current market data.
        
        Args:
            data: Historical OHLCV data
            current_index: Current data index
            
        Returns:
            List of trading signals
        """
        pass
    
    def on_bar(self, data: pd.DataFrame, current_index: int) -> List[TradingSignal]:
        """
        Called on each new bar of data.
        
        Args:
            data: Historical OHLCV data
            current_index: Current data index
            
        Returns:
            List of trading signals
        """
        if not self.is_initialized:
            self.initialize(data)
            self.is_initialized = True
        
        return self.generate_signals(data, current_index)
    
    def get_open_positions(self, symbol: str = None) -> List[Position]:
        """Get open positions."""
        positions = [p for p in self.positions if p.is_open]
        if symbol:
            positions = [p for p in positions if p.symbol == symbol]
        return positions
    
    def get_closed_positions(self, symbol: str = None) -> List[Position]:
        """Get closed positions."""
        positions = [p for p in self.positions if p.is_closed]
        if symbol:
            positions = [p for p in positions if p.symbol == symbol]
        return positions
    
    def add_position(self, position: Position) -> None:
        """Add position to strategy."""
        self.positions.append(position)
    
    def close_position(self, position: Position, price: float, timestamp: datetime, commission: float = 0.0) -> Trade:
        """Close position and create trade record."""
        position.close(price, timestamp, commission)
        trade = Trade.from_position(position)
        self.trades.append(trade)
        return trade
    
    def update_positions(self, symbol: str, price: float) -> None:
        """Update position prices."""
        for position in self.get_open_positions(symbol):
            position.update_price(price)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get strategy performance summary."""
        if not self.trades:
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'total_pnl': 0.0,
                'avg_trade_pnl': 0.0
            }

        winning_trades = [t for t in self.trades if t.is_profitable]
        losing_trades = [t for t in self.trades if not t.is_profitable]
        total_pnl = sum(t.pnl for t in self.trades)

        return {
            'total_trades': len(self.trades),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': (len(winning_trades) / len(self.trades)) * 100,
            'total_pnl': total_pnl,
            'avg_trade_pnl': total_pnl / len(self.trades),
            'avg_winning_trade': sum(t.pnl for t in winning_trades) / len(winning_trades) if winning_trades else 0,
            'avg_losing_trade': sum(t.pnl for t in losing_trades) / len(losing_trades) if losing_trades else 0
        }

    def to_dict(self) -> Dict[str, Any]:
        """Convert strategy to dictionary."""
        return {
            'name': self.name,
            'parameters': self.parameters,
            'total_positions': len(self.positions),
            'open_positions': len(self.get_open_positions()),
            'total_trades': len(self.trades),
            'total_signals': len(self.signals),
            'performance': self.get_performance_summary()
        }
