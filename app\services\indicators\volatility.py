"""
Volatility technical indicators.
"""

from typing import Dict, Any, Union, List
import pandas as pd
import numpy as np

from .base import BaseIndicator


class BollingerBands(BaseIndicator):
    """Bollinger Bands indicator."""
    
    def __init__(self, period: int = 20, std_dev: float = 2.0):
        """
        Initialize Bollinger Bands.
        
        Args:
            period: Moving average period
            std_dev: Standard deviation multiplier
        """
        super().__init__("BollingerBands", {"period": period, "std_dev": std_dev})
        self.period = period
        self.std_dev = std_dev
    
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calculate Bollinger Bands values."""
        df = self._prepare_data(data)
        
        if len(df) < self.period:
            return {"upper": [], "middle": [], "lower": [], "timestamps": []}
        
        # Calculate middle band (SMA)
        middle_band = df['close'].rolling(window=self.period).mean()
        
        # Calculate standard deviation
        std = df['close'].rolling(window=self.period).std()
        
        # Calculate upper and lower bands
        upper_band = middle_band + (std * self.std_dev)
        lower_band = middle_band - (std * self.std_dev)
        
        # Store values
        self.values = {
            'upper': upper_band.dropna().tolist(),
            'middle': middle_band.dropna().tolist(),
            'lower': lower_band.dropna().tolist()
        }
        self.timestamps = df['timestamp'][self.period-1:].tolist()
        self.is_ready = len(self.values['middle']) > 0
        
        return {
            "upper": self.values['upper'],
            "middle": self.values['middle'],
            "lower": self.values['lower'],
            "timestamps": self.timestamps,
            "period": self.period,
            "std_dev": self.std_dev
        }
    
    def get_signals(self, data: pd.DataFrame) -> Dict[str, List[bool]]:
        """Generate Bollinger Bands trading signals."""
        result = self.calculate(data)
        
        if not result['upper']:
            return {'buy_signals': [], 'sell_signals': []}
        
        # Get price data aligned with bands
        df = self._prepare_data(data)
        prices = df['close'][self.period-1:].values
        upper = np.array(result['upper'])
        lower = np.array(result['lower'])
        
        # Buy when price touches lower band, sell when price touches upper band
        buy_signals = []
        sell_signals = []
        
        for i in range(len(prices)):
            if prices[i] <= lower[i]:
                buy_signals.append(True)
                sell_signals.append(False)
            elif prices[i] >= upper[i]:
                buy_signals.append(False)
                sell_signals.append(True)
            else:
                buy_signals.append(False)
                sell_signals.append(False)
        
        return {
            'buy_signals': buy_signals,
            'sell_signals': sell_signals
        }


class ATR(BaseIndicator):
    """Average True Range indicator."""
    
    def __init__(self, period: int = 14):
        """
        Initialize ATR.
        
        Args:
            period: ATR calculation period
        """
        super().__init__("ATR", {"period": period})
        self.period = period
    
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calculate ATR values."""
        df = self._prepare_data(data)
        
        if len(df) < self.period + 1:
            return {"values": [], "timestamps": []}
        
        # Calculate True Range
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift(1))
        low_close = np.abs(df['low'] - df['close'].shift(1))
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        
        # Calculate ATR (smoothed True Range)
        atr = pd.Series(true_range).rolling(window=self.period).mean()
        
        # Store values
        self.values = atr.dropna().tolist()
        self.timestamps = df['timestamp'][self.period:].tolist()
        self.is_ready = len(self.values) > 0
        
        return {
            "values": self.values,
            "timestamps": self.timestamps,
            "period": self.period
        }


class Keltner(BaseIndicator):
    """Keltner Channels indicator."""
    
    def __init__(self, period: int = 20, multiplier: float = 2.0):
        """
        Initialize Keltner Channels.
        
        Args:
            period: EMA period
            multiplier: ATR multiplier
        """
        super().__init__("Keltner", {"period": period, "multiplier": multiplier})
        self.period = period
        self.multiplier = multiplier
    
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calculate Keltner Channels values."""
        df = self._prepare_data(data)
        
        if len(df) < self.period + 1:
            return {"upper": [], "middle": [], "lower": [], "timestamps": []}
        
        # Calculate middle line (EMA of typical price)
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        middle_line = typical_price.ewm(span=self.period).mean()
        
        # Calculate ATR
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift(1))
        low_close = np.abs(df['low'] - df['close'].shift(1))
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        atr = pd.Series(true_range).ewm(span=self.period).mean()
        
        # Calculate upper and lower channels
        upper_channel = middle_line + (atr * self.multiplier)
        lower_channel = middle_line - (atr * self.multiplier)
        
        # Store values (skip first value due to shift operation)
        self.values = {
            'upper': upper_channel[1:].tolist(),
            'middle': middle_line[1:].tolist(),
            'lower': lower_channel[1:].tolist()
        }
        self.timestamps = df['timestamp'][1:].tolist()
        self.is_ready = len(self.values['middle']) > 0
        
        return {
            "upper": self.values['upper'],
            "middle": self.values['middle'],
            "lower": self.values['lower'],
            "timestamps": self.timestamps,
            "period": self.period,
            "multiplier": self.multiplier
        }
