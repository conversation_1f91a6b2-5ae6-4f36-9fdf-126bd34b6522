"""
Unit tests for DataService.
"""

import pytest
from datetime import datetime, timedelta
import pandas as pd

from app.services.data_service import DataService
from app.database.models import Symbol, StockOHLCV


@pytest.mark.unit
class TestDataService:
    """Test cases for DataService."""
    
    def test_create_symbol(self, data_service: DataService):
        """Test symbol creation."""
        symbol_data = {
            'symbol': 'TESTSTOCK',
            'name': 'Test Stock',
            'exchange': 'NSE',
            'segment': 'EQ',
            'instrument_type': 'EQUITY',
            'tick_size': 0.05,
            'lot_size': 1,
            'is_active': True,
            'fyers_symbol': 'NSE:TESTSTOCK-EQ'
        }
        
        symbol = data_service.create_symbol(symbol_data)
        
        assert symbol is not None
        assert symbol.symbol == 'TESTSTOCK'
        assert symbol.name == 'Test Stock'
        assert symbol.exchange == 'NSE'
        assert symbol.is_active is True
    
    def test_get_symbol_by_name(self, data_service: DataService, sample_symbol: Symbol):
        """Test getting symbol by name."""
        symbol = data_service.get_symbol('NIFTY')
        
        assert symbol is not None
        assert symbol.symbol == 'NIFTY'
        assert symbol.id == sample_symbol.id
    
    def test_get_nonexistent_symbol(self, data_service: DataService):
        """Test getting non-existent symbol."""
        symbol = data_service.get_symbol('NONEXISTENT')
        assert symbol is None
    
    def test_store_ohlcv_data(self, data_service: DataService, sample_symbol: Symbol):
        """Test storing OHLCV data."""
        ohlcv_data = [
            {
                'timestamp': datetime(2024, 1, 1, 9, 15),
                'open': 20000.0,
                'high': 20100.0,
                'low': 19900.0,
                'close': 20050.0,
                'volume': 1000000
            },
            {
                'timestamp': datetime(2024, 1, 1, 9, 16),
                'open': 20050.0,
                'high': 20150.0,
                'low': 20000.0,
                'close': 20100.0,
                'volume': 1100000
            }
        ]
        
        success = data_service.store_ohlcv_data('NIFTY', ohlcv_data)
        assert success is True
        
        # Verify data was stored
        stored_data = data_service.get_ohlcv_data(
            symbol='NIFTY',
            start_time=datetime(2024, 1, 1),
            end_time=datetime(2024, 1, 2)
        )
        
        assert len(stored_data) == 2
        assert stored_data[0]['close'] == 20050.0
        assert stored_data[1]['close'] == 20100.0
    
    def test_get_ohlcv_data_as_dataframe(self, data_service: DataService, sample_ohlcv_data: pd.DataFrame):
        """Test getting OHLCV data as DataFrame."""
        df = data_service.get_ohlcv_data(
            symbol='NIFTY',
            start_time=datetime(2024, 1, 1),
            end_time=datetime(2024, 4, 10),
            as_dataframe=True
        )
        
        assert isinstance(df, pd.DataFrame)
        assert not df.empty
        assert 'open' in df.columns
        assert 'high' in df.columns
        assert 'low' in df.columns
        assert 'close' in df.columns
        assert 'volume' in df.columns
        assert len(df) == 100  # Sample data has 100 records
    
    def test_get_latest_ohlcv(self, data_service: DataService, sample_ohlcv_data: pd.DataFrame):
        """Test getting latest OHLCV data."""
        latest_data = data_service.get_latest_ohlcv('NIFTY', limit=5)
        
        assert len(latest_data) == 5
        # Should be in descending order by timestamp
        for i in range(1, len(latest_data)):
            assert latest_data[i-1]['timestamp'] >= latest_data[i]['timestamp']
    
    def test_get_data_statistics(self, data_service: DataService, sample_ohlcv_data: pd.DataFrame):
        """Test getting data statistics."""
        stats = data_service.get_data_statistics('NIFTY')
        
        assert stats is not None
        assert 'symbol' in stats
        assert 'total_records' in stats
        assert 'data_range' in stats
        assert 'latest_price' in stats
        assert 'days_of_data' in stats
        
        assert stats['symbol'] == 'NIFTY'
        assert stats['total_records'] == 100
        assert stats['latest_price'] is not None
    
    def test_store_duplicate_ohlcv_data(self, data_service: DataService, sample_symbol: Symbol):
        """Test storing duplicate OHLCV data (should handle gracefully)."""
        ohlcv_data = [
            {
                'timestamp': datetime(2024, 1, 1, 9, 15),
                'open': 20000.0,
                'high': 20100.0,
                'low': 19900.0,
                'close': 20050.0,
                'volume': 1000000
            }
        ]
        
        # Store once
        success1 = data_service.store_ohlcv_data('NIFTY', ohlcv_data)
        assert success1 is True
        
        # Store again (duplicate)
        success2 = data_service.store_ohlcv_data('NIFTY', ohlcv_data)
        # Should handle gracefully (implementation dependent)
        assert success2 in [True, False]  # Either succeeds or fails gracefully
    
    def test_get_ohlcv_data_empty_result(self, data_service: DataService, sample_symbol: Symbol):
        """Test getting OHLCV data with no results."""
        # Query for future dates
        future_start = datetime(2025, 1, 1)
        future_end = datetime(2025, 1, 2)
        
        data = data_service.get_ohlcv_data(
            symbol='NIFTY',
            start_time=future_start,
            end_time=future_end
        )
        
        assert data == []
    
    def test_get_ohlcv_data_invalid_symbol(self, data_service: DataService):
        """Test getting OHLCV data for invalid symbol."""
        data = data_service.get_ohlcv_data(
            symbol='INVALID',
            start_time=datetime(2024, 1, 1),
            end_time=datetime(2024, 1, 2)
        )
        
        assert data == []
    
    def test_cleanup_old_data(self, data_service: DataService, sample_ohlcv_data: pd.DataFrame):
        """Test cleanup of old data."""
        # This test depends on the implementation of cleanup_old_data
        result = data_service.cleanup_old_data(days_to_keep=30)
        
        assert isinstance(result, dict)
        # The exact behavior depends on implementation
    
    def test_data_validation(self, data_service: DataService, sample_symbol: Symbol):
        """Test data validation during storage."""
        # Test with invalid OHLCV data
        invalid_ohlcv_data = [
            {
                'timestamp': datetime(2024, 1, 1, 9, 15),
                'open': 20000.0,
                'high': 19900.0,  # High < Open (invalid)
                'low': 20100.0,   # Low > Open (invalid)
                'close': 20050.0,
                'volume': 1000000
            }
        ]
        
        # Should handle invalid data gracefully
        success = data_service.store_ohlcv_data('NIFTY', invalid_ohlcv_data)
        # Implementation dependent - might validate or store as-is
        assert success in [True, False]
    
    def test_bulk_data_operations(self, data_service: DataService, sample_symbol: Symbol):
        """Test bulk data operations performance."""
        # Generate large dataset
        large_dataset = []
        base_time = datetime(2024, 1, 1, 9, 15)
        
        for i in range(1000):  # 1000 records
            large_dataset.append({
                'timestamp': base_time + timedelta(minutes=i),
                'open': 20000.0 + i,
                'high': 20100.0 + i,
                'low': 19900.0 + i,
                'close': 20050.0 + i,
                'volume': 1000000 + i * 1000
            })
        
        success = data_service.store_ohlcv_data('NIFTY', large_dataset)
        assert success is True
        
        # Verify bulk retrieval
        retrieved_data = data_service.get_ohlcv_data(
            symbol='NIFTY',
            start_time=base_time,
            end_time=base_time + timedelta(minutes=1000)
        )
        
        assert len(retrieved_data) >= 1000  # At least the data we inserted
    
    def test_concurrent_data_access(self, data_service: DataService, sample_ohlcv_data: pd.DataFrame):
        """Test concurrent data access (basic test)."""
        import threading
        import time
        
        results = []
        
        def read_data():
            data = data_service.get_ohlcv_data(
                symbol='NIFTY',
                start_time=datetime(2024, 1, 1),
                end_time=datetime(2024, 4, 10)
            )
            results.append(len(data))
        
        # Create multiple threads
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=read_data)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads
        for thread in threads:
            thread.join()
        
        # All threads should get the same result
        assert len(set(results)) == 1  # All results should be identical
        assert results[0] > 0  # Should have data
