"""
Reference Backtesting Engine based on the high-performance reference project approach.
This engine provides a generic framework that can work with any strategy file.
"""

import os
import sys
import time
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Type
import importlib.util
import tempfile

# Import backtesting library
from backtesting import Backtest

from app.core.logging import get_logger
from app.services.optimized_data_service import OptimizedDataService
from app.database.connection import get_db
from app.services.backtesting.strategy_interface import get_strategy_adapter, BacktestingLibraryAdapter

logger = get_logger(__name__)


class ReferenceBacktestEngine:
    """
    Reference backtesting engine that provides high-performance backtesting
    with a generic strategy interface.
    """
    
    def __init__(self, db_session=None):
        """
        Initialize the reference backtesting engine.
        
        Args:
            db_session: Database session (optional)
        """
        self.db = db_session or next(get_db())
        self.data_service = OptimizedDataService(self.db)
        self.execution_time = None
        self.memory_usage = None
        
        # Strategy cache to avoid repeated imports
        self._strategy_cache = {}
        
    def run_backtest(
        self,
        strategy_name: Optional[str] = None,
        strategy_module: Optional[str] = None,
        strategy_class: Optional[str] = None,
        symbol: str = "NIFTY50",
        timeframe: int = 30,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        initial_capital: float = 30000,
        margin: float = 0.1,
        commission: float = 0.0,
        strategy_config: Optional[Dict[str, Any]] = None,
        generate_files: bool = False
    ) -> Dict[str, Any]:
        """
        Run backtest using the reference approach with any strategy.

        Args:
            strategy_name: Name of registered strategy (preferred)
            strategy_module: Path to strategy module file (alternative)
            strategy_class: Name of the strategy class (alternative)
            symbol: Symbol to backtest
            timeframe: Timeframe in minutes
            start_date: Start date (YYYY-MM-DD format)
            end_date: End date (YYYY-MM-DD format)
            initial_capital: Initial capital
            margin: Margin requirement
            commission: Commission rate
            strategy_config: Strategy configuration
            generate_files: Whether to generate output files

        Returns:
            Comprehensive backtest results
        """
        logger.info(f"🚀 Starting Reference Backtest for {symbol} ({timeframe}min timeframe)")
        start_time = time.time()
        
        try:
            # Step 1: Get strategy adapter and class
            strategy_adapter, strategy_cls = self._get_strategy(strategy_name, strategy_module, strategy_class)
            
            # Step 2: Load and prepare data
            data = self._load_and_prepare_data(symbol, timeframe, start_date, end_date)
            if data is None or data.empty:
                raise ValueError(f"No data available for {symbol}")
            
            # Step 3: Merge strategy config
            final_config = self._merge_strategy_config(strategy_adapter, strategy_config)
            config_path = None
            if final_config:
                config_path = self._create_temp_config(final_config)
            
            # Step 4: Run backtest using backtesting library
            backtest_results = self._run_backtest_execution(
                data, strategy_cls, initial_capital, margin, commission
            )
            
            # Step 5: Calculate custom metrics
            custom_metrics = self._calculate_custom_metrics(
                backtest_results, initial_capital, margin
            )
            
            # Step 6: Generate files if requested
            if generate_files:
                self._generate_output_files(backtest_results, custom_metrics, symbol, timeframe)
            
            # Step 7: Cleanup temporary files
            if config_path and os.path.exists(config_path):
                os.unlink(config_path)
            
            self.execution_time = time.time() - start_time
            
            logger.info(f"✅ Reference Backtest completed in {self.execution_time:.2f}s")
            
            return {
                'execution_time': self.execution_time,
                'symbol': symbol,
                'timeframe': timeframe,
                'data_points': len(data),
                'backtest_stats': backtest_results['stats']._strategy,
                'custom_metrics': custom_metrics,
                'trades_data': backtest_results['trades_data'],
                'performance_summary': {
                    'total_trades': backtest_results['stats']['# Trades'],
                    'win_rate': custom_metrics.get('win_rate', 0) if custom_metrics else 0,
                    'total_return': backtest_results['stats']['Return [%]'],
                    'max_drawdown': backtest_results['stats']['Max. Drawdown [%]'],
                    'sharpe_ratio': backtest_results['stats']['Sharpe Ratio']
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Reference backtest failed: {e}")
            raise
        finally:
            # Cleanup
            if config_path and os.path.exists(config_path):
                try:
                    os.unlink(config_path)
                except:
                    pass
    
    def _get_strategy(self, strategy_name: Optional[str], strategy_module: Optional[str], strategy_class: Optional[str]):
        """Get strategy adapter and class using the generic interface."""
        try:
            # Option 1: Use registered strategy by name
            if strategy_name:
                adapter = get_strategy_adapter(strategy_name)
                if adapter is None:
                    raise ValueError(f"Strategy '{strategy_name}' not found in registry")

                strategy_cls = adapter.get_strategy_class()
                logger.info(f"✅ Using registered strategy: {strategy_name}")
                return adapter, strategy_cls

            # Option 2: Create adapter from module and class
            elif strategy_module and strategy_class:
                cache_key = f"{strategy_module}::{strategy_class}"

                if cache_key in self._strategy_cache:
                    adapter, strategy_cls = self._strategy_cache[cache_key]
                    return adapter, strategy_cls

                adapter = BacktestingLibraryAdapter(strategy_module, strategy_class)

                if not adapter.validate_strategy():
                    raise ValueError(f"Strategy validation failed for {strategy_class}")

                strategy_cls = adapter.get_strategy_class()

                # Cache the adapter and class
                self._strategy_cache[cache_key] = (adapter, strategy_cls)

                logger.info(f"✅ Created adapter for strategy: {strategy_class}")
                return adapter, strategy_cls

            else:
                # Default to SimplePriceActionStrategy
                logger.info("🔄 No strategy specified, using default SimplePriceActionStrategy")
                adapter = get_strategy_adapter("SimplePriceActionStrategy")
                if adapter is None:
                    raise ValueError("Default SimplePriceActionStrategy not available")

                strategy_cls = adapter.get_strategy_class()
                return adapter, strategy_cls

        except Exception as e:
            logger.error(f"❌ Failed to get strategy: {e}")
            raise

    def _merge_strategy_config(self, strategy_adapter, user_config: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Merge strategy's default config with user-provided config."""
        try:
            # Get strategy's default config
            default_config = strategy_adapter.get_strategy_config()

            if default_config is None and user_config is None:
                return None

            if default_config is None:
                return user_config

            if user_config is None:
                return default_config

            # Merge configs (user config takes precedence)
            merged_config = default_config.copy()
            merged_config.update(user_config)

            logger.info("✅ Merged strategy configuration")
            return merged_config

        except Exception as e:
            logger.error(f"❌ Failed to merge strategy config: {e}")
            return user_config

    def _load_and_prepare_data(
        self,
        symbol: str,
        timeframe: int,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> pd.DataFrame:
        """Load and prepare data for backtesting."""
        logger.info(f"📊 Loading data for {symbol} ({timeframe}min timeframe)")

        try:
            # Set default date range if not provided
            if not end_date:
                end_date = datetime.now()
            else:
                end_date = datetime.strptime(end_date, "%Y-%m-%d")

            if not start_date:
                # Default to 8 years of data for comprehensive backtesting
                start_date = end_date - timedelta(days=8*365)
            else:
                start_date = datetime.strptime(start_date, "%Y-%m-%d")

            # Get 1-minute data from database
            data = self.data_service.get_ohlcv_data(
                symbol=symbol,
                start_time=start_date,
                end_time=end_date,
                interval="1m",
                exchange="NSE",
                as_dataframe=True
            )

            if data is None or data.empty:
                raise ValueError(f"No data found for {symbol}")

            logger.info(f"📈 Loaded {len(data)} 1-minute records")

            # Resample to target timeframe if needed
            if timeframe > 1:
                data = self._resample_data(data, timeframe)
                logger.info(f"📊 Resampled to {len(data)} {timeframe}-minute records")

            # Prepare data for backtesting library format
            data = self._prepare_backtest_format(data)

            return data

        except Exception as e:
            logger.error(f"❌ Failed to load data: {e}")
            raise

    def _resample_data(self, data: pd.DataFrame, timeframe: int) -> pd.DataFrame:
        """Resample 1-minute data to target timeframe."""
        try:
            # Ensure datetime index
            if 'datetime' in data.columns:
                data = data.set_index('datetime')

            # Resample using OHLC aggregation
            resampled = data.resample(f'{timeframe}min').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()

            return resampled.reset_index()

        except Exception as e:
            logger.error(f"❌ Failed to resample data: {e}")
            raise

    def _prepare_backtest_format(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data in the format expected by backtesting library."""
        try:
            # Ensure required columns exist and are properly named
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']

            # Map column names to backtesting library format
            column_mapping = {
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'volume': 'Volume'
            }

            # Rename columns
            data = data.rename(columns=column_mapping)

            # Ensure datetime index
            if 'datetime' in data.columns:
                data = data.set_index('datetime')

            # Ensure all required columns exist
            for col in required_columns:
                if col not in data.columns:
                    raise ValueError(f"Required column '{col}' not found in data")

            # Select only required columns
            data = data[required_columns]

            # Ensure numeric types
            for col in required_columns:
                data[col] = pd.to_numeric(data[col], errors='coerce')

            # Remove any rows with NaN values
            data = data.dropna()

            # Sort by index (datetime)
            data = data.sort_index()

            logger.info(f"✅ Data prepared for backtesting: {len(data)} records")
            return data

        except Exception as e:
            logger.error(f"❌ Failed to prepare data format: {e}")
            raise

    def _run_backtest_execution(
        self,
        data: pd.DataFrame,
        strategy_cls: Type,
        initial_capital: float,
        margin: float,
        commission: float
    ) -> Dict[str, Any]:
        """Run the actual backtest execution using backtesting library."""
        logger.info("⚡ Executing backtest...")
        exec_start = time.time()

        # Change to reference directory for config file access
        original_cwd = os.getcwd()
        reference_dir = Path(__file__).parent.parent.parent.parent / "Reference" / "V7_IntradayCleanup_Best_30Min"

        try:
            # Change working directory to reference project
            os.chdir(str(reference_dir))

            # Initialize backtest with optimized settings
            bt = Backtest(
                data,
                strategy_cls,
                cash=initial_capital,
                margin=margin,
                commission=commission,
                trade_on_close=True,
                hedging=False,
                exclusive_orders=True,
                finalize_trades=True
            )

            # Run backtest
            stats = bt.run()
            exec_time = time.time() - exec_start

            logger.info(f"✅ Backtest execution completed in {exec_time:.2f}s")

            # Extract trade data
            trades_data = self._extract_trade_data(bt, stats)

            return {
                'backtest_instance': bt,
                'stats': stats,
                'trades_data': trades_data,
                'execution_time': exec_time
            }

        except Exception as e:
            logger.error(f"❌ Backtest execution failed: {e}")
            raise
        finally:
            # Always restore original working directory
            os.chdir(original_cwd)

    def _extract_trade_data(self, bt: Backtest, stats) -> List[Dict[str, Any]]:
        """Extract trade data from backtest results."""
        try:
            trades = []

            # First try to get trades from strategy's internal trade log
            strategy_instance = bt._strategy
            if hasattr(strategy_instance, 'trade_log') and strategy_instance.trade_log:
                logger.info(f"📊 Found strategy internal trade log with {len(strategy_instance.trade_log)} entries")

                for trade_entry in strategy_instance.trade_log:
                    trades.append({
                        'entry_time': trade_entry.get('entry_time'),
                        'exit_time': trade_entry.get('exit_time'),
                        'entry_price': trade_entry.get('entry_price'),
                        'exit_price': trade_entry.get('exit_price'),
                        'size': trade_entry.get('size', 1),
                        'pnl': trade_entry.get('pnl'),
                        'return_pct': trade_entry.get('return_pct', 0),
                        'duration': trade_entry.get('duration', 0),
                        'is_long': trade_entry.get('position_type', 'LONG') == 'LONG',
                        'position_type': trade_entry.get('position_type'),
                        'signal': trade_entry.get('signal'),
                        'pattern': trade_entry.get('pattern')
                    })

            # Fallback to backtesting library's trade data
            elif hasattr(stats, '_trades') and not stats._trades.empty:
                logger.info(f"📊 Using backtesting library trade data with {len(stats._trades)} trades")

                for idx, trade in stats._trades.iterrows():
                    entry_time = trade.EntryTime
                    exit_time = trade.ExitTime
                    entry_price = trade.EntryPrice
                    exit_price = trade.ExitPrice
                    size = trade.Size
                    pnl = (exit_price - entry_price) * size if size > 0 else (entry_price - exit_price) * abs(size)

                    trades.append({
                        'entry_time': entry_time,
                        'exit_time': exit_time,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'size': size,
                        'pnl': pnl,
                        'return_pct': pnl / (entry_price * abs(size)) * 100,
                        'duration': (exit_time - entry_time).total_seconds() / 60,  # minutes
                        'is_long': size > 0,
                        'position_type': "LONG" if size > 0 else "SHORT"
                    })

            # Last resort: check if bt has _trades attribute
            elif hasattr(bt, '_trades') and bt._trades is not None:
                logger.info(f"📊 Using backtest instance trade data")

                for trade in bt._trades:
                    trades.append({
                        'entry_time': trade.entry_time,
                        'exit_time': trade.exit_time,
                        'entry_price': trade.entry_price,
                        'exit_price': trade.exit_price,
                        'size': trade.size,
                        'pnl': trade.pnl,
                        'return_pct': trade.pnl / (trade.entry_price * abs(trade.size)) * 100,
                        'duration': (trade.exit_time - trade.entry_time).total_seconds() / 60,  # minutes
                        'is_long': trade.size > 0
                    })

            logger.info(f"📊 Extracted {len(trades)} trades")
            return trades

        except Exception as e:
            logger.error(f"❌ Failed to extract trade data: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []

    def _calculate_custom_metrics(
        self,
        backtest_results: Dict[str, Any],
        initial_capital: float,
        margin: float
    ) -> Optional[Dict[str, Any]]:
        """Calculate custom metrics from backtest results."""
        try:
            trades_data = backtest_results['trades_data']

            if not trades_data:
                logger.warning("No trades found for custom metrics calculation")
                return None

            # Basic trade statistics
            total_trades = len(trades_data)
            winning_trades = [t for t in trades_data if t['pnl'] > 0]
            losing_trades = [t for t in trades_data if t['pnl'] <= 0]

            win_count = len(winning_trades)
            loss_count = len(losing_trades)
            win_rate = (win_count / total_trades * 100) if total_trades > 0 else 0

            # PnL calculations
            total_pnl = sum(t['pnl'] for t in trades_data)
            avg_win = sum(t['pnl'] for t in winning_trades) / win_count if win_count > 0 else 0
            avg_loss = sum(t['pnl'] for t in losing_trades) / loss_count if loss_count > 0 else 0

            # Risk-to-Reward ratio
            avg_win_abs = abs(avg_win)
            avg_loss_abs = abs(avg_loss)
            risk_reward_ratio = avg_win_abs / avg_loss_abs if avg_loss_abs > 0 else 0

            # Profit factor
            gross_profit = sum(t['pnl'] for t in winning_trades)
            gross_loss = abs(sum(t['pnl'] for t in losing_trades))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

            # Return calculations
            total_return_pct = (total_pnl / initial_capital) * 100

            # Duration statistics
            avg_trade_duration = sum(t['duration'] for t in trades_data) / total_trades if total_trades > 0 else 0

            custom_metrics = {
                'total_trades': total_trades,
                'winning_trades': win_count,
                'losing_trades': loss_count,
                'win_rate': round(win_rate, 2),
                'total_pnl': round(total_pnl, 2),
                'total_return_pct': round(total_return_pct, 2),
                'avg_win': round(avg_win, 2),
                'avg_loss': round(avg_loss, 2),
                'risk_reward_ratio': round(risk_reward_ratio, 2),
                'profit_factor': round(profit_factor, 2),
                'gross_profit': round(gross_profit, 2),
                'gross_loss': round(gross_loss, 2),
                'avg_trade_duration_minutes': round(avg_trade_duration, 2),
                'initial_capital': initial_capital
            }

            logger.info(f"✅ Calculated custom metrics: {win_rate:.1f}% win rate, {total_return_pct:.1f}% return")
            return custom_metrics

        except Exception as e:
            logger.error(f"❌ Failed to calculate custom metrics: {e}")
            return None

    def _create_temp_config(self, strategy_config: Dict[str, Any]) -> str:
        """Create temporary configuration file for strategy."""
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                import yaml
                yaml.dump(strategy_config, f, default_flow_style=False)
                temp_path = f.name

            logger.info(f"📝 Created temporary config file: {temp_path}")
            return temp_path

        except Exception as e:
            logger.error(f"❌ Failed to create temp config: {e}")
            raise

    def _generate_output_files(
        self,
        backtest_results: Dict[str, Any],
        custom_metrics: Optional[Dict[str, Any]],
        symbol: str,
        timeframe: int
    ):
        """Generate output files for backtest results."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Generate trades CSV
            trades_data = backtest_results['trades_data']
            if trades_data:
                trades_df = pd.DataFrame(trades_data)
                trades_filename = f"backtest_trades_{symbol}_{timeframe}min_{timestamp}.csv"
                trades_df.to_csv(trades_filename, index=False)
                logger.info(f"📄 Generated trades file: {trades_filename}")

            # Generate stats JSON
            stats_data = {
                'backtest_stats': dict(backtest_results['stats']),
                'custom_metrics': custom_metrics,
                'execution_time': backtest_results['execution_time'],
                'symbol': symbol,
                'timeframe': timeframe,
                'timestamp': timestamp
            }

            stats_filename = f"backtest_stats_{symbol}_{timeframe}min_{timestamp}.json"
            with open(stats_filename, 'w') as f:
                json.dump(stats_data, f, indent=2, default=str)

            logger.info(f"📄 Generated stats file: {stats_filename}")

        except Exception as e:
            logger.error(f"❌ Failed to generate output files: {e}")

    def close(self):
        """Close database connection and cleanup resources."""
        try:
            if hasattr(self, 'db') and self.db:
                self.db.close()
            logger.info("🔒 Reference backtest engine closed")
        except Exception as e:
            logger.error(f"❌ Error closing engine: {e}")


# Convenience functions for quick backtesting
def run_reference_backtest(
    strategy_name: Optional[str] = None,
    strategy_module: Optional[str] = None,
    strategy_class: Optional[str] = None,
    symbol: str = "NIFTY50",
    timeframe: int = 30,
    **kwargs
) -> Dict[str, Any]:
    """
    Convenience function to run a reference backtest.

    Args:
        strategy_name: Name of registered strategy (preferred)
        strategy_module: Path to strategy module file (alternative)
        strategy_class: Name of the strategy class (alternative)
        symbol: Symbol to backtest
        timeframe: Timeframe in minutes
        **kwargs: Additional arguments for run_backtest

    Returns:
        Backtest results
    """
    engine = None
    try:
        engine = ReferenceBacktestEngine()
        return engine.run_backtest(
            strategy_name=strategy_name,
            strategy_module=strategy_module,
            strategy_class=strategy_class,
            symbol=symbol,
            timeframe=timeframe,
            **kwargs
        )
    finally:
        if engine:
            engine.close()


def run_simple_price_action_backtest(
    symbol: str = "NIFTY50",
    timeframe: int = 30,
    **kwargs
) -> Dict[str, Any]:
    """
    Convenience function to run SimplePriceActionStrategy backtest.

    Args:
        symbol: Symbol to backtest
        timeframe: Timeframe in minutes
        **kwargs: Additional arguments for run_backtest

    Returns:
        Backtest results
    """
    return run_reference_backtest(
        strategy_name="SimplePriceActionStrategy",
        symbol=symbol,
        timeframe=timeframe,
        **kwargs
    )
