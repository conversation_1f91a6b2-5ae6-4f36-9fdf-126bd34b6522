#!/usr/bin/env python3
"""
Enhanced NIFTY Historical Data Storage Script
Fetches and stores 3 months of 1-minute NIFTY data with comprehensive validation.
"""

import sys
import os
import asyncio
from datetime import datetime, timed<PERSON>ta
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.database.connection import get_db
from app.services.market_data_service import MarketDataService
from app.services.data_service import DataService
from app.services.aggregation_service import AggregationService
from app.database.repositories.symbol_repository import SymbolRepository

logger = get_logger(__name__)


class NIFTYHistoricalDataManager:
    """Manager for NIFTY historical data operations."""
    
    def __init__(self):
        """Initialize the manager."""
        self.db = None
        self.market_service = None
        self.data_service = None
        self.aggregation_service = None
        self.symbol_repo = None
        
    def initialize_services(self):
        """Initialize all required services."""
        try:
            self.db = next(get_db())
            self.market_service = MarketDataService(self.db)
            self.data_service = DataService(self.db)
            self.aggregation_service = AggregationService(self.db)
            self.symbol_repo = SymbolRepository(self.db)
            
            logger.info("✓ Services initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            return False
    
    def setup_nifty_symbol(self):
        """Ensure NIFTY symbol exists in database."""
        try:
            # Check if NIFTY symbol exists
            nifty_symbol = self.symbol_repo.get_by_symbol("NIFTY")
            
            if not nifty_symbol:
                logger.info("Creating NIFTY symbol in database...")
                
                symbol_data = {
                    'symbol': 'NIFTY',
                    'name': 'NIFTY 50 Index',
                    'exchange': 'NSE',
                    'segment': 'INDEX',
                    'instrument_type': 'INDEX',
                    'tick_size': 0.05,
                    'lot_size': 1,
                    'is_active': True,
                    'fyers_symbol': 'NSE:NIFTY50-INDEX'
                }
                
                nifty_symbol = self.data_service.create_symbol(symbol_data)
                logger.info(f"✓ NIFTY symbol created with ID: {nifty_symbol.id}")
            else:
                logger.info(f"✓ NIFTY symbol already exists with ID: {nifty_symbol.id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting up NIFTY symbol: {e}")
            return False
    
    def fetch_3month_historical_data(self):
        """Fetch 3 months of historical data for NIFTY."""
        try:
            logger.info("🔄 Starting 3-month historical data fetch for NIFTY...")

            # Initialize Fyers connection
            if not self.market_service.initialize_fyers_connection():
                logger.error("Failed to initialize Fyers connection")
                return False

            # Check existing data
            existing_stats = self.data_service.get_data_statistics("NIFTY")
            if existing_stats and existing_stats.get('total_records', 0) > 0:
                logger.info(f"⚠️  Existing data found: {existing_stats['total_records']} records")
                logger.info(f"   Data range: {existing_stats['data_range']['start']} to {existing_stats['data_range']['end']}")

                response = input("Do you want to continue and potentially overwrite existing data? (y/N): ")
                if response.lower() != 'y':
                    logger.info("Operation cancelled by user")
                    return False

            # Fetch 3 months of data directly
            logger.info("📊 Fetching 3 months of 1-minute NIFTY data...")

            success = self.market_service.fetch_and_store_historical_data(
                symbol="NSE:NIFTY50-INDEX",
                timeframe="1",  # 1 minute
                days=90  # 3 months
            )

            if success:
                # Get final statistics
                final_stats = self.data_service.get_data_statistics("NIFTY")
                if final_stats:
                    logger.info(f"✓ Data fetch completed: {final_stats['total_records']} total records")
                    logger.info(f"  Data range: {final_stats['data_range']['start']} to {final_stats['data_range']['end']}")
                return True
            else:
                logger.error("⚠️  Data fetch failed")
                return False

        except Exception as e:
            logger.error(f"Error fetching historical data: {e}")
            return False
    
    def validate_data_integrity(self):
        """Validate the integrity of stored data."""
        try:
            logger.info("🔍 Validating data integrity...")
            
            # Get data statistics
            stats = self.data_service.get_data_statistics("NIFTY")
            if not stats:
                logger.error("No data found for validation")
                return False
            
            logger.info("📊 Data Statistics:")
            logger.info(f"   Total records: {stats['total_records']:,}")
            logger.info(f"   Data range: {stats['data_range']['start']} to {stats['data_range']['end']}")
            logger.info(f"   Latest price: ₹{stats['latest_price']}")
            
            # Validate data completeness
            expected_days = 90
            expected_minutes_per_day = 375  # 9:15 AM to 3:30 PM = 375 minutes
            expected_total_records = expected_days * expected_minutes_per_day
            
            actual_records = stats['total_records']
            completeness_ratio = (actual_records / expected_total_records) * 100
            
            logger.info(f"📈 Data Completeness:")
            logger.info(f"   Expected records (approx): {expected_total_records:,}")
            logger.info(f"   Actual records: {actual_records:,}")
            logger.info(f"   Completeness: {completeness_ratio:.1f}%")
            
            # Check for data gaps
            gaps = self.data_service.check_data_gaps("NIFTY", timedelta(minutes=5))
            if gaps:
                logger.warning(f"⚠️  Found {len(gaps)} data gaps larger than 5 minutes")
                for gap in gaps[:5]:  # Show first 5 gaps
                    logger.warning(f"   Gap: {gap['start']} to {gap['end']} ({gap['duration']})")
            else:
                logger.info("✓ No significant data gaps found")
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating data: {e}")
            return False
    
    def generate_aggregated_timeframes(self):
        """Generate aggregated data for multiple timeframes."""
        try:
            logger.info("🔄 Generating aggregated timeframes...")
            
            timeframes = ['5m', '15m', '30m', '1h', '2h', '4h', '1d']
            
            for timeframe in timeframes:
                logger.info(f"📊 Aggregating {timeframe} data...")
                
                success = self.aggregation_service.aggregate_symbol_data(
                    symbol="NIFTY",
                    timeframe=timeframe,
                    start_time=datetime.now() - timedelta(days=90),
                    end_time=datetime.now()
                )
                
                if success:
                    logger.info(f"✓ {timeframe} aggregation completed")
                else:
                    logger.warning(f"⚠️  {timeframe} aggregation failed")
            
            # Get aggregation statistics
            agg_stats = self.aggregation_service.get_aggregation_statistics()
            logger.info("📈 Aggregation Statistics:")
            for key, value in agg_stats.items():
                if key.endswith('_records'):
                    logger.info(f"   {key}: {value:,}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error generating aggregated timeframes: {e}")
            return False
    
    def cleanup(self):
        """Cleanup resources."""
        if self.db:
            self.db.close()
            logger.info("✓ Database connection closed")


async def main():
    """Main execution function."""
    logger.info("🚀 Starting NIFTY 3-Month Historical Data Setup")
    logger.info("=" * 60)
    
    manager = NIFTYHistoricalDataManager()
    
    try:
        # Step 1: Initialize services
        logger.info("Step 1: Initializing services...")
        if not manager.initialize_services():
            return False
        
        # Step 2: Setup NIFTY symbol
        logger.info("\nStep 2: Setting up NIFTY symbol...")
        if not manager.setup_nifty_symbol():
            return False
        
        # Step 3: Fetch historical data
        logger.info("\nStep 3: Fetching 3-month historical data...")
        if not manager.fetch_3month_historical_data():
            return False
        
        # Step 4: Validate data integrity
        logger.info("\nStep 4: Validating data integrity...")
        if not manager.validate_data_integrity():
            return False
        
        # Step 5: Generate aggregated timeframes
        logger.info("\nStep 5: Generating aggregated timeframes...")
        if not manager.generate_aggregated_timeframes():
            return False
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 NIFTY 3-Month Historical Data Setup COMPLETED!")
        logger.info("✓ All steps executed successfully")
        logger.info("✓ Data is ready for analysis and backtesting")
        
        return True
        
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        return False
    
    finally:
        manager.cleanup()


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
