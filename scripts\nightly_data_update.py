#!/usr/bin/env python3
"""
Nightly Data Update Script
Automated script for nightly historical data updates and maintenance.
"""

import sys
import os
import argparse
import time
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.database.connection import get_db, check_database_connection
from app.services.data_service import DataService
from app.services.symbol_mapping_service import SymbolMappingService

logger = get_logger(__name__)

class NightlyDataUpdater:
    """Manages nightly data updates and maintenance tasks."""
    
    def __init__(self):
        """Initialize the updater."""
        self.db = None
        self.data_service = None
        self.symbol_service = None
        self.report_file = None
        
    def initialize_services(self) -> bool:
        """Initialize all required services."""
        try:
            if not check_database_connection():
                logger.error("Database connection failed")
                return False
            
            self.db = next(get_db())
            self.data_service = DataService(self.db)
            self.symbol_service = SymbolMappingService()
            
            logger.info("✓ Services initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            return False
    
    def create_nightly_report(self) -> Dict:
        """Create nightly data status report."""
        try:
            logger.info("📊 Creating nightly data status report...")
            
            report = {
                "timestamp": datetime.now().isoformat(),
                "database_status": {},
                "symbol_coverage": {},
                "data_quality": {},
                "recommendations": []
            }
            
            # Database status
            try:
                # Get total record count
                total_records = self.db.execute("SELECT COUNT(*) FROM stock_ohlcv").scalar()
                
                # Get symbol count
                symbol_count = self.db.execute("SELECT COUNT(DISTINCT symbol_id) FROM stock_ohlcv").scalar()
                
                # Get date range
                date_range = self.db.execute("""
                    SELECT MIN(timestamp) as min_date, MAX(timestamp) as max_date 
                    FROM stock_ohlcv
                """).fetchone()
                
                report["database_status"] = {
                    "total_records": total_records,
                    "symbols_with_data": symbol_count,
                    "date_range": {
                        "start": date_range.min_date.isoformat() if date_range.min_date else None,
                        "end": date_range.max_date.isoformat() if date_range.max_date else None
                    }
                }
                
            except Exception as e:
                logger.error(f"Error getting database status: {e}")
                report["database_status"]["error"] = str(e)
            
            # Symbol coverage analysis
            try:
                # Get NIFTY 50 coverage
                nifty50_mappings = self.symbol_service.get_nifty50_mappings()
                nifty50_symbols = list(nifty50_mappings.keys())
                
                symbols_with_data = []
                symbols_without_data = []
                
                for symbol in nifty50_symbols:
                    stats = self.data_service.get_data_statistics(symbol)
                    if stats and stats.get('total_records', 0) > 0:
                        symbols_with_data.append({
                            "symbol": symbol,
                            "records": stats['total_records'],
                            "latest_date": stats['data_range']['end'] if stats.get('data_range') else None
                        })
                    else:
                        symbols_without_data.append(symbol)
                
                report["symbol_coverage"] = {
                    "nifty50_total": len(nifty50_symbols),
                    "nifty50_with_data": len(symbols_with_data),
                    "nifty50_without_data": len(symbols_without_data),
                    "coverage_percentage": (len(symbols_with_data) / len(nifty50_symbols)) * 100,
                    "symbols_with_data": symbols_with_data,
                    "symbols_without_data": symbols_without_data
                }
                
            except Exception as e:
                logger.error(f"Error analyzing symbol coverage: {e}")
                report["symbol_coverage"]["error"] = str(e)
            
            # Data quality checks
            try:
                quality_issues = []
                
                # Check for gaps in recent data
                recent_date = datetime.now() - timedelta(days=7)
                gap_check = self.db.execute("""
                    SELECT symbol_id, COUNT(*) as record_count
                    FROM stock_ohlcv 
                    WHERE timestamp >= %s
                    GROUP BY symbol_id
                    HAVING COUNT(*) < 1000  -- Less than expected for a week
                """, (recent_date,)).fetchall()
                
                if gap_check:
                    quality_issues.append({
                        "type": "recent_data_gaps",
                        "description": f"Found {len(gap_check)} symbols with insufficient recent data",
                        "affected_symbols": len(gap_check)
                    })
                
                # Check for stale data
                stale_threshold = datetime.now() - timedelta(days=3)
                stale_check = self.db.execute("""
                    SELECT symbol_id, MAX(timestamp) as latest_timestamp
                    FROM stock_ohlcv
                    GROUP BY symbol_id
                    HAVING MAX(timestamp) < %s
                """, (stale_threshold,)).fetchall()
                
                if stale_check:
                    quality_issues.append({
                        "type": "stale_data",
                        "description": f"Found {len(stale_check)} symbols with stale data (older than 3 days)",
                        "affected_symbols": len(stale_check)
                    })
                
                report["data_quality"] = {
                    "issues_found": len(quality_issues),
                    "issues": quality_issues
                }
                
            except Exception as e:
                logger.error(f"Error checking data quality: {e}")
                report["data_quality"]["error"] = str(e)
            
            # Generate recommendations
            recommendations = []
            
            if report["symbol_coverage"].get("coverage_percentage", 0) < 80:
                recommendations.append("Low NIFTY 50 coverage - consider running bulk data load")
            
            if report["data_quality"].get("issues_found", 0) > 0:
                recommendations.append("Data quality issues detected - review and fix gaps")
            
            if not recommendations:
                recommendations.append("Data status looks good - no immediate action required")
            
            report["recommendations"] = recommendations
            
            return report
            
        except Exception as e:
            logger.error(f"Error creating nightly report: {e}")
            return {"error": str(e)}
    
    def save_report(self, report: Dict, output_dir: str = "reports") -> str:
        """Save report to file."""
        try:
            # Create reports directory
            reports_dir = Path(output_dir)
            reports_dir.mkdir(exist_ok=True)
            
            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"nightly_data_report_{timestamp}.json"
            filepath = reports_dir / filename
            
            # Save report
            with open(filepath, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            logger.info(f"📄 Report saved to: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"Error saving report: {e}")
            return ""
    
    def print_report_summary(self, report: Dict):
        """Print a summary of the report to console."""
        try:
            logger.info("\n" + "=" * 80)
            logger.info("📊 NIGHTLY DATA STATUS REPORT")
            logger.info("=" * 80)
            
            # Database status
            db_status = report.get("database_status", {})
            if "error" not in db_status:
                logger.info(f"Total records: {db_status.get('total_records', 0):,}")
                logger.info(f"Symbols with data: {db_status.get('symbols_with_data', 0)}")
                
                date_range = db_status.get("date_range", {})
                if date_range.get("start") and date_range.get("end"):
                    logger.info(f"Date range: {date_range['start'][:10]} to {date_range['end'][:10]}")
            
            # Symbol coverage
            coverage = report.get("symbol_coverage", {})
            if "error" not in coverage:
                logger.info(f"\nNIFTY 50 Coverage: {coverage.get('nifty50_with_data', 0)}/{coverage.get('nifty50_total', 0)} ({coverage.get('coverage_percentage', 0):.1f}%)")
                
                if coverage.get("symbols_without_data"):
                    logger.info(f"Missing data for: {', '.join(coverage['symbols_without_data'][:5])}")
                    if len(coverage['symbols_without_data']) > 5:
                        logger.info(f"... and {len(coverage['symbols_without_data']) - 5} more")
            
            # Data quality
            quality = report.get("data_quality", {})
            if "error" not in quality:
                issues_count = quality.get("issues_found", 0)
                logger.info(f"\nData quality issues: {issues_count}")
                
                for issue in quality.get("issues", []):
                    logger.info(f"  - {issue['description']}")
            
            # Recommendations
            recommendations = report.get("recommendations", [])
            if recommendations:
                logger.info("\n📋 Recommendations:")
                for i, rec in enumerate(recommendations, 1):
                    logger.info(f"  {i}. {rec}")
            
        except Exception as e:
            logger.error(f"Error printing report summary: {e}")
    
    def run_maintenance_tasks(self) -> bool:
        """Run database maintenance tasks."""
        try:
            logger.info("🔧 Running maintenance tasks...")
            
            # Update table statistics
            logger.info("  - Updating table statistics...")
            self.db.execute("ANALYZE stock_ohlcv")
            
            # Clean up old progress records (older than 30 days)
            logger.info("  - Cleaning up old progress records...")
            cleanup_date = datetime.now() - timedelta(days=30)
            # This would depend on your progress table structure
            
            logger.info("✓ Maintenance tasks completed")
            return True
            
        except Exception as e:
            logger.error(f"Error running maintenance tasks: {e}")
            return False
    
    def cleanup(self):
        """Cleanup resources."""
        if self.db:
            self.db.close()
            logger.info("✓ Database connection closed")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Nightly data update and maintenance")
    parser.add_argument("--report-only", action="store_true",
                       help="Generate report only, skip data loading")
    parser.add_argument("--output-dir", default="reports",
                       help="Output directory for reports (default: reports)")
    parser.add_argument("--maintenance", action="store_true",
                       help="Run maintenance tasks")
    
    args = parser.parse_args()
    
    logger.info("🌙 Nightly Data Update Script")
    logger.info("=" * 80)
    logger.info(f"Started at: {datetime.now()}")
    
    updater = NightlyDataUpdater()
    
    try:
        # Initialize services
        if not updater.initialize_services():
            return False
        
        # Generate nightly report
        logger.info("\nStep 1: Generating nightly data report...")
        report = updater.create_nightly_report()
        
        # Save and display report
        report_file = updater.save_report(report, args.output_dir)
        updater.print_report_summary(report)
        
        # Run maintenance tasks if requested
        if args.maintenance:
            logger.info("\nStep 2: Running maintenance tasks...")
            updater.run_maintenance_tasks()
        
        # If not report-only, could trigger data loading here
        if not args.report_only:
            logger.info("\nStep 3: Data loading...")
            logger.info("💡 To load data, run: python scripts/load_all_symbols_15year_data.py --symbols nifty50")
        
        logger.info(f"\n✅ Nightly update completed at: {datetime.now()}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Nightly update failed: {e}")
        return False
    finally:
        updater.cleanup()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
