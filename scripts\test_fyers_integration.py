#!/usr/bin/env python3
"""
Test script for Fyers API integration.
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import setup_logging
from app.database.connection import get_db
from app.services.market_data_service import MarketDataService
from app.database.models import MarketType

logger = setup_logging(level="INFO")


def test_authentication():
    """Test Fyers authentication."""
    logger.info("Testing Fyers authentication...")
    
    try:
        db = next(get_db())
        market_service = MarketDataService(db)
        
        success = market_service.initialize_fyers_connection()
        
        if success:
            logger.info("✓ Fyers authentication successful")
            
            # Test profile
            profile = market_service.fyers_client.get_profile()
            if profile:
                logger.info(f"✓ Profile retrieved: {profile.get('name', 'Unknown')}")
            else:
                logger.warning("⚠ Failed to retrieve profile")
            
            return True
        else:
            logger.error("✗ Fyers authentication failed")
            return False
            
    except Exception as e:
        logger.error(f"✗ Authentication test failed: {e}")
        return False
    finally:
        db.close()


def test_live_quotes():
    """Test live quotes fetching."""
    logger.info("Testing live quotes...")
    
    try:
        db = next(get_db())
        market_service = MarketDataService(db)
        
        # Initialize connection
        if not market_service.initialize_fyers_connection():
            logger.error("Failed to initialize Fyers connection")
            return False
        
        # Test symbols
        test_symbols = ["NSE:NIFTY50-INDEX", "NSE:BANKNIFTY-INDEX", "NSE:RELIANCE-EQ"]
        
        quotes = market_service.get_live_quotes(test_symbols)
        
        if quotes:
            logger.info(f"✓ Retrieved quotes for {len(quotes)} symbols")
            
            for symbol, quote in quotes.items():
                logger.info(f"  {symbol}: LTP={quote.ltp}, Volume={quote.volume}")
            
            return True
        else:
            logger.error("✗ No quotes retrieved")
            return False
            
    except Exception as e:
        logger.error(f"✗ Live quotes test failed: {e}")
        return False
    finally:
        db.close()


def test_historical_data():
    """Test historical data fetching."""
    logger.info("Testing historical data fetching...")
    
    try:
        db = next(get_db())
        market_service = MarketDataService(db)
        
        # Initialize connection
        if not market_service.initialize_fyers_connection():
            logger.error("Failed to initialize Fyers connection")
            return False
        
        # Create test symbols in database
        test_symbols = [
            ("NIFTY", MarketType.INDEX),
            ("BANKNIFTY", MarketType.INDEX),
            ("RELIANCE", MarketType.EQUITY)
        ]
        
        for symbol_name, market_type in test_symbols:
            symbol_obj = market_service.data_service.get_symbol_by_name(symbol_name)
            if not symbol_obj:
                logger.info(f"Creating symbol: {symbol_name}")
                market_service.create_symbol_from_fyers(symbol_name, market_type)
        
        # Test historical data fetch
        fyers_symbol = "NSE:NIFTY50-INDEX"
        db_symbol = "NIFTY"
        
        success = market_service.fetch_and_store_historical_data(
            fyers_symbol, 
            timeframe="1",  # 1 minute
            days=7  # Last 7 days
        )
        
        if success:
            logger.info("✓ Historical data fetched and stored successfully")
            
            # Verify data in database
            stats = market_service.data_service.get_data_statistics(db_symbol)
            if stats:
                logger.info(f"  Database records: {stats['total_records']}")
                logger.info(f"  Data range: {stats['data_range']['start']} to {stats['data_range']['end']}")
            
            return True
        else:
            logger.error("✗ Historical data fetch failed")
            return False
            
    except Exception as e:
        logger.error(f"✗ Historical data test failed: {e}")
        return False
    finally:
        db.close()


def test_bulk_historical_fetch():
    """Test bulk historical data fetching."""
    logger.info("Testing bulk historical data fetching...")
    
    try:
        db = next(get_db())
        market_service = MarketDataService(db)
        
        # Initialize connection
        if not market_service.initialize_fyers_connection():
            logger.error("Failed to initialize Fyers connection")
            return False
        
        # Test symbols mapping (Fyers format -> DB format)
        symbol_mapping = {
            "NSE:NIFTY50-INDEX": ("NIFTY", MarketType.INDEX),
            "NSE:BANKNIFTY-INDEX": ("BANKNIFTY", MarketType.INDEX),
            "NSE:RELIANCE-EQ": ("RELIANCE", MarketType.EQUITY)
        }
        
        # Create symbols in database
        for fyers_symbol, (db_symbol, market_type) in symbol_mapping.items():
            symbol_obj = market_service.data_service.get_symbol_by_name(db_symbol)
            if not symbol_obj:
                logger.info(f"Creating symbol: {db_symbol}")
                market_service.create_symbol_from_fyers(db_symbol, market_type)
        
        # Bulk fetch
        fyers_symbols = list(symbol_mapping.keys())
        results = market_service.bulk_fetch_historical_data(
            fyers_symbols,
            timeframe="1",
            days=3  # Last 3 days
        )
        
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        
        logger.info(f"✓ Bulk fetch completed: {successful}/{total} symbols successful")
        
        # Show statistics for each symbol
        for fyers_symbol, (db_symbol, _) in symbol_mapping.items():
            if results.get(fyers_symbol):
                stats = market_service.data_service.get_data_statistics(db_symbol)
                if stats:
                    logger.info(f"  {db_symbol}: {stats['total_records']} records")
        
        return successful > 0
        
    except Exception as e:
        logger.error(f"✗ Bulk historical fetch test failed: {e}")
        return False
    finally:
        db.close()


def test_real_time_data():
    """Test real-time data streaming."""
    logger.info("Testing real-time data streaming...")
    
    try:
        db = next(get_db())
        market_service = MarketDataService(db)
        
        # Initialize connection
        if not market_service.initialize_fyers_connection():
            logger.error("Failed to initialize Fyers connection")
            return False
        
        # Add data callback
        received_data = []
        
        def data_callback(symbol, quote_data):
            received_data.append((symbol, quote_data))
            logger.info(f"Real-time data: {symbol} = {quote_data.ltp}")
        
        market_service.add_data_callback(data_callback)
        
        # Start real-time data
        test_symbols = ["NSE:NIFTY50-INDEX", "NSE:BANKNIFTY-INDEX"]
        
        success = market_service.start_real_time_data(test_symbols)
        
        if success:
            logger.info("✓ Real-time data streaming started")
            
            # Wait for some data
            import time
            logger.info("Waiting for real-time data (30 seconds)...")
            time.sleep(30)
            
            # Stop streaming
            market_service.stop_real_time_data()
            
            if received_data:
                logger.info(f"✓ Received {len(received_data)} real-time updates")
                return True
            else:
                logger.warning("⚠ No real-time data received")
                return False
        else:
            logger.error("✗ Failed to start real-time data streaming")
            return False
            
    except Exception as e:
        logger.error(f"✗ Real-time data test failed: {e}")
        return False
    finally:
        db.close()


def main():
    """Main test function."""
    logger.info("=" * 60)
    logger.info("FYERS API INTEGRATION TESTS")
    logger.info("=" * 60)
    
    tests = [
        ("Authentication", test_authentication),
        ("Live Quotes", test_live_quotes),
        ("Historical Data", test_historical_data),
        ("Bulk Historical Fetch", test_bulk_historical_fetch),
        # ("Real-time Data", test_real_time_data),  # Commented out for quick testing
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} Test ---")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "PASS" if success else "FAIL"
        logger.info(f"{test_name}: {status}")
        if success:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
