"""
Real-time paper trading engine with live market data integration.
"""

from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
import asyncio
import json
from dataclasses import dataclass, field
from enum import Enum
import uuid

from app.core.logging import get_logger
from app.services.paper_trading.portfolio import PaperPortfolio
from app.services.paper_trading.orders import PaperOrder, OrderType, OrderStatus, OrderSide
from app.services.paper_trading.risk_manager import RiskManager
from app.services.market_data_service import MarketDataService

logger = get_logger(__name__)


class TradingMode(Enum):
    """Trading modes."""
    MANUAL = "manual"
    AUTOMATED = "automated"
    SEMI_AUTOMATED = "semi_automated"


@dataclass
class TradingSession:
    """Paper trading session configuration."""
    session_id: str
    name: str
    initial_capital: float
    trading_mode: TradingMode
    symbols: List[str]
    strategy_config: Optional[Dict[str, Any]] = None
    risk_config: Optional[Dict[str, Any]] = None
    created_at: datetime = field(default_factory=datetime.now)
    is_active: bool = True
    
    # Performance tracking
    total_pnl: float = 0.0
    realized_pnl: float = 0.0
    unrealized_pnl: float = 0.0
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0


@dataclass
class MarketDataUpdate:
    """Market data update structure."""
    symbol: str
    price: float
    volume: int
    timestamp: datetime
    bid: Optional[float] = None
    ask: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    open: Optional[float] = None


class RealTimePaperTradingEngine:
    """Real-time paper trading engine."""
    
    def __init__(self, session: TradingSession, market_data_service: MarketDataService):
        """Initialize the real-time paper trading engine."""
        self.session = session
        self.market_data_service = market_data_service
        
        # Initialize portfolio
        self.portfolio = PaperPortfolio(session.initial_capital)
        
        # Initialize risk manager
        risk_config = session.risk_config or {}
        self.risk_manager = RiskManager(risk_config)
        
        # Order management
        self.pending_orders: Dict[str, PaperOrder] = {}
        self.order_history: List[PaperOrder] = []
        
        # Market data tracking
        self.current_prices: Dict[str, float] = {}
        self.price_history: Dict[str, List[MarketDataUpdate]] = {}
        
        # Event callbacks
        self.order_callbacks: List[Callable] = []
        self.position_callbacks: List[Callable] = []
        self.pnl_callbacks: List[Callable] = []
        
        # Strategy integration
        self.strategy = None
        self.auto_trading_enabled = session.trading_mode == TradingMode.AUTOMATED
        
        # Performance tracking
        self.equity_curve: List[float] = [session.initial_capital]
        self.trade_log: List[Dict[str, Any]] = []
        
        # State
        self.is_running = False
        self.last_update = datetime.now()
    
    def start(self):
        """Start the paper trading engine."""
        if self.is_running:
            logger.warning(f"Session {self.session.session_id} is already running")
            return
        
        self.is_running = True
        self.session.is_active = True
        logger.info(f"Started paper trading session: {self.session.name}")
        
        # Initialize market data subscriptions
        for symbol in self.session.symbols:
            self.price_history[symbol] = []
    
    def stop(self):
        """Stop the paper trading engine."""
        self.is_running = False
        self.session.is_active = False
        logger.info(f"Stopped paper trading session: {self.session.name}")
    
    def update_market_data(self, market_update: MarketDataUpdate):
        """Update market data and process any triggered orders."""
        if not self.is_running:
            return
        
        symbol = market_update.symbol
        price = market_update.price
        
        # Update current prices
        self.current_prices[symbol] = price
        
        # Store price history
        if symbol in self.price_history:
            self.price_history[symbol].append(market_update)
            # Keep only last 1000 updates
            if len(self.price_history[symbol]) > 1000:
                self.price_history[symbol] = self.price_history[symbol][-1000:]
        
        # Update portfolio values
        self.portfolio.update_market_prices({symbol: price})
        
        # Process pending orders
        self._process_pending_orders(symbol, price)
        
        # Update performance metrics
        self._update_performance_metrics()
        
        # Generate strategy signals if automated
        if self.auto_trading_enabled and self.strategy:
            self._process_strategy_signals(symbol, market_update)
        
        # Notify callbacks
        self._notify_callbacks()
        
        self.last_update = datetime.now()
    
    def place_order(
        self,
        symbol: str,
        side: OrderSide,
        quantity: float,
        order_type: OrderType = OrderType.MARKET,
        price: Optional[float] = None,
        stop_price: Optional[float] = None,
        time_in_force: str = "GTC"
    ) -> str:
        """
        Place a paper trading order.
        
        Args:
            symbol: Symbol to trade
            side: Order side (BUY/SELL)
            quantity: Order quantity
            order_type: Order type
            price: Limit price (for limit orders)
            stop_price: Stop price (for stop orders)
            time_in_force: Time in force
            
        Returns:
            Order ID
        """
        try:
            # Validate order
            if not self.is_running:
                raise ValueError("Trading session is not active")
            
            if symbol not in self.session.symbols:
                raise ValueError(f"Symbol {symbol} not in trading session")
            
            current_price = self.current_prices.get(symbol)
            if current_price is None:
                raise ValueError(f"No market data available for {symbol}")
            
            # Risk management check
            if not self.risk_manager.validate_order(
                symbol, side, quantity, current_price, self.portfolio
            ):
                raise ValueError("Order rejected by risk management")
            
            # Create order
            order = PaperOrder(
                order_id=str(uuid.uuid4()),
                symbol=symbol,
                side=side,
                quantity=quantity,
                order_type=order_type,
                price=price,
                stop_price=stop_price,
                time_in_force=time_in_force,
                timestamp=datetime.now(),
                status=OrderStatus.PENDING
            )
            
            # Process order immediately if market order
            if order_type == OrderType.MARKET:
                self._execute_order(order, current_price)
            else:
                # Add to pending orders
                self.pending_orders[order.order_id] = order
            
            logger.info(f"Order placed: {order.order_id} - {side.value} {quantity} {symbol}")
            return order.order_id
            
        except Exception as e:
            logger.error(f"Error placing order: {e}")
            raise
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel a pending order."""
        try:
            if order_id in self.pending_orders:
                order = self.pending_orders[order_id]
                order.status = OrderStatus.CANCELLED
                order.updated_at = datetime.now()
                
                # Move to history
                self.order_history.append(order)
                del self.pending_orders[order_id]
                
                logger.info(f"Order cancelled: {order_id}")
                return True
            else:
                logger.warning(f"Order not found: {order_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            return False
    
    def _process_pending_orders(self, symbol: str, current_price: float):
        """Process pending orders that may be triggered."""
        orders_to_execute = []
        orders_to_remove = []
        
        for order_id, order in self.pending_orders.items():
            if order.symbol != symbol:
                continue
            
            should_execute = False
            execution_price = current_price
            
            # Check order conditions
            if order.order_type == OrderType.LIMIT:
                if order.side == OrderSide.BUY and current_price <= order.price:
                    should_execute = True
                    execution_price = order.price
                elif order.side == OrderSide.SELL and current_price >= order.price:
                    should_execute = True
                    execution_price = order.price
            
            elif order.order_type == OrderType.STOP:
                if order.side == OrderSide.BUY and current_price >= order.stop_price:
                    should_execute = True
                elif order.side == OrderSide.SELL and current_price <= order.stop_price:
                    should_execute = True
            
            elif order.order_type == OrderType.STOP_LIMIT:
                # Stop triggered, convert to limit order
                if ((order.side == OrderSide.BUY and current_price >= order.stop_price) or
                    (order.side == OrderSide.SELL and current_price <= order.stop_price)):
                    
                    if order.side == OrderSide.BUY and current_price <= order.price:
                        should_execute = True
                        execution_price = order.price
                    elif order.side == OrderSide.SELL and current_price >= order.price:
                        should_execute = True
                        execution_price = order.price
            
            if should_execute:
                orders_to_execute.append((order, execution_price))
                orders_to_remove.append(order_id)
        
        # Execute triggered orders
        for order, execution_price in orders_to_execute:
            self._execute_order(order, execution_price)
        
        # Remove executed orders from pending
        for order_id in orders_to_remove:
            if order_id in self.pending_orders:
                del self.pending_orders[order_id]
    
    def _execute_order(self, order: PaperOrder, execution_price: float):
        """Execute an order."""
        try:
            # Calculate commission (simple model)
            commission = abs(order.quantity * execution_price * 0.001)  # 0.1%
            
            # Execute in portfolio
            success = self.portfolio.execute_order(
                symbol=order.symbol,
                side=order.side,
                quantity=order.quantity,
                price=execution_price,
                commission=commission
            )
            
            if success:
                # Update order status
                order.status = OrderStatus.FILLED
                order.filled_price = execution_price
                order.filled_quantity = order.quantity
                order.commission = commission
                order.updated_at = datetime.now()
                
                # Add to history
                self.order_history.append(order)
                
                # Log trade
                self.trade_log.append({
                    'timestamp': order.updated_at,
                    'order_id': order.order_id,
                    'symbol': order.symbol,
                    'side': order.side.value,
                    'quantity': order.quantity,
                    'price': execution_price,
                    'commission': commission,
                    'portfolio_value': self.portfolio.total_value
                })
                
                # Update session statistics
                self.session.total_trades += 1
                
                logger.info(f"Order executed: {order.order_id} at {execution_price}")
                
            else:
                # Execution failed
                order.status = OrderStatus.REJECTED
                order.updated_at = datetime.now()
                self.order_history.append(order)
                logger.warning(f"Order execution failed: {order.order_id}")
                
        except Exception as e:
            logger.error(f"Error executing order: {e}")
            order.status = OrderStatus.REJECTED
            order.updated_at = datetime.now()
            self.order_history.append(order)
    
    def _process_strategy_signals(self, symbol: str, market_update: MarketDataUpdate):
        """Process strategy signals for automated trading."""
        if not self.strategy:
            return
        
        try:
            # Get recent price data for strategy
            recent_data = self.price_history.get(symbol, [])
            if len(recent_data) < 20:  # Need minimum data
                return
            
            # Convert to format expected by strategy
            price_data = [
                {
                    'timestamp': update.timestamp,
                    'open': update.open or update.price,
                    'high': update.high or update.price,
                    'low': update.low or update.price,
                    'close': update.price,
                    'volume': update.volume
                }
                for update in recent_data[-100:]  # Last 100 data points
            ]
            
            # Generate signals
            signals = self.strategy.generate_signals(price_data)
            
            # Process signals
            if signals.get('buy', False):
                # Calculate position size
                position_size = self._calculate_position_size(symbol, market_update.price)
                if position_size > 0:
                    self.place_order(
                        symbol=symbol,
                        side=OrderSide.BUY,
                        quantity=position_size,
                        order_type=OrderType.MARKET
                    )
            
            elif signals.get('sell', False):
                # Close existing position
                position = self.portfolio.get_position(symbol)
                if position and position.quantity > 0:
                    self.place_order(
                        symbol=symbol,
                        side=OrderSide.SELL,
                        quantity=position.quantity,
                        order_type=OrderType.MARKET
                    )
                    
        except Exception as e:
            logger.error(f"Error processing strategy signals: {e}")
    
    def _calculate_position_size(self, symbol: str, price: float) -> float:
        """Calculate position size based on risk management."""
        # Simple position sizing: 10% of portfolio value
        portfolio_value = self.portfolio.total_value
        target_value = portfolio_value * 0.1
        return target_value / price
    
    def _update_performance_metrics(self):
        """Update performance metrics."""
        current_value = self.portfolio.total_value
        self.equity_curve.append(current_value)
        
        # Update session PnL
        initial_capital = self.session.initial_capital
        self.session.total_pnl = (current_value - initial_capital) / initial_capital
        
        # Calculate realized and unrealized PnL
        self.session.realized_pnl = self.portfolio.realized_pnl / initial_capital
        self.session.unrealized_pnl = self.portfolio.unrealized_pnl / initial_capital
    
    def _notify_callbacks(self):
        """Notify registered callbacks of updates."""
        try:
            for callback in self.order_callbacks:
                callback(self.order_history[-1] if self.order_history else None)
            
            for callback in self.position_callbacks:
                callback(self.portfolio.positions)
            
            for callback in self.pnl_callbacks:
                callback(self.session.total_pnl)
                
        except Exception as e:
            logger.error(f"Error in callback notification: {e}")
    
    def add_order_callback(self, callback: Callable):
        """Add order update callback."""
        self.order_callbacks.append(callback)
    
    def add_position_callback(self, callback: Callable):
        """Add position update callback."""
        self.position_callbacks.append(callback)
    
    def add_pnl_callback(self, callback: Callable):
        """Add PnL update callback."""
        self.pnl_callbacks.append(callback)
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get comprehensive session summary."""
        return {
            'session_id': self.session.session_id,
            'name': self.session.name,
            'is_active': self.is_running,
            'initial_capital': self.session.initial_capital,
            'current_value': self.portfolio.total_value,
            'total_pnl': self.session.total_pnl,
            'realized_pnl': self.session.realized_pnl,
            'unrealized_pnl': self.session.unrealized_pnl,
            'total_trades': self.session.total_trades,
            'winning_trades': self.session.winning_trades,
            'losing_trades': self.session.losing_trades,
            'pending_orders': len(self.pending_orders),
            'positions': len(self.portfolio.positions),
            'last_update': self.last_update.isoformat(),
            'symbols': self.session.symbols,
            'trading_mode': self.session.trading_mode.value
        }
