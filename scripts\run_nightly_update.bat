@echo off
REM Nightly Data Update Batch Script
REM This script runs the nightly data update process

echo ========================================
echo Signal Stack - Nightly Data Update
echo ========================================
echo Started at: %date% %time%

REM Change to the project directory
cd /d "C:\Users\<USER>\Desktop\Python\signal_stack"

REM Activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
)

REM Run nightly report and maintenance
echo.
echo Step 1: Generating nightly report...
python scripts\nightly_data_update.py --maintenance --output-dir reports

REM Check if we should run data loading based on the day of week
REM Run full data loading on weekends (Saturday = 7, Sunday = 1)
for /f "tokens=1" %%i in ('powershell -command "Get-Date -Format 'dddd'"') do set DAYOFWEEK=%%i

if "%DAYOFWEEK%"=="Saturday" (
    echo.
    echo Step 2: Weekend full data loading...
    python scripts\load_all_symbols_15year_data.py --symbols nifty50 --years 2
) else if "%DAYOFWEEK%"=="Sunday" (
    echo.
    echo Step 2: Weekend full data loading...
    python scripts\load_all_symbols_15year_data.py --symbols indices --years 1
) else (
    echo.
    echo Step 2: Weekday - skipping bulk data loading
    echo To manually load data, run:
    echo python scripts\load_all_symbols_15year_data.py --symbols nifty50
)

echo.
echo ========================================
echo Nightly update completed at: %date% %time%
echo ========================================

REM Keep window open if run manually
if "%1"=="" pause
