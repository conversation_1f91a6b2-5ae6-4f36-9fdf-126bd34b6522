"""
Momentum technical indicators.
"""

from typing import Dict, Any, Union, List
import pandas as pd
import numpy as np

from .base import BaseIndicator


class RSI(BaseIndicator):
    """Relative Strength Index indicator."""
    
    def __init__(self, period: int = 14):
        """
        Initialize RSI.
        
        Args:
            period: RSI calculation period
        """
        super().__init__("RSI", {"period": period})
        self.period = period
    
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calculate RSI values."""
        df = self._prepare_data(data)
        
        if len(df) < self.period + 1:
            return {"values": [], "timestamps": []}
        
        # Calculate price changes
        delta = df['close'].diff()
        
        # Separate gains and losses
        gains = delta.where(delta > 0, 0)
        losses = -delta.where(delta < 0, 0)
        
        # Calculate average gains and losses
        avg_gains = gains.rolling(window=self.period).mean()
        avg_losses = losses.rolling(window=self.period).mean()
        
        # Calculate RSI
        rs = avg_gains / avg_losses
        rsi = 100 - (100 / (1 + rs))
        
        # Store values
        self.values = rsi.dropna().tolist()
        self.timestamps = df['timestamp'][self.period:].tolist()
        self.is_ready = len(self.values) > 0
        
        return {
            "values": self.values,
            "timestamps": self.timestamps,
            "period": self.period
        }
    
    def get_signals(self, data: pd.DataFrame) -> Dict[str, List[bool]]:
        """Generate RSI trading signals."""
        result = self.calculate(data)
        
        if not result['values']:
            return {'buy_signals': [], 'sell_signals': []}
        
        rsi_values = np.array(result['values'])
        
        # Buy when RSI crosses above 30 (oversold)
        # Sell when RSI crosses below 70 (overbought)
        buy_signals = []
        sell_signals = []
        
        for i in range(1, len(rsi_values)):
            if rsi_values[i] > 30 and rsi_values[i-1] <= 30:
                buy_signals.append(True)
                sell_signals.append(False)
            elif rsi_values[i] < 70 and rsi_values[i-1] >= 70:
                buy_signals.append(False)
                sell_signals.append(True)
            else:
                buy_signals.append(False)
                sell_signals.append(False)
        
        # Add False for first element
        buy_signals.insert(0, False)
        sell_signals.insert(0, False)
        
        return {
            'buy_signals': buy_signals,
            'sell_signals': sell_signals
        }


class Stochastic(BaseIndicator):
    """Stochastic Oscillator indicator."""
    
    def __init__(self, k_period: int = 14, d_period: int = 3):
        """
        Initialize Stochastic.
        
        Args:
            k_period: %K period
            d_period: %D period (smoothing)
        """
        super().__init__("Stochastic", {"k_period": k_period, "d_period": d_period})
        self.k_period = k_period
        self.d_period = d_period
    
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calculate Stochastic values."""
        df = self._prepare_data(data)
        
        if len(df) < self.k_period:
            return {"k_percent": [], "d_percent": [], "timestamps": []}
        
        # Calculate %K
        lowest_low = df['low'].rolling(window=self.k_period).min()
        highest_high = df['high'].rolling(window=self.k_period).max()
        k_percent = 100 * ((df['close'] - lowest_low) / (highest_high - lowest_low))
        
        # Calculate %D (smoothed %K)
        d_percent = k_percent.rolling(window=self.d_period).mean()
        
        # Store values
        self.values = {
            'k_percent': k_percent.dropna().tolist(),
            'd_percent': d_percent.dropna().tolist()
        }
        
        # Adjust timestamps
        start_idx = self.k_period + self.d_period - 2
        self.timestamps = df['timestamp'][start_idx:].tolist()
        self.is_ready = len(self.values['k_percent']) > 0
        
        return {
            "k_percent": self.values['k_percent'],
            "d_percent": self.values['d_percent'],
            "timestamps": self.timestamps,
            "k_period": self.k_period,
            "d_period": self.d_period
        }


class Williams_R(BaseIndicator):
    """Williams %R indicator."""
    
    def __init__(self, period: int = 14):
        """
        Initialize Williams %R.
        
        Args:
            period: Calculation period
        """
        super().__init__("Williams_R", {"period": period})
        self.period = period
    
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calculate Williams %R values."""
        df = self._prepare_data(data)
        
        if len(df) < self.period:
            return {"values": [], "timestamps": []}
        
        # Calculate Williams %R
        highest_high = df['high'].rolling(window=self.period).max()
        lowest_low = df['low'].rolling(window=self.period).min()
        williams_r = -100 * ((highest_high - df['close']) / (highest_high - lowest_low))
        
        # Store values
        self.values = williams_r.dropna().tolist()
        self.timestamps = df['timestamp'][self.period-1:].tolist()
        self.is_ready = len(self.values) > 0
        
        return {
            "values": self.values,
            "timestamps": self.timestamps,
            "period": self.period
        }


class CCI(BaseIndicator):
    """Commodity Channel Index indicator."""
    
    def __init__(self, period: int = 20):
        """
        Initialize CCI.
        
        Args:
            period: Calculation period
        """
        super().__init__("CCI", {"period": period})
        self.period = period
    
    def calculate(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calculate CCI values."""
        df = self._prepare_data(data)
        
        if len(df) < self.period:
            return {"values": [], "timestamps": []}
        
        # Calculate Typical Price
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        
        # Calculate Simple Moving Average of Typical Price
        sma_tp = typical_price.rolling(window=self.period).mean()
        
        # Calculate Mean Deviation
        mean_deviation = typical_price.rolling(window=self.period).apply(
            lambda x: np.mean(np.abs(x - x.mean()))
        )
        
        # Calculate CCI
        cci = (typical_price - sma_tp) / (0.015 * mean_deviation)
        
        # Store values
        self.values = cci.dropna().tolist()
        self.timestamps = df['timestamp'][self.period-1:].tolist()
        self.is_ready = len(self.values) > 0
        
        return {
            "values": self.values,
            "timestamps": self.timestamps,
            "period": self.period
        }
