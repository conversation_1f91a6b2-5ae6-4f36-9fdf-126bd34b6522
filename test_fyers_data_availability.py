#!/usr/bin/env python3
"""
Test script to check Fyers data availability for different date ranges.
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.integrations.fyers.fyers_client import FyersClient
from app.services.symbol_mapping_service import SymbolMappingService

logger = get_logger(__name__)

def test_data_availability():
    """Test data availability for different date ranges."""
    
    logger.info("🔍 Testing Fyers data availability...")
    
    # Initialize services
    fyers_client = FyersClient()
    symbol_service = SymbolMappingService()
    
    # Authenticate
    logger.info("Authenticating with Fyers...")
    if not fyers_client.authenticate():
        logger.error("❌ Failed to authenticate with <PERSON>yers")
        return False
    
    logger.info("✅ Fyers authentication successful")
    
    # Get NIFTY symbol mapping
    logger.info("Getting NIFTY symbol mapping...")
    nifty_mapping = symbol_service.get_symbol_mapping("NIFTY")
    if not nifty_mapping:
        logger.error("❌ Could not find NIFTY symbol mapping")
        return False
    
    logger.info(f"✅ Found NIFTY mapping: {nifty_mapping.fyers_symbol}")
    
    # Test different date ranges
    test_ranges = [
        ("Recent 7 days", 7),
        ("Recent 30 days", 30),
        ("Recent 90 days", 90),
        ("Recent 1 year", 365),
        ("Recent 2 years", 730),
        ("Recent 5 years", 1825),
        ("Recent 10 years", 3650),
        ("Recent 15 years", 5475),
    ]
    
    for range_name, days in test_ranges:
        logger.info(f"\n📊 Testing {range_name} ({days} days)...")
        
        try:
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            logger.info(f"   Date range: {start_date.date()} to {end_date.date()}")
            
            # Fetch data
            ohlc_data = fyers_client.get_historical_data(
                symbol=nifty_mapping.fyers_symbol,
                interval=1,  # 1-minute data
                days_to_fetch=days
            )
            
            if ohlc_data:
                logger.info(f"   ✅ Success: {len(ohlc_data)} records")
                
                # Show first and last data points
                first_record = ohlc_data[0]
                last_record = ohlc_data[-1]
                
                first_dt = datetime.fromtimestamp(first_record.timestamp)
                last_dt = datetime.fromtimestamp(last_record.timestamp)
                
                logger.info(f"   📈 First record: {first_dt} - Price: ₹{first_record.close}")
                logger.info(f"   📈 Last record: {last_dt} - Price: ₹{last_record.close}")
                
                # If this is the first successful range, we can stop here for now
                if range_name == "Recent 7 days":
                    logger.info(f"\n✅ Data is available! Continuing with other ranges...")
                
            else:
                logger.warning(f"   ❌ No data available for {range_name}")
                
        except Exception as e:
            logger.error(f"   ❌ Error testing {range_name}: {e}")
    
    return True

if __name__ == "__main__":
    success = test_data_availability()
    sys.exit(0 if success else 1)
