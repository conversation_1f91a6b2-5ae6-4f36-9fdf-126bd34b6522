"""
Timeframe aggregation service for converting 1-minute data to higher timeframes.
"""

from typing import List, Dict, Optional, Tu<PERSON>
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import text, and_
import pandas as pd
import numpy as np

from app.core.logging import get_logger
from app.database.repositories.ohlcv_repository import OHLCVRepository, OHLCVAggRepository
from app.database.repositories.symbol_repository import SymbolRepository
from app.database.models import StockOH<PERSON>V, StockOHLCVAgg

logger = get_logger(__name__)


class AggregationService:
    """Service for timeframe aggregation operations."""
    
    # Supported timeframes and their minute intervals
    TIMEFRAMES = {
        '5m': 5,
        '10m': 10,
        '15m': 15,
        '30m': 30,
        '1h': 60,
        '2h': 120,
        '4h': 240,
        '1d': 1440  # 24 * 60 minutes
    }
    
    def __init__(self, db: Session):
        """
        Initialize aggregation service.
        
        Args:
            db: Database session
        """
        self.db = db
        self.symbol_repo = SymbolRepository(db)
        self.ohlcv_repo = OHLCVRepository(db)
        self.ohlcv_agg_repo = OHLCVAggRepository(db)
    
    def aggregate_symbol_data(
        self,
        symbol: str,
        timeframe: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        force_recalculate: bool = False
    ) -> bool:
        """
        Aggregate data for a specific symbol and timeframe.
        
        Args:
            symbol: Symbol name
            timeframe: Target timeframe (5m, 15m, 30m, 1h, 1d)
            start_time: Start time for aggregation (if None, uses last aggregated time)
            end_time: End time for aggregation (if None, uses current time)
            force_recalculate: Whether to recalculate existing aggregations
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Validate timeframe
            if timeframe not in self.TIMEFRAMES:
                logger.error(f"Unsupported timeframe: {timeframe}")
                return False
            
            # Get symbol
            symbol_obj = self.symbol_repo.get_by_symbol(symbol)
            if not symbol_obj:
                logger.error(f"Symbol not found: {symbol}")
                return False
            
            # Determine time range
            if start_time is None:
                start_time = self._get_last_aggregated_time(symbol_obj.id, timeframe)
                if start_time is None:
                    # If no previous aggregation, start from first available data
                    data_range = self.ohlcv_repo.get_data_range(symbol_obj.id)
                    if data_range:
                        start_time = data_range[0]
                    else:
                        logger.warning(f"No data available for {symbol}")
                        return False
            
            if end_time is None:
                end_time = datetime.utcnow()
            
            logger.info(f"Aggregating {symbol} data for {timeframe} "
                       f"from {start_time} to {end_time}")
            
            # Use stored procedure for efficient aggregation
            if self._use_stored_procedure_aggregation(
                symbol_obj.id, timeframe, start_time, end_time
            ):
                logger.info(f"Successfully aggregated {symbol} data using stored procedure")
                return True
            
            # Fallback to Python-based aggregation
            return self._python_based_aggregation(
                symbol_obj.id, timeframe, start_time, end_time, force_recalculate
            )
            
        except Exception as e:
            logger.error(f"Error aggregating data for {symbol}: {e}")
            return False
    
    def aggregate_all_symbols(
        self,
        timeframes: Optional[List[str]] = None,
        hours_back: int = 24
    ) -> Dict[str, Dict[str, bool]]:
        """
        Aggregate data for all active symbols.
        
        Args:
            timeframes: List of timeframes to aggregate (if None, uses all)
            hours_back: Number of hours to look back for aggregation
            
        Returns:
            Dictionary with symbol -> timeframe -> success status
        """
        if timeframes is None:
            timeframes = list(self.TIMEFRAMES.keys())
        
        results = {}
        
        # Get all active symbols
        symbols = self.symbol_repo.get_active_symbols()
        
        logger.info(f"Starting aggregation for {len(symbols)} symbols, "
                   f"{len(timeframes)} timeframes")
        
        for symbol_obj in symbols:
            symbol_name = symbol_obj.symbol
            results[symbol_name] = {}
            
            logger.info(f"Processing symbol: {symbol_name}")
            
            for timeframe in timeframes:
                try:
                    # Calculate start time
                    start_time = datetime.utcnow() - timedelta(hours=hours_back)
                    
                    success = self.aggregate_symbol_data(
                        symbol_name, timeframe, start_time
                    )
                    
                    results[symbol_name][timeframe] = success
                    
                    if success:
                        logger.debug(f"✓ {symbol_name} - {timeframe}")
                    else:
                        logger.warning(f"✗ {symbol_name} - {timeframe}")
                        
                except Exception as e:
                    logger.error(f"Error aggregating {symbol_name} - {timeframe}: {e}")
                    results[symbol_name][timeframe] = False
        
        # Summary
        total_operations = len(symbols) * len(timeframes)
        successful_operations = sum(
            1 for symbol_results in results.values()
            for success in symbol_results.values()
            if success
        )
        
        logger.info(f"Aggregation completed: {successful_operations}/{total_operations} successful")
        
        return results
    
    def get_aggregated_data_as_dataframe(
        self,
        symbol: str,
        timeframe: str,
        start_time: datetime,
        end_time: datetime
    ) -> pd.DataFrame:
        """
        Get aggregated data as pandas DataFrame.
        
        Args:
            symbol: Symbol name
            timeframe: Timeframe
            start_time: Start time
            end_time: End time
            
        Returns:
            DataFrame with aggregated OHLCV data
        """
        try:
            symbol_obj = self.symbol_repo.get_by_symbol(symbol)
            if not symbol_obj:
                logger.error(f"Symbol not found: {symbol}")
                return pd.DataFrame()
            
            # Get aggregated data
            agg_data = self.ohlcv_agg_repo.get_aggregated_data(
                symbol_obj.id, timeframe, start_time, end_time
            )
            
            if not agg_data:
                logger.warning(f"No aggregated data found for {symbol} - {timeframe}")
                return pd.DataFrame()
            
            # Convert to DataFrame
            data_dict = {
                'timestamp': [d.timestamp for d in agg_data],
                'open': [float(d.open) for d in agg_data],
                'high': [float(d.high) for d in agg_data],
                'low': [float(d.low) for d in agg_data],
                'close': [float(d.close) for d in agg_data],
                'volume': [int(d.volume) for d in agg_data]
            }
            
            df = pd.DataFrame(data_dict)
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            return df
            
        except Exception as e:
            logger.error(f"Error getting aggregated data as DataFrame: {e}")
            return pd.DataFrame()
    
    def _get_last_aggregated_time(self, symbol_id: int, timeframe: str) -> Optional[datetime]:
        """Get the last aggregated timestamp for a symbol and timeframe."""
        try:
            query = text("""
                SELECT MAX(timestamp) 
                FROM stock_ohlcv_agg 
                WHERE symbol_id = :symbol_id AND timeframe = :timeframe
            """)
            
            result = self.db.execute(query, {
                'symbol_id': symbol_id,
                'timeframe': timeframe
            }).scalar()
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting last aggregated time: {e}")
            return None
    
    def _use_stored_procedure_aggregation(
        self,
        symbol_id: int,
        timeframe: str,
        start_time: datetime,
        end_time: datetime
    ) -> bool:
        """Use PostgreSQL stored procedure for aggregation."""
        try:
            # Use the stored procedure created in init_db.py
            self.ohlcv_agg_repo.aggregate_data_using_procedure(
                symbol_id, timeframe, start_time, end_time
            )
            return True
            
        except Exception as e:
            logger.error(f"Stored procedure aggregation failed: {e}")
            return False
    
    def _python_based_aggregation(
        self,
        symbol_id: int,
        timeframe: str,
        start_time: datetime,
        end_time: datetime,
        force_recalculate: bool = False
    ) -> bool:
        """Python-based aggregation as fallback."""
        try:
            logger.info(f"Using Python-based aggregation for timeframe {timeframe}")
            
            # Get 1-minute data
            raw_data = self.ohlcv_repo.get_ohlcv_data(
                symbol_id, start_time, end_time
            )
            
            if not raw_data:
                logger.warning("No raw data available for aggregation")
                return False
            
            # Convert to DataFrame for easier manipulation
            df_data = {
                'timestamp': [d.timestamp for d in raw_data],
                'open': [float(d.open) for d in raw_data],
                'high': [float(d.high) for d in raw_data],
                'low': [float(d.low) for d in raw_data],
                'close': [float(d.close) for d in raw_data],
                'volume': [int(d.volume) for d in raw_data]
            }
            
            df = pd.DataFrame(df_data)
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            # Resample based on timeframe
            interval_minutes = self.TIMEFRAMES[timeframe]
            
            if interval_minutes < 1440:  # Less than 1 day
                freq = f'{interval_minutes}T'  # T for minutes
            else:  # Daily
                freq = 'D'
            
            # Aggregate using pandas resample
            agg_df = df.resample(freq).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()
            
            # Convert back to storage format
            agg_data = []
            for timestamp, row in agg_df.iterrows():
                agg_data.append({
                    'timestamp': timestamp,
                    'open': float(row['open']),
                    'high': float(row['high']),
                    'low': float(row['low']),
                    'close': float(row['close']),
                    'volume': int(row['volume'])
                })
            
            # Store aggregated data
            if agg_data:
                count = self.ohlcv_agg_repo.insert_aggregated_data(
                    symbol_id, timeframe, agg_data
                )
                logger.info(f"Stored {count} aggregated records for timeframe {timeframe}")
                return True
            else:
                logger.warning("No aggregated data to store")
                return False
                
        except Exception as e:
            logger.error(f"Python-based aggregation failed: {e}")
            return False
    
    def cleanup_old_aggregated_data(self, days_to_keep: int = 365) -> Dict[str, int]:
        """
        Clean up old aggregated data.
        
        Args:
            days_to_keep: Number of days to keep
            
        Returns:
            Dictionary with cleanup statistics
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
            
            # Count records to be deleted
            count_query = text("""
                SELECT COUNT(*) FROM stock_ohlcv_agg 
                WHERE timestamp < :cutoff_date
            """)
            
            count_to_delete = self.db.execute(count_query, {
                'cutoff_date': cutoff_date
            }).scalar()
            
            # Delete old records
            delete_query = text("""
                DELETE FROM stock_ohlcv_agg 
                WHERE timestamp < :cutoff_date
            """)
            
            self.db.execute(delete_query, {'cutoff_date': cutoff_date})
            self.db.commit()
            
            logger.info(f"Cleaned up {count_to_delete} old aggregated records")
            
            return {
                'deleted_records': count_to_delete,
                'cutoff_date': cutoff_date,
                'days_kept': days_to_keep
            }
            
        except Exception as e:
            logger.error(f"Error cleaning up old aggregated data: {e}")
            return {'deleted_records': 0, 'error': str(e)}
    
    def get_aggregation_statistics(self) -> Dict[str, any]:
        """Get aggregation statistics."""
        try:
            stats = {}
            
            # Get record counts by timeframe
            for timeframe in self.TIMEFRAMES.keys():
                count_query = text("""
                    SELECT COUNT(*) FROM stock_ohlcv_agg 
                    WHERE timeframe = :timeframe
                """)
                
                count = self.db.execute(count_query, {
                    'timeframe': timeframe
                }).scalar()
                
                stats[f'{timeframe}_records'] = count
            
            # Get latest aggregation times
            latest_query = text("""
                SELECT timeframe, MAX(timestamp) as latest_time
                FROM stock_ohlcv_agg 
                GROUP BY timeframe
            """)
            
            latest_times = {}
            for row in self.db.execute(latest_query):
                latest_times[row.timeframe] = row.latest_time
            
            stats['latest_aggregations'] = latest_times
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting aggregation statistics: {e}")
            return {'error': str(e)}
