#!/usr/bin/env python3
"""
Complete platform startup script for Signal Stack trading platform.
Launches both backend and frontend services.
"""

import sys
import os
import subprocess
import time
import threading
import signal
from pathlib import Path
import argparse
import webbrowser

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


class PlatformLauncher:
    """Platform launcher for Signal Stack."""
    
    def __init__(self):
        """Initialize the launcher."""
        self.backend_process = None
        self.frontend_process = None
        self.processes = []
        self.shutdown_event = threading.Event()
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        print(f"\n🛑 Received signal {signum}, shutting down...")
        self.shutdown()
        sys.exit(0)
    
    def check_prerequisites(self):
        """Check all prerequisites."""
        print("🔍 Checking prerequisites...")
        
        # Check Python
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 9):
            print(f"✗ Python 3.9+ required, found {python_version.major}.{python_version.minor}")
            return False
        print(f"✓ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # Check Node.js
        try:
            result = subprocess.run(["node", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ Node.js {result.stdout.strip()}")
            else:
                print("✗ Node.js not found")
                return False
        except FileNotFoundError:
            print("✗ Node.js not installed")
            return False
        
        # Check PostgreSQL
        try:
            result = subprocess.run(["psql", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ PostgreSQL available")
            else:
                print("⚠️  PostgreSQL not found (may still work with remote DB)")
        except FileNotFoundError:
            print("⚠️  PostgreSQL not found (may still work with remote DB)")
        
        # Check required files
        required_files = [
            "config.yaml",
            "requirements.txt",
            "frontend/package.json"
        ]
        
        for file_path in required_files:
            full_path = project_root / file_path
            if full_path.exists():
                print(f"✓ {file_path}")
            else:
                print(f"✗ {file_path} not found")
                return False
        
        return True
    
    def install_backend_dependencies(self):
        """Install backend dependencies."""
        print("📦 Installing backend dependencies...")
        
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], cwd=project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ Backend dependencies installed")
                return True
            else:
                print(f"✗ Failed to install backend dependencies: {result.stderr}")
                return False
        except Exception as e:
            print(f"✗ Error installing backend dependencies: {e}")
            return False
    
    def install_frontend_dependencies(self):
        """Install frontend dependencies."""
        print("📦 Installing frontend dependencies...")
        
        frontend_dir = project_root / "frontend"
        
        try:
            result = subprocess.run([
                "npm", "install"
            ], cwd=frontend_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ Frontend dependencies installed")
                return True
            else:
                print(f"✗ Failed to install frontend dependencies: {result.stderr}")
                return False
        except Exception as e:
            print(f"✗ Error installing frontend dependencies: {e}")
            return False
    
    def setup_database(self):
        """Setup database if needed."""
        print("🗄️  Setting up database...")
        
        try:
            # Check if database setup script exists
            setup_script = project_root / "scripts" / "setup_database.py"
            if setup_script.exists():
                result = subprocess.run([
                    sys.executable, str(setup_script)
                ], cwd=project_root, capture_output=True, text=True)
                
                if result.returncode == 0:
                    print("✓ Database setup completed")
                    return True
                else:
                    print(f"⚠️  Database setup warning: {result.stderr}")
                    return True  # Continue anyway
            else:
                print("⚠️  Database setup script not found, skipping")
                return True
        except Exception as e:
            print(f"⚠️  Database setup error: {e}")
            return True  # Continue anyway
    
    def start_backend(self, host="127.0.0.1", port=8000):
        """Start the backend server."""
        print(f"🚀 Starting backend server on {host}:{port}...")
        
        try:
            self.backend_process = subprocess.Popen([
                "uvicorn", "app.main:app",
                "--host", host,
                "--port", str(port),
                "--reload"
            ], cwd=project_root)
            
            self.processes.append(self.backend_process)
            
            # Wait a moment for server to start
            time.sleep(3)
            
            # Check if process is still running
            if self.backend_process.poll() is None:
                print(f"✓ Backend server started (PID: {self.backend_process.pid})")
                return True
            else:
                print("✗ Backend server failed to start")
                return False
                
        except Exception as e:
            print(f"✗ Error starting backend: {e}")
            return False
    
    def start_frontend(self, port=3000):
        """Start the frontend development server."""
        print(f"🚀 Starting frontend server on port {port}...")
        
        frontend_dir = project_root / "frontend"
        
        try:
            # Set environment variables
            env = os.environ.copy()
            env['PORT'] = str(port)
            env['BROWSER'] = 'none'  # Don't auto-open browser
            
            self.frontend_process = subprocess.Popen([
                "npm", "start"
            ], cwd=frontend_dir, env=env)
            
            self.processes.append(self.frontend_process)
            
            # Wait a moment for server to start
            time.sleep(5)
            
            # Check if process is still running
            if self.frontend_process.poll() is None:
                print(f"✓ Frontend server started (PID: {self.frontend_process.pid})")
                return True
            else:
                print("✗ Frontend server failed to start")
                return False
                
        except Exception as e:
            print(f"✗ Error starting frontend: {e}")
            return False
    
    def wait_for_backend(self, host="127.0.0.1", port=8000, timeout=30):
        """Wait for backend to be ready."""
        print("⏳ Waiting for backend to be ready...")
        
        import requests
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"http://{host}:{port}/health", timeout=2)
                if response.status_code == 200:
                    print("✓ Backend is ready")
                    return True
            except:
                pass
            
            time.sleep(1)
        
        print("⚠️  Backend health check timeout")
        return False
    
    def wait_for_frontend(self, port=3000, timeout=60):
        """Wait for frontend to be ready."""
        print("⏳ Waiting for frontend to be ready...")
        
        import requests
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"http://localhost:{port}", timeout=2)
                if response.status_code == 200:
                    print("✓ Frontend is ready")
                    return True
            except:
                pass
            
            time.sleep(2)
        
        print("⚠️  Frontend readiness check timeout")
        return False
    
    def open_browser(self, url="http://localhost:3000"):
        """Open browser to the application."""
        try:
            print(f"🌐 Opening browser to {url}")
            webbrowser.open(url)
        except Exception as e:
            print(f"⚠️  Could not open browser: {e}")
            print(f"Please manually open: {url}")
    
    def monitor_processes(self):
        """Monitor running processes."""
        print("👀 Monitoring processes... (Press Ctrl+C to stop)")
        
        try:
            while not self.shutdown_event.is_set():
                # Check backend
                if self.backend_process and self.backend_process.poll() is not None:
                    print("⚠️  Backend process stopped unexpectedly")
                    break
                
                # Check frontend
                if self.frontend_process and self.frontend_process.poll() is not None:
                    print("⚠️  Frontend process stopped unexpectedly")
                    break
                
                time.sleep(5)
                
        except KeyboardInterrupt:
            pass
    
    def shutdown(self):
        """Shutdown all processes."""
        print("🛑 Shutting down platform...")
        
        self.shutdown_event.set()
        
        for process in self.processes:
            if process and process.poll() is None:
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                except Exception as e:
                    print(f"Error stopping process: {e}")
        
        print("✓ Platform shutdown complete")
    
    def run(self, backend_host="127.0.0.1", backend_port=8000, frontend_port=3000, 
            skip_deps=False, skip_browser=False):
        """Run the complete platform."""
        try:
            # Check prerequisites
            if not self.check_prerequisites():
                print("❌ Prerequisites check failed")
                return False
            
            # Install dependencies
            if not skip_deps:
                if not self.install_backend_dependencies():
                    return False
                
                if not self.install_frontend_dependencies():
                    return False
            
            # Setup database
            self.setup_database()
            
            # Start backend
            if not self.start_backend(backend_host, backend_port):
                return False
            
            # Wait for backend to be ready
            self.wait_for_backend(backend_host, backend_port)
            
            # Start frontend
            if not self.start_frontend(frontend_port):
                return False
            
            # Wait for frontend to be ready
            if self.wait_for_frontend(frontend_port):
                if not skip_browser:
                    self.open_browser(f"http://localhost:{frontend_port}")
            
            print("\n" + "="*60)
            print("🎉 Signal Stack Platform is running!")
            print("="*60)
            print(f"📊 Frontend: http://localhost:{frontend_port}")
            print(f"🔧 Backend API: http://{backend_host}:{backend_port}")
            print(f"📚 API Docs: http://{backend_host}:{backend_port}/docs")
            print("="*60)
            
            # Monitor processes
            self.monitor_processes()
            
            return True
            
        except Exception as e:
            print(f"❌ Platform startup failed: {e}")
            return False
        finally:
            self.shutdown()


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Signal Stack Platform Launcher")
    parser.add_argument("--backend-host", default="127.0.0.1", help="Backend host")
    parser.add_argument("--backend-port", type=int, default=8000, help="Backend port")
    parser.add_argument("--frontend-port", type=int, default=3000, help="Frontend port")
    parser.add_argument("--skip-deps", action="store_true", help="Skip dependency installation")
    parser.add_argument("--skip-browser", action="store_true", help="Don't open browser automatically")
    
    args = parser.parse_args()
    
    launcher = PlatformLauncher()
    success = launcher.run(
        backend_host=args.backend_host,
        backend_port=args.backend_port,
        frontend_port=args.frontend_port,
        skip_deps=args.skip_deps,
        skip_browser=args.skip_browser
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
