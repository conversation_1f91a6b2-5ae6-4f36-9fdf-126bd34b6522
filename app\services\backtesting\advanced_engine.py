"""
Advanced backtesting engine with enhanced features.
"""

from typing import Dict, Any, List, Optional, Callable, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from dataclasses import dataclass, field
from enum import Enum
import json

from app.core.logging import get_logger
from .strategy import BaseStrategy
from .portfolio import Portfolio
from .orders import Order, OrderType, OrderStatus
from .metrics import PerformanceMetrics

logger = get_logger(__name__)


class BacktestMode(Enum):
    """Backtesting modes."""
    VECTORIZED = "vectorized"
    EVENT_DRIVEN = "event_driven"
    MONTE_CARLO = "monte_carlo"


@dataclass
class BacktestConfig:
    """Backtesting configuration."""
    initial_capital: float = 100000.0
    commission: float = 0.001  # 0.1%
    slippage: float = 0.0005   # 0.05%
    margin_requirement: float = 0.2  # 20% for leveraged positions
    max_leverage: float = 1.0  # No leverage by default
    risk_free_rate: float = 0.05  # 5% annual risk-free rate
    
    # Position sizing
    position_sizing_method: str = "fixed_amount"  # fixed_amount, fixed_percent, kelly, volatility_target
    position_size: float = 10000.0  # Amount or percentage
    max_position_size: float = 0.1  # Maximum 10% of portfolio per position
    
    # Risk management
    max_drawdown_limit: float = 0.2  # Stop trading if drawdown exceeds 20%
    daily_loss_limit: float = 0.05   # Stop trading if daily loss exceeds 5%
    
    # Execution settings
    execution_delay: int = 0  # Bars delay for order execution
    price_impact: float = 0.0  # Market impact as percentage of price
    
    # Data settings
    benchmark_symbol: str = "NIFTY"
    warm_up_period: int = 100  # Bars for indicator warm-up


@dataclass
class BacktestResult:
    """Comprehensive backtest results."""
    # Basic info
    strategy_name: str
    symbol: str
    start_date: datetime
    end_date: datetime
    duration_days: int
    
    # Performance metrics
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    max_drawdown_duration: int
    calmar_ratio: float
    
    # Trading statistics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    
    # Risk metrics
    var_95: float  # Value at Risk (95%)
    cvar_95: float  # Conditional Value at Risk (95%)
    beta: float
    alpha: float
    
    # Portfolio evolution
    equity_curve: List[float] = field(default_factory=list)
    drawdown_curve: List[float] = field(default_factory=list)
    trade_log: List[Dict[str, Any]] = field(default_factory=list)
    
    # Additional metrics
    monthly_returns: Dict[str, float] = field(default_factory=dict)
    yearly_returns: Dict[str, float] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'strategy_name': self.strategy_name,
            'symbol': self.symbol,
            'start_date': self.start_date.isoformat(),
            'end_date': self.end_date.isoformat(),
            'duration_days': self.duration_days,
            'total_return': self.total_return,
            'annualized_return': self.annualized_return,
            'volatility': self.volatility,
            'sharpe_ratio': self.sharpe_ratio,
            'sortino_ratio': self.sortino_ratio,
            'max_drawdown': self.max_drawdown,
            'max_drawdown_duration': self.max_drawdown_duration,
            'calmar_ratio': self.calmar_ratio,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': self.win_rate,
            'avg_win': self.avg_win,
            'avg_loss': self.avg_loss,
            'profit_factor': self.profit_factor,
            'var_95': self.var_95,
            'cvar_95': self.cvar_95,
            'beta': self.beta,
            'alpha': self.alpha,
            'monthly_returns': self.monthly_returns,
            'yearly_returns': self.yearly_returns
        }


class AdvancedBacktestEngine:
    """Advanced backtesting engine with comprehensive features."""
    
    def __init__(self, config: BacktestConfig = None):
        """Initialize the advanced backtest engine."""
        self.config = config or BacktestConfig()
        self.portfolio = Portfolio(self.config.initial_capital)
        self.metrics = PerformanceMetrics()
        
        # State tracking
        self.current_bar = 0
        self.current_date = None
        self.data = None
        self.benchmark_data = None
        
        # Results tracking
        self.equity_curve = []
        self.drawdown_curve = []
        self.trade_log = []
        self.daily_returns = []
        self.benchmark_returns = []
        
        # Risk management
        self.max_drawdown_reached = False
        self.daily_loss_exceeded = False
        
    def run_backtest(
        self,
        strategy: BaseStrategy,
        data: pd.DataFrame,
        benchmark_data: Optional[pd.DataFrame] = None
    ) -> BacktestResult:
        """
        Run comprehensive backtest.
        
        Args:
            strategy: Trading strategy to test
            data: OHLCV data for backtesting
            benchmark_data: Benchmark data for comparison
            
        Returns:
            Comprehensive backtest results
        """
        try:
            logger.info(f"Starting advanced backtest for {strategy.name}")
            
            # Initialize
            self._initialize_backtest(strategy, data, benchmark_data)
            
            # Run backtest based on mode
            if hasattr(strategy, 'vectorized') and strategy.vectorized:
                self._run_vectorized_backtest(strategy)
            else:
                self._run_event_driven_backtest(strategy)
            
            # Calculate final results
            results = self._calculate_results(strategy)
            
            logger.info(f"Backtest completed. Total return: {results.total_return:.2%}")
            return results
            
        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            raise
    
    def _initialize_backtest(
        self,
        strategy: BaseStrategy,
        data: pd.DataFrame,
        benchmark_data: Optional[pd.DataFrame]
    ):
        """Initialize backtest state."""
        self.data = data.copy()
        self.benchmark_data = benchmark_data
        self.current_bar = 0
        self.current_date = self.data.index[0] if not self.data.empty else datetime.now()
        
        # Reset portfolio
        self.portfolio = Portfolio(self.config.initial_capital)
        
        # Reset tracking
        self.equity_curve = [self.config.initial_capital]
        self.drawdown_curve = [0.0]
        self.trade_log = []
        self.daily_returns = []
        self.benchmark_returns = []
        
        # Initialize strategy
        strategy.initialize(self.data, self.config)
    
    def _run_event_driven_backtest(self, strategy: BaseStrategy):
        """Run event-driven backtest."""
        for i in range(len(self.data)):
            self.current_bar = i
            self.current_date = self.data.index[i]
            
            # Check risk management
            if self._check_risk_limits():
                logger.warning("Risk limits exceeded, stopping backtest")
                break
            
            # Get current market data
            current_data = self.data.iloc[:i+1]
            
            # Generate signals
            signals = strategy.generate_signals(current_data)
            
            # Process signals and execute orders
            self._process_signals(signals, self.data.iloc[i])
            
            # Update portfolio value
            self._update_portfolio_value(self.data.iloc[i])
            
            # Track performance
            self._track_performance()
    
    def _run_vectorized_backtest(self, strategy: BaseStrategy):
        """Run vectorized backtest for faster execution."""
        # Generate all signals at once
        signals = strategy.generate_signals(self.data)
        
        # Process signals sequentially
        for i in range(len(self.data)):
            self.current_bar = i
            self.current_date = self.data.index[i]
            
            if self._check_risk_limits():
                break
            
            # Get signal for current bar
            if i < len(signals):
                current_signals = {k: v[i] if isinstance(v, list) and i < len(v) else v 
                                 for k, v in signals.items()}
                self._process_signals(current_signals, self.data.iloc[i])
            
            self._update_portfolio_value(self.data.iloc[i])
            self._track_performance()
    
    def _process_signals(self, signals: Dict[str, Any], market_data: pd.Series):
        """Process trading signals and execute orders."""
        symbol = market_data.name if hasattr(market_data, 'name') else 'SYMBOL'
        current_price = market_data['close']
        
        # Apply slippage and commission
        buy_price = current_price * (1 + self.config.slippage)
        sell_price = current_price * (1 - self.config.slippage)
        
        # Process buy signals
        if signals.get('buy', False):
            position_size = self._calculate_position_size(current_price)
            if position_size > 0:
                order = Order(
                    symbol=symbol,
                    order_type=OrderType.MARKET,
                    quantity=position_size,
                    price=buy_price,
                    timestamp=self.current_date
                )
                self._execute_order(order)
        
        # Process sell signals
        if signals.get('sell', False):
            current_position = self.portfolio.get_position(symbol)
            if current_position and current_position.quantity > 0:
                order = Order(
                    symbol=symbol,
                    order_type=OrderType.MARKET,
                    quantity=-current_position.quantity,
                    price=sell_price,
                    timestamp=self.current_date
                )
                self._execute_order(order)
    
    def _calculate_position_size(self, price: float) -> float:
        """Calculate position size based on configuration."""
        available_capital = self.portfolio.cash
        
        if self.config.position_sizing_method == "fixed_amount":
            return min(self.config.position_size / price, 
                      available_capital * self.config.max_position_size / price)
        
        elif self.config.position_sizing_method == "fixed_percent":
            portfolio_value = self.portfolio.total_value
            target_amount = portfolio_value * self.config.position_size
            return min(target_amount / price,
                      available_capital * self.config.max_position_size / price)
        
        else:
            # Default to fixed amount
            return min(self.config.position_size / price,
                      available_capital * self.config.max_position_size / price)
    
    def _execute_order(self, order: Order):
        """Execute an order and update portfolio."""
        # Calculate commission
        commission = abs(order.quantity * order.price * self.config.commission)
        
        # Execute order
        success = self.portfolio.execute_order(order, commission)
        
        if success:
            # Log trade
            self.trade_log.append({
                'timestamp': order.timestamp,
                'symbol': order.symbol,
                'side': 'BUY' if order.quantity > 0 else 'SELL',
                'quantity': abs(order.quantity),
                'price': order.price,
                'commission': commission,
                'portfolio_value': self.portfolio.total_value
            })
    
    def _update_portfolio_value(self, market_data: pd.Series):
        """Update portfolio value with current market prices."""
        symbol = market_data.name if hasattr(market_data, 'name') else 'SYMBOL'
        current_price = market_data['close']
        
        # Update position values
        position = self.portfolio.get_position(symbol)
        if position:
            position.current_price = current_price
        
        # Update total portfolio value
        self.portfolio.update_value()
    
    def _track_performance(self):
        """Track performance metrics."""
        current_value = self.portfolio.total_value
        self.equity_curve.append(current_value)
        
        # Calculate drawdown
        peak_value = max(self.equity_curve)
        drawdown = (peak_value - current_value) / peak_value
        self.drawdown_curve.append(drawdown)
        
        # Calculate daily returns
        if len(self.equity_curve) > 1:
            daily_return = (current_value - self.equity_curve[-2]) / self.equity_curve[-2]
            self.daily_returns.append(daily_return)
    
    def _check_risk_limits(self) -> bool:
        """Check if risk limits are exceeded."""
        if len(self.drawdown_curve) > 0:
            current_drawdown = self.drawdown_curve[-1]
            if current_drawdown > self.config.max_drawdown_limit:
                self.max_drawdown_reached = True
                return True
        
        if len(self.daily_returns) > 0:
            daily_return = self.daily_returns[-1]
            if daily_return < -self.config.daily_loss_limit:
                self.daily_loss_exceeded = True
                return True
        
        return False
    
    def _calculate_results(self, strategy: BaseStrategy) -> BacktestResult:
        """Calculate comprehensive backtest results."""
        if len(self.equity_curve) < 2:
            raise ValueError("Insufficient data for results calculation")
        
        # Basic calculations
        initial_value = self.equity_curve[0]
        final_value = self.equity_curve[-1]
        total_return = (final_value - initial_value) / initial_value
        
        # Time calculations
        start_date = self.data.index[0]
        end_date = self.data.index[-1]
        duration_days = (end_date - start_date).days
        years = duration_days / 365.25
        
        # Performance metrics
        returns_array = np.array(self.daily_returns)
        annualized_return = (1 + total_return) ** (1/years) - 1 if years > 0 else 0
        volatility = np.std(returns_array) * np.sqrt(252) if len(returns_array) > 1 else 0
        
        # Risk metrics
        sharpe_ratio = (annualized_return - self.config.risk_free_rate) / volatility if volatility > 0 else 0
        max_drawdown = max(self.drawdown_curve) if self.drawdown_curve else 0
        
        # Trading statistics
        trades = self.trade_log
        total_trades = len([t for t in trades if t['side'] == 'SELL'])
        
        # Create result object
        result = BacktestResult(
            strategy_name=strategy.name,
            symbol=getattr(strategy, 'symbol', 'UNKNOWN'),
            start_date=start_date,
            end_date=end_date,
            duration_days=duration_days,
            total_return=total_return,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=0.0,  # TODO: Implement
            max_drawdown=max_drawdown,
            max_drawdown_duration=0,  # TODO: Implement
            calmar_ratio=annualized_return / max_drawdown if max_drawdown > 0 else 0,
            total_trades=total_trades,
            winning_trades=0,  # TODO: Calculate
            losing_trades=0,   # TODO: Calculate
            win_rate=0.0,      # TODO: Calculate
            avg_win=0.0,       # TODO: Calculate
            avg_loss=0.0,      # TODO: Calculate
            profit_factor=0.0, # TODO: Calculate
            var_95=0.0,        # TODO: Implement
            cvar_95=0.0,       # TODO: Implement
            beta=0.0,          # TODO: Implement
            alpha=0.0,         # TODO: Implement
            equity_curve=self.equity_curve,
            drawdown_curve=self.drawdown_curve,
            trade_log=trades
        )
        
        return result
