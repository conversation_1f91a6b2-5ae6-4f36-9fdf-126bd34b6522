"""
Test script for Signal Stack API endpoints.
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000/api/v1"

def test_api_endpoints():
    """Test various API endpoints."""
    
    print("Testing Signal Stack API endpoints...")
    
    # Test 1: Get available indicators
    print("\n1. Testing indicators endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/indicators/list")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Found {len(data['indicators'])} indicators")
            print(f"  Available indicators: {', '.join(data['indicators'][:5])}...")
        else:
            print(f"✗ Error: {response.status_code}")
    except Exception as e:
        print(f"✗ Exception: {e}")
    
    # Test 2: Get available strategies
    print("\n2. Testing backtesting strategies endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/backtesting/strategies")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Found {len(data['strategies'])} strategy types")
            for strategy in data['strategies']:
                print(f"  - {strategy['name']}: {strategy['description']}")
        else:
            print(f"✗ Error: {response.status_code}")
    except Exception as e:
        print(f"✗ Exception: {e}")
    
    # Test 3: Get screener presets
    print("\n3. Testing screener presets endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/screener/presets")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Found {len(data['presets'])} screener presets")
            for preset in data['presets']:
                print(f"  - {preset['name']}: {preset['description']}")
        else:
            print(f"✗ Error: {response.status_code}")
    except Exception as e:
        print(f"✗ Exception: {e}")
    
    # Test 4: Get market status
    print("\n4. Testing market status endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/market-data/market-status")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Market is {'open' if data['is_open'] else 'closed'}")
            print(f"  Timezone: {data['timezone']}")
        else:
            print(f"✗ Error: {response.status_code}")
    except Exception as e:
        print(f"✗ Exception: {e}")
    
    # Test 5: Get timeframes
    print("\n5. Testing timeframes endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/market-data/timeframes")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Available timeframes: {', '.join(data['timeframes'])}")
        else:
            print(f"✗ Error: {response.status_code}")
    except Exception as e:
        print(f"✗ Exception: {e}")
    
    # Test 6: Test paper trading sessions
    print("\n6. Testing paper trading sessions endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/paper-trading/sessions")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Found {len(data['sessions'])} active paper trading sessions")
        else:
            print(f"✗ Error: {response.status_code}")
    except Exception as e:
        print(f"✗ Exception: {e}")
    
    # Test 7: Test strategy types
    print("\n7. Testing strategy types endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/strategies/types")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Found {len(data['strategy_types'])} strategy types")
            for strategy_type in data['strategy_types']:
                print(f"  - {strategy_type['name']}: {len(strategy_type['default_parameters'])} parameters")
        else:
            print(f"✗ Error: {response.status_code}")
    except Exception as e:
        print(f"✗ Exception: {e}")
    
    print("\n" + "="*50)
    print("API Testing Complete!")
    print("✓ All basic endpoints are responding")
    print("✓ Server is running successfully")
    print("✓ API documentation available at: http://localhost:8000/docs")

if __name__ == "__main__":
    test_api_endpoints()
