import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Add,
  MoreVert,
  PlayArrow,
  Pause,
  Edit,
  Delete,
  Assessment,
  TrendingUp,
  TrendingDown,
} from '@mui/icons-material';
import axios from 'axios';

interface Strategy {
  id: string;
  name: string;
  description: string;
  type: 'technical' | 'fundamental' | 'quantitative';
  status: 'active' | 'inactive' | 'backtesting';
  symbols: string[];
  parameters: Record<string, any>;
  performance: {
    totalReturn: number;
    winRate: number;
    sharpeRatio: number;
    maxDrawdown: number;
    totalTrades: number;
  };
  createdAt: string;
  updatedAt: string;
}

const StrategyManager: React.FC = () => {
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingStrategy, setEditingStrategy] = useState<Strategy | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'technical' as const,
    symbols: '',
    parameters: '{}',
  });

  useEffect(() => {
    fetchStrategies();
  }, []);

  const fetchStrategies = async () => {
    try {
      const response = await axios.get('http://localhost:8002/api/v1/strategies');
      setStrategies(response.data);
    } catch (error) {
      console.error('Error fetching strategies:', error);
    }
  };

  const handleCreateStrategy = () => {
    setEditingStrategy(null);
    setFormData({
      name: '',
      description: '',
      type: 'technical',
      symbols: '',
      parameters: '{}',
    });
    setOpenDialog(true);
  };

  const handleEditStrategy = (strategy: Strategy) => {
    setEditingStrategy(strategy);
    setFormData({
      name: strategy.name,
      description: strategy.description,
      type: strategy.type,
      symbols: strategy.symbols.join(', '),
      parameters: JSON.stringify(strategy.parameters, null, 2),
    });
    setOpenDialog(true);
    setAnchorEl(null);
  };

  const handleSaveStrategy = async () => {
    try {
      const payload = {
        ...formData,
        symbols: formData.symbols.split(',').map(s => s.trim()),
        parameters: JSON.parse(formData.parameters),
      };

      if (editingStrategy) {
        await axios.put(`http://localhost:8002/api/v1/strategies/${editingStrategy.id}`, payload);
      } else {
        await axios.post('http://localhost:8002/api/v1/strategies', payload);
      }

      setOpenDialog(false);
      fetchStrategies();
    } catch (error) {
      console.error('Error saving strategy:', error);
    }
  };

  const handleDeleteStrategy = async (strategyId: string) => {
    try {
      await axios.delete(`http://localhost:8002/api/v1/strategies/${strategyId}`);
      fetchStrategies();
      setAnchorEl(null);
    } catch (error) {
      console.error('Error deleting strategy:', error);
    }
  };

  const handleToggleStrategy = async (strategy: Strategy) => {
    try {
      const newStatus = strategy.status === 'active' ? 'inactive' : 'active';
      await axios.patch(`http://localhost:8002/api/v1/strategies/${strategy.id}/status`, {
        status: newStatus,
      });
      fetchStrategies();
    } catch (error) {
      console.error('Error toggling strategy:', error);
    }
  };

  const handleBacktestStrategy = async (strategyId: string) => {
    try {
      await axios.post(`http://localhost:8002/api/v1/strategies/${strategyId}/backtest`);
      fetchStrategies();
      setAnchorEl(null);
    } catch (error) {
      console.error('Error starting backtest:', error);
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, strategy: Strategy) => {
    setAnchorEl(event.currentTarget);
    setSelectedStrategy(strategy);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedStrategy(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'default';
      case 'backtesting':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'technical':
        return 'primary';
      case 'fundamental':
        return 'secondary';
      case 'quantitative':
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Strategy Manager</Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={handleCreateStrategy}
        >
          Create Strategy
        </Button>
      </Box>

      <Grid container spacing={3}>
        {strategies.map((strategy) => (
          <Grid item xs={12} md={6} lg={4} key={strategy.id}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="h6" component="div">
                    {strategy.name}
                  </Typography>
                  <IconButton
                    size="small"
                    onClick={(e) => handleMenuClick(e, strategy)}
                  >
                    <MoreVert />
                  </IconButton>
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {strategy.description}
                </Typography>

                <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                  <Chip
                    label={strategy.status}
                    color={getStatusColor(strategy.status) as any}
                    size="small"
                  />
                  <Chip
                    label={strategy.type}
                    color={getTypeColor(strategy.type) as any}
                    variant="outlined"
                    size="small"
                  />
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Symbols: {strategy.symbols.join(', ')}
                </Typography>

                {/* Performance Metrics */}
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Performance
                  </Typography>
                  <Grid container spacing={1}>
                    <Grid item xs={6}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {strategy.performance.totalReturn >= 0 ? (
                          <TrendingUp color="success" fontSize="small" />
                        ) : (
                          <TrendingDown color="error" fontSize="small" />
                        )}
                        <Typography variant="caption" sx={{ ml: 0.5 }}>
                          Return: {strategy.performance.totalReturn.toFixed(2)}%
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="caption">
                        Win Rate: {strategy.performance.winRate.toFixed(1)}%
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="caption">
                        Sharpe: {strategy.performance.sharpeRatio.toFixed(2)}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="caption">
                        Trades: {strategy.performance.totalTrades}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              </CardContent>

              <CardActions>
                <FormControlLabel
                  control={
                    <Switch
                      checked={strategy.status === 'active'}
                      onChange={() => handleToggleStrategy(strategy)}
                      size="small"
                    />
                  }
                  label="Active"
                />
                <Button
                  size="small"
                  startIcon={<Assessment />}
                  onClick={() => handleBacktestStrategy(strategy.id)}
                  disabled={strategy.status === 'backtesting'}
                >
                  Backtest
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => selectedStrategy && handleEditStrategy(selectedStrategy)}>
          <Edit fontSize="small" sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={() => selectedStrategy && handleBacktestStrategy(selectedStrategy.id)}>
          <Assessment fontSize="small" sx={{ mr: 1 }} />
          Backtest
        </MenuItem>
        <MenuItem
          onClick={() => selectedStrategy && handleDeleteStrategy(selectedStrategy.id)}
          sx={{ color: 'error.main' }}
        >
          <Delete fontSize="small" sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Create/Edit Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingStrategy ? 'Edit Strategy' : 'Create New Strategy'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Strategy Name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Strategy Type</InputLabel>
                <Select
                  value={formData.type}
                  label="Strategy Type"
                  onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
                >
                  <MenuItem value="technical">Technical</MenuItem>
                  <MenuItem value="fundamental">Fundamental</MenuItem>
                  <MenuItem value="quantitative">Quantitative</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Symbols (comma-separated)"
                value={formData.symbols}
                onChange={(e) => setFormData({ ...formData, symbols: e.target.value })}
                placeholder="NIFTY, BANKNIFTY, RELIANCE"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Parameters (JSON)"
                multiline
                rows={6}
                value={formData.parameters}
                onChange={(e) => setFormData({ ...formData, parameters: e.target.value })}
                placeholder='{"rsi_period": 14, "ma_period": 20}'
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleSaveStrategy} variant="contained">
            {editingStrategy ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StrategyManager;
