#!/usr/bin/env python3
"""
Test script for the new Reference Backtesting Framework.
This script validates the performance and functionality of the reference approach.
"""

import sys
import time
import argparse
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.services.backtesting.reference_engine import (
    ReferenceBacktestEngine, 
    run_reference_backtest,
    run_simple_price_action_backtest
)
from app.services.backtesting.strategy_interface import strategy_registry

logger = get_logger(__name__)


def test_strategy_registry():
    """Test the strategy registry functionality."""
    logger.info("🧪 Testing Strategy Registry...")
    
    try:
        # List available strategies
        strategies = strategy_registry.list_strategies()
        logger.info(f"📋 Available strategies: {len(strategies)}")
        
        for strategy in strategies:
            logger.info(f"  - {strategy['name']}: {strategy['description']} (validated: {strategy['validated']})")
        
        # Test getting SimplePriceActionStrategy
        from app.services.backtesting.strategy_interface import get_strategy_adapter
        adapter = get_strategy_adapter("SimplePriceActionStrategy")
        
        if adapter:
            logger.info("✅ SimplePriceActionStrategy adapter loaded successfully")
            
            # Test strategy validation
            if adapter.validate_strategy():
                logger.info("✅ SimplePriceActionStrategy validation passed")
            else:
                logger.error("❌ SimplePriceActionStrategy validation failed")
                return False
        else:
            logger.error("❌ Failed to load SimplePriceActionStrategy adapter")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Strategy registry test failed: {e}")
        return False


def test_reference_engine_direct():
    """Test the reference engine directly."""
    logger.info("🧪 Testing Reference Engine (Direct)...")
    
    engine = None
    try:
        engine = ReferenceBacktestEngine()
        
        # Test with registered strategy
        result = engine.run_backtest(
            strategy_name="SimplePriceActionStrategy",
            symbol="NIFTY50",
            timeframe=30,
            generate_files=False
        )
        
        logger.info(f"✅ Direct engine test completed in {result['execution_time']:.2f}s")
        logger.info(f"📊 Results: {result['performance_summary']['total_trades']} trades, "
                   f"{result['performance_summary']['total_return']:.2f}% return")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Direct engine test failed: {e}")
        return None
    finally:
        if engine:
            engine.close()


def test_convenience_functions():
    """Test the convenience functions."""
    logger.info("🧪 Testing Convenience Functions...")
    
    try:
        # Test generic convenience function
        logger.info("Testing run_reference_backtest...")
        result1 = run_reference_backtest(
            strategy_name="SimplePriceActionStrategy",
            symbol="NIFTY50",
            timeframe=30,
            generate_files=False
        )
        
        logger.info(f"✅ Generic function test completed in {result1['execution_time']:.2f}s")
        
        # Test specific convenience function
        logger.info("Testing run_simple_price_action_backtest...")
        result2 = run_simple_price_action_backtest(
            symbol="NIFTY50",
            timeframe=30,
            generate_files=False
        )
        
        logger.info(f"✅ Specific function test completed in {result2['execution_time']:.2f}s")
        
        # Compare results
        if abs(result1['execution_time'] - result2['execution_time']) < 1.0:
            logger.info("✅ Both convenience functions produced consistent results")
        else:
            logger.warning("⚠️ Execution times differ significantly between convenience functions")
        
        return result2
        
    except Exception as e:
        logger.error(f"❌ Convenience functions test failed: {e}")
        return None


def test_performance_comparison(timeframe: int = 30):
    """Test performance against the reference implementation."""
    logger.info(f"🏁 Performance Comparison Test ({timeframe}min timeframe)")
    
    try:
        # Test new reference framework
        logger.info("🚀 Testing New Reference Framework...")
        start_time = time.time()
        
        new_result = run_simple_price_action_backtest(
            symbol="NIFTY50",
            timeframe=timeframe,
            generate_files=False
        )
        
        new_execution_time = time.time() - start_time
        
        logger.info(f"✅ New Framework: {new_execution_time:.2f}s total, "
                   f"{new_result['execution_time']:.2f}s engine time")
        
        # Test original reference implementation
        logger.info("🔬 Testing Original Reference Implementation...")
        
        reference_dir = project_root / "Reference" / "V7_IntradayCleanup_Best_30Min"
        sys.path.insert(0, str(reference_dir))
        
        import os
        original_cwd = os.getcwd()
        os.chdir(str(reference_dir))
        
        try:
            from SimplePriceActionStrategyMain_DB import main_db_version
            
            ref_start_time = time.time()
            ref_result = main_db_version(timeframe=timeframe)
            ref_execution_time = time.time() - ref_start_time
            
            logger.info(f"✅ Original Reference: {ref_execution_time:.2f}s total, "
                       f"{ref_result['execution_time']:.2f}s engine time")
            
        finally:
            os.chdir(original_cwd)
        
        # Performance comparison
        logger.info("\n" + "="*60)
        logger.info("📊 PERFORMANCE COMPARISON RESULTS")
        logger.info("="*60)
        logger.info(f"New Framework Total Time:     {new_execution_time:.2f}s")
        logger.info(f"Original Reference Time:      {ref_execution_time:.2f}s")
        logger.info(f"Performance Ratio:            {new_execution_time/ref_execution_time:.2f}x")
        
        if new_execution_time <= ref_execution_time * 1.1:  # Within 10%
            logger.info("🎉 NEW FRAMEWORK MEETS PERFORMANCE TARGET!")
        else:
            logger.warning("⚠️ New framework is slower than target")
        
        logger.info("="*60)
        
        return {
            'new_framework': {
                'total_time': new_execution_time,
                'engine_time': new_result['execution_time'],
                'trades': new_result['performance_summary']['total_trades'],
                'return': new_result['performance_summary']['total_return']
            },
            'original_reference': {
                'total_time': ref_execution_time,
                'engine_time': ref_result['execution_time'],
                'trades': ref_result['total_trades'],
                'return': ref_result['total_return']
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Performance comparison failed: {e}")
        return None


def main():
    """Main test function."""
    parser = argparse.ArgumentParser(description="Test Reference Backtesting Framework")
    parser.add_argument("--timeframe", type=int, default=30, help="Timeframe in minutes")
    parser.add_argument("--skip-performance", action="store_true", help="Skip performance comparison")
    parser.add_argument("--quick", action="store_true", help="Run quick tests only")
    
    args = parser.parse_args()
    
    logger.info("🚀 Starting Reference Backtesting Framework Tests")
    logger.info("="*60)
    
    success_count = 0
    total_tests = 4 if not args.skip_performance else 3
    
    # Test 1: Strategy Registry
    if test_strategy_registry():
        success_count += 1
    
    # Test 2: Direct Engine
    if not args.quick:
        if test_reference_engine_direct():
            success_count += 1
    else:
        total_tests -= 1
    
    # Test 3: Convenience Functions
    if test_convenience_functions():
        success_count += 1
    
    # Test 4: Performance Comparison
    if not args.skip_performance and not args.quick:
        if test_performance_comparison(args.timeframe):
            success_count += 1
    elif args.skip_performance:
        total_tests -= 1
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("📋 TEST SUMMARY")
    logger.info("="*60)
    logger.info(f"Tests Passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        logger.info("🎉 ALL TESTS PASSED!")
        return 0
    else:
        logger.error("❌ SOME TESTS FAILED!")
        return 1


if __name__ == "__main__":
    exit(main())
