[{"C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\components\\Dashboard.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\components\\MarketData.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\components\\StrategyManager.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\components\\BacktestingDashboard.tsx": "7"}, {"size": 554, "mtime": 1752403988574, "results": "8", "hashOfConfig": "9"}, {"size": 425, "mtime": 1752403986856, "results": "10", "hashOfConfig": "9"}, {"size": 1354, "mtime": 1752532442342, "results": "11", "hashOfConfig": "9"}, {"size": 9767, "mtime": 1752532536823, "results": "12", "hashOfConfig": "9"}, {"size": 10802, "mtime": 1752404253233, "results": "13", "hashOfConfig": "9"}, {"size": 12619, "mtime": 1752404294403, "results": "14", "hashOfConfig": "9"}, {"size": 16171, "mtime": 1752532410466, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "dzpwqd", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\App.tsx", ["37"], [], "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\components\\Dashboard.tsx", ["38"], [], "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\components\\MarketData.tsx", ["39"], [], "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\components\\StrategyManager.tsx", ["40", "41", "42"], [], "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\components\\BacktestingDashboard.tsx", ["43", "44"], [], {"ruleId": "45", "severity": 1, "message": "46", "line": 1, "column": 17, "nodeType": "47", "messageId": "48", "endLine": 1, "endColumn": 25}, {"ruleId": "45", "severity": 1, "message": "49", "line": 59, "column": 26, "nodeType": "47", "messageId": "48", "endLine": 59, "endColumn": 43}, {"ruleId": "50", "severity": 1, "message": "51", "line": 57, "column": 6, "nodeType": "52", "endLine": 57, "endColumn": 48, "suggestions": "53"}, {"ruleId": "45", "severity": 1, "message": "54", "line": 4, "column": 3, "nodeType": "47", "messageId": "48", "endLine": 4, "endColumn": 8}, {"ruleId": "45", "severity": 1, "message": "55", "line": 29, "column": 3, "nodeType": "47", "messageId": "48", "endLine": 29, "endColumn": 12}, {"ruleId": "45", "severity": 1, "message": "56", "line": 30, "column": 3, "nodeType": "47", "messageId": "48", "endLine": 30, "endColumn": 8}, {"ruleId": "45", "severity": 1, "message": "57", "line": 25, "column": 3, "nodeType": "47", "messageId": "48", "endLine": 25, "endColumn": 10}, {"ruleId": "45", "severity": 1, "message": "58", "line": 32, "column": 3, "nodeType": "47", "messageId": "48", "endLine": 32, "endColumn": 11}, "@typescript-eslint/no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'setSelectedSymbol' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'filterAndSortData'. Either include it or remove the dependency array.", "ArrayExpression", ["59"], "'Paper' is defined but never used.", "'PlayArrow' is defined but never used.", "'Pause' is defined but never used.", "'Divider' is defined but never used.", "'Timeline' is defined but never used.", {"desc": "60", "fix": "61"}, "Update the dependencies array to be: [marketData, searchTerm, sortBy, filterBy, filterAndSortData]", {"range": "62", "text": "63"}, [1273, 1315], "[marketData, searchTerm, sortBy, filterBy, filterAndSortData]"]