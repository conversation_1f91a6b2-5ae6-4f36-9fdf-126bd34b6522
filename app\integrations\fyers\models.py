"""
Data models for Fyers API integration.
"""

from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from datetime import datetime
from enum import Enum


class MarketStatus(str, Enum):
    """Market status enumeration."""
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    PRE_OPEN = "PRE_OPEN"
    POST_CLOSE = "POST_CLOSE"


class OrderType(str, Enum):
    """Order type enumeration."""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP = "STOP"
    STOP_LIMIT = "STOP_LIMIT"


class OrderSide(str, Enum):
    """Order side enumeration."""
    BUY = "BUY"
    SELL = "SELL"


class TimeFrame(str, Enum):
    """Timeframe enumeration for historical data."""
    MIN_1 = "1"
    MIN_3 = "3"
    MIN_5 = "5"
    MIN_15 = "15"
    MIN_30 = "30"
    MIN_60 = "60"
    DAY_1 = "1D"
    WEEK_1 = "1W"


@dataclass
class QuoteData:
    """Market quote data structure."""
    symbol: str
    ltp: float
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    prev_close: float
    volume: int
    change: float
    change_percent: float
    timestamp: datetime
    market_status: MarketStatus = MarketStatus.CLOSED
    
    @classmethod
    def from_fyers_response(cls, symbol: str, data: Dict[str, Any]) -> 'QuoteData':
        """Create QuoteData from Fyers API response."""
        return cls(
            symbol=symbol,
            ltp=float(data.get('lp', 0)),
            open_price=float(data.get('o', 0)),
            high_price=float(data.get('h', 0)),
            low_price=float(data.get('l', 0)),
            close_price=float(data.get('c', 0)),
            prev_close=float(data.get('prev_close_price', 0)),
            volume=int(data.get('v', 0)),
            change=float(data.get('ch', 0)),
            change_percent=float(data.get('chp', 0)),
            timestamp=datetime.now(),
            market_status=MarketStatus.OPEN if data.get('market_status') == 1 else MarketStatus.CLOSED
        )


@dataclass
class OHLCVData:
    """OHLCV historical data structure."""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    
    @classmethod
    def from_fyers_response(cls, data: List[Any]) -> 'OHLCVData':
        """Create OHLCVData from Fyers API response."""
        return cls(
            timestamp=datetime.fromtimestamp(data[0]),
            open=float(data[1]),
            high=float(data[2]),
            low=float(data[3]),
            close=float(data[4]),
            volume=int(data[5])
        )


@dataclass
class SymbolInfo:
    """Symbol information structure."""
    symbol: str
    name: str
    exchange: str
    segment: str
    token: str
    lot_size: int
    tick_size: float
    is_active: bool = True
    
    @classmethod
    def from_fyers_master(cls, data: Dict[str, Any]) -> 'SymbolInfo':
        """Create SymbolInfo from Fyers master data."""
        return cls(
            symbol=data.get('symbol', ''),
            name=data.get('name', ''),
            exchange=data.get('exchange', ''),
            segment=data.get('segment', ''),
            token=data.get('token', ''),
            lot_size=int(data.get('lot_size', 1)),
            tick_size=float(data.get('tick_size', 0.05))
        )


@dataclass
class OrderRequest:
    """Order request structure."""
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: Optional[float] = None
    stop_price: Optional[float] = None
    disclosed_quantity: Optional[int] = None
    validity: str = "DAY"
    product_type: str = "CNC"
    
    def to_fyers_format(self) -> Dict[str, Any]:
        """Convert to Fyers API format."""
        order_data = {
            "symbol": self.symbol,
            "qty": self.quantity,
            "type": self.order_type.value,
            "side": 1 if self.side == OrderSide.BUY else -1,
            "productType": self.product_type,
            "validity": self.validity
        }
        
        if self.price is not None:
            order_data["limitPrice"] = self.price
        
        if self.stop_price is not None:
            order_data["stopPrice"] = self.stop_price
        
        if self.disclosed_quantity is not None:
            order_data["disclosedQty"] = self.disclosed_quantity
        
        return order_data


@dataclass
class OrderResponse:
    """Order response structure."""
    order_id: str
    status: str
    message: str
    symbol: str
    quantity: int
    price: Optional[float] = None
    
    @classmethod
    def from_fyers_response(cls, data: Dict[str, Any]) -> 'OrderResponse':
        """Create OrderResponse from Fyers API response."""
        return cls(
            order_id=data.get('id', ''),
            status=data.get('status', ''),
            message=data.get('message', ''),
            symbol=data.get('symbol', ''),
            quantity=int(data.get('qty', 0)),
            price=float(data.get('price', 0)) if data.get('price') else None
        )


@dataclass
class Position:
    """Position data structure."""
    symbol: str
    quantity: int
    average_price: float
    current_price: float
    pnl: float
    pnl_percent: float
    side: str
    product_type: str
    
    @classmethod
    def from_fyers_response(cls, data: Dict[str, Any]) -> 'Position':
        """Create Position from Fyers API response."""
        return cls(
            symbol=data.get('symbol', ''),
            quantity=int(data.get('qty', 0)),
            average_price=float(data.get('avgPrice', 0)),
            current_price=float(data.get('ltp', 0)),
            pnl=float(data.get('pl', 0)),
            pnl_percent=float(data.get('plPercent', 0)),
            side=data.get('side', ''),
            product_type=data.get('productType', '')
        )


@dataclass
class FundsInfo:
    """Account funds information."""
    available_balance: float
    used_margin: float
    total_balance: float
    
    @classmethod
    def from_fyers_response(cls, data: Dict[str, Any]) -> 'FundsInfo':
        """Create FundsInfo from Fyers API response."""
        fund_limit = data.get('fund_limit', {})
        return cls(
            available_balance=float(fund_limit.get('equityAmount', 0)),
            used_margin=float(fund_limit.get('utilisedAmount', 0)),
            total_balance=float(fund_limit.get('equityAmount', 0) + fund_limit.get('utilisedAmount', 0))
        )
