"""
Real-time data pipeline for live market data ingestion and processing.
"""

import asyncio
import threading
from typing import List, Dict, Optional, Callable
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from collections import deque
import time

from app.core.logging import get_logger
from app.database.connection import get_db
from app.services.market_data_service import MarketDataService
from app.services.aggregation_service import AggregationService
from app.integrations.fyers.models import QuoteData
from app.database.models import MarketType

logger = get_logger(__name__)


class RealTimeDataPipeline:
    """Real-time data pipeline for live market data processing."""
    
    def __init__(self):
        """Initialize the real-time data pipeline."""
        self.is_running = False
        self.market_service: Optional[MarketDataService] = None
        self.aggregation_service: Optional[AggregationService] = None
        
        # Data buffers for batch processing
        self.data_buffer: Dict[str, deque] = {}
        self.buffer_size = 100  # Number of ticks to buffer before processing
        self.buffer_timeout = 60  # Seconds to wait before processing partial buffer
        
        # Threading
        self.processing_thread: Optional[threading.Thread] = None
        self.last_buffer_flush = {}
        
        # Statistics
        self.stats = {
            'ticks_received': 0,
            'ticks_processed': 0,
            'errors': 0,
            'last_update': None,
            'symbols_active': 0
        }
        
        # Callbacks for external notifications
        self.data_callbacks: List[Callable] = []
    
    def initialize(self) -> bool:
        """
        Initialize the pipeline with database connections.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get database session
            db = next(get_db())
            
            # Initialize services
            self.market_service = MarketDataService(db)
            self.aggregation_service = AggregationService(db)
            
            # Initialize Fyers connection
            if not self.market_service.initialize_fyers_connection():
                logger.error("Failed to initialize Fyers connection")
                return False
            
            # Add data callback
            self.market_service.add_data_callback(self._on_market_data)
            
            logger.info("Real-time data pipeline initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize pipeline: {e}")
            return False
    
    def start(self, symbols: List[str]) -> bool:
        """
        Start the real-time data pipeline.
        
        Args:
            symbols: List of symbols to monitor
            
        Returns:
            True if started successfully, False otherwise
        """
        try:
            if self.is_running:
                logger.warning("Pipeline is already running")
                return True
            
            if not self.market_service:
                logger.error("Pipeline not initialized")
                return False
            
            logger.info(f"Starting real-time pipeline for {len(symbols)} symbols")
            
            # Initialize data buffers
            for symbol in symbols:
                self.data_buffer[symbol] = deque(maxlen=self.buffer_size * 2)
                self.last_buffer_flush[symbol] = time.time()
            
            # Start real-time data streaming
            if not self.market_service.start_real_time_data(symbols):
                logger.error("Failed to start real-time data streaming")
                return False
            
            # Start processing thread
            self.is_running = True
            self.processing_thread = threading.Thread(
                target=self._processing_loop, 
                daemon=True
            )
            self.processing_thread.start()
            
            self.stats['symbols_active'] = len(symbols)
            logger.info("Real-time data pipeline started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start pipeline: {e}")
            return False
    
    def stop(self) -> None:
        """Stop the real-time data pipeline."""
        try:
            if not self.is_running:
                return
            
            logger.info("Stopping real-time data pipeline...")
            
            # Stop the pipeline
            self.is_running = False
            
            # Stop market data streaming
            if self.market_service:
                self.market_service.stop_real_time_data()
            
            # Wait for processing thread to finish
            if self.processing_thread and self.processing_thread.is_alive():
                self.processing_thread.join(timeout=10)
            
            # Process remaining buffered data
            self._flush_all_buffers()
            
            logger.info("Real-time data pipeline stopped")
            
        except Exception as e:
            logger.error(f"Error stopping pipeline: {e}")
    
    def add_data_callback(self, callback: Callable) -> None:
        """
        Add callback for processed data notifications.
        
        Args:
            callback: Function to call with processed data
        """
        self.data_callbacks.append(callback)
    
    def get_statistics(self) -> Dict:
        """Get pipeline statistics."""
        return {
            **self.stats,
            'is_running': self.is_running,
            'buffer_sizes': {symbol: len(buffer) for symbol, buffer in self.data_buffer.items()},
            'websocket_status': (
                self.market_service.websocket_client.connection_status 
                if self.market_service and self.market_service.websocket_client 
                else {'connected': False}
            )
        }
    
    def _on_market_data(self, symbol: str, quote_data: QuoteData) -> None:
        """
        Handle incoming market data.
        
        Args:
            symbol: Symbol name
            quote_data: Quote data from Fyers
        """
        try:
            # Update statistics
            self.stats['ticks_received'] += 1
            self.stats['last_update'] = datetime.now()
            
            # Convert to 1-minute OHLCV format
            minute_timestamp = quote_data.timestamp.replace(second=0, microsecond=0)
            
            ohlcv_data = {
                'timestamp': minute_timestamp,
                'open': quote_data.ltp,
                'high': quote_data.ltp,
                'low': quote_data.ltp,
                'close': quote_data.ltp,
                'volume': 1,  # We'll aggregate volume properly
                'tick_count': 1,
                'last_price': quote_data.ltp
            }
            
            # Add to buffer
            if symbol in self.data_buffer:
                self.data_buffer[symbol].append(ohlcv_data)
            
            # Notify callbacks
            for callback in self.data_callbacks:
                try:
                    callback(symbol, quote_data)
                except Exception as e:
                    logger.error(f"Error in data callback: {e}")
            
        except Exception as e:
            logger.error(f"Error processing market data for {symbol}: {e}")
            self.stats['errors'] += 1
    
    def _processing_loop(self) -> None:
        """Main processing loop running in separate thread."""
        logger.info("Processing loop started")
        
        while self.is_running:
            try:
                current_time = time.time()
                
                # Check each symbol's buffer
                for symbol in list(self.data_buffer.keys()):
                    buffer = self.data_buffer[symbol]
                    last_flush = self.last_buffer_flush.get(symbol, 0)
                    
                    # Process if buffer is full or timeout reached
                    should_process = (
                        len(buffer) >= self.buffer_size or
                        (len(buffer) > 0 and current_time - last_flush > self.buffer_timeout)
                    )
                    
                    if should_process:
                        self._process_symbol_buffer(symbol)
                
                # Sleep briefly to avoid busy waiting
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error in processing loop: {e}")
                self.stats['errors'] += 1
                time.sleep(5)  # Wait longer on error
        
        logger.info("Processing loop stopped")
    
    def _process_symbol_buffer(self, symbol: str) -> None:
        """
        Process buffered data for a symbol.
        
        Args:
            symbol: Symbol to process
        """
        try:
            buffer = self.data_buffer[symbol]
            if not buffer:
                return
            
            # Extract all data from buffer
            data_points = []
            while buffer:
                data_points.append(buffer.popleft())
            
            if not data_points:
                return
            
            # Aggregate ticks into 1-minute candles
            minute_candles = self._aggregate_ticks_to_minutes(data_points)
            
            # Store in database
            if minute_candles:
                success = self._store_minute_data(symbol, minute_candles)
                
                if success:
                    self.stats['ticks_processed'] += len(data_points)
                    
                    # Trigger aggregation for higher timeframes
                    self._trigger_timeframe_aggregation(symbol, minute_candles)
                else:
                    logger.error(f"Failed to store minute data for {symbol}")
            
            # Update last flush time
            self.last_buffer_flush[symbol] = time.time()
            
        except Exception as e:
            logger.error(f"Error processing buffer for {symbol}: {e}")
            self.stats['errors'] += 1
    
    def _aggregate_ticks_to_minutes(self, data_points: List[Dict]) -> List[Dict]:
        """
        Aggregate tick data into 1-minute candles.
        
        Args:
            data_points: List of tick data
            
        Returns:
            List of 1-minute OHLCV candles
        """
        try:
            # Group by minute timestamp
            minute_groups = {}
            
            for point in data_points:
                minute_key = point['timestamp']
                
                if minute_key not in minute_groups:
                    minute_groups[minute_key] = {
                        'timestamp': minute_key,
                        'open': point['last_price'],
                        'high': point['last_price'],
                        'low': point['last_price'],
                        'close': point['last_price'],
                        'volume': 0,
                        'tick_count': 0
                    }
                
                group = minute_groups[minute_key]
                
                # Update OHLC
                group['high'] = max(group['high'], point['last_price'])
                group['low'] = min(group['low'], point['last_price'])
                group['close'] = point['last_price']  # Last price becomes close
                group['volume'] += point.get('volume', 1)
                group['tick_count'] += 1
            
            # Convert to list and sort by timestamp
            candles = list(minute_groups.values())
            candles.sort(key=lambda x: x['timestamp'])
            
            return candles
            
        except Exception as e:
            logger.error(f"Error aggregating ticks to minutes: {e}")
            return []
    
    def _store_minute_data(self, symbol: str, minute_candles: List[Dict]) -> bool:
        """
        Store 1-minute candle data in database.
        
        Args:
            symbol: Symbol name
            minute_candles: List of 1-minute candles
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.market_service:
                return False
            
            # Convert to storage format (remove extra fields)
            storage_data = []
            for candle in minute_candles:
                storage_data.append({
                    'timestamp': candle['timestamp'],
                    'open': candle['open'],
                    'high': candle['high'],
                    'low': candle['low'],
                    'close': candle['close'],
                    'volume': candle['volume']
                })
            
            # Store using data service
            return self.market_service.data_service.store_ohlcv_data(symbol, storage_data)
            
        except Exception as e:
            logger.error(f"Error storing minute data for {symbol}: {e}")
            return False
    
    def _trigger_timeframe_aggregation(self, symbol: str, minute_candles: List[Dict]) -> None:
        """
        Trigger aggregation for higher timeframes.
        
        Args:
            symbol: Symbol name
            minute_candles: New minute candles
        """
        try:
            if not self.aggregation_service or not minute_candles:
                return
            
            # Get time range for aggregation
            start_time = min(candle['timestamp'] for candle in minute_candles)
            end_time = max(candle['timestamp'] for candle in minute_candles)
            
            # Extend range slightly to ensure we capture complete periods
            start_time = start_time - timedelta(hours=1)
            end_time = end_time + timedelta(minutes=1)
            
            # Aggregate common timeframes
            timeframes = ['5m', '15m', '30m', '1h']
            
            for timeframe in timeframes:
                try:
                    self.aggregation_service.aggregate_symbol_data(
                        symbol, timeframe, start_time, end_time
                    )
                except Exception as e:
                    logger.error(f"Error aggregating {symbol} for {timeframe}: {e}")
            
        except Exception as e:
            logger.error(f"Error triggering timeframe aggregation for {symbol}: {e}")
    
    def _flush_all_buffers(self) -> None:
        """Flush all remaining data in buffers."""
        try:
            logger.info("Flushing all data buffers...")
            
            for symbol in list(self.data_buffer.keys()):
                if self.data_buffer[symbol]:
                    self._process_symbol_buffer(symbol)
            
            logger.info("All buffers flushed")
            
        except Exception as e:
            logger.error(f"Error flushing buffers: {e}")
