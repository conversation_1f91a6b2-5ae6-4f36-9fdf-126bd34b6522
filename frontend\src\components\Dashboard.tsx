import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Grid,
  Paper,
  Typography,
  AppBar,
  Toolbar,
  Container,
  Card,
  CardContent,
  Chip,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  ShowChart,
  Assessment,
  Settings,
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  AccountBalance,
  Timeline,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import io from 'socket.io-client';

interface MarketData {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  timestamp: string;
}

interface OHLCVData {
  timestamp: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [marketData, setMarketData] = useState<MarketData[]>([]);
  const [chartData, setChartData] = useState<OHLCVData[]>([]);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedSymbol, setSelectedSymbol] = useState('NIFTY');
  const [connectionStatus, setConnectionStatus] = useState('Disconnected');

  useEffect(() => {
    // Initialize WebSocket connection
    const socket = io('http://localhost:8000');

    socket.on('connect', () => {
      setConnectionStatus('Connected');
      console.log('Connected to WebSocket server');
    });

    socket.on('disconnect', () => {
      setConnectionStatus('Disconnected');
      console.log('Disconnected from WebSocket server');
    });

    socket.on('market_data', (data: MarketData) => {
      setMarketData(prev => {
        const updated = prev.filter(item => item.symbol !== data.symbol);
        return [...updated, data];
      });
    });

    socket.on('ohlcv_data', (data: OHLCVData[]) => {
      setChartData(data);
    });

    // Cleanup on unmount
    return () => {
      socket.disconnect();
    };
  }, []);

  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
    }).format(price);
  };

  const formatVolume = (volume: number) => {
    if (volume >= 10000000) {
      return `${(volume / 10000000).toFixed(1)}Cr`;
    } else if (volume >= 100000) {
      return `${(volume / 100000).toFixed(1)}L`;
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(1)}K`;
    }
    return volume.toString();
  };

  const sidebarItems = [
    { text: 'Dashboard', icon: <DashboardIcon />, active: true, path: '/dashboard' },
    { text: 'Market Data', icon: <ShowChart />, path: '/market-data' },
    { text: 'Strategies', icon: <Assessment />, path: '/strategies' },
    { text: 'Backtesting', icon: <Timeline />, path: '/backtesting' },
    { text: 'Paper Trading', icon: <AccountBalance />, path: '/paper-trading' },
    { text: 'Settings', icon: <Settings />, path: '/settings' },
  ];

  const handleNavigation = (path: string) => {
    navigate(path);
    setDrawerOpen(false);
  };

  return (
    <Box sx={{ display: 'flex' }}>
      {/* App Bar */}
      <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            onClick={toggleDrawer}
            edge="start"
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            Signal Stack Trading Platform
          </Typography>
          <Chip
            label={connectionStatus}
            color={connectionStatus === 'Connected' ? 'success' : 'error'}
            variant="outlined"
            size="small"
          />
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Drawer
        variant="temporary"
        open={drawerOpen}
        onClose={toggleDrawer}
        sx={{
          width: 240,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 240,
            boxSizing: 'border-box',
          },
        }}
      >
        <Toolbar />
        <Box sx={{ overflow: 'auto' }}>
          <List>
            {sidebarItems.map((item, index) => (
              <ListItem
                button
                key={item.text}
                selected={item.active}
                onClick={() => handleNavigation(item.path)}
                sx={{
                  '&.Mui-selected': {
                    backgroundColor: 'primary.main',
                    color: 'white',
                    '& .MuiListItemIcon-root': {
                      color: 'white',
                    },
                  },
                }}
              >
                <ListItemIcon>{item.icon}</ListItemIcon>
                <ListItemText primary={item.text} />
              </ListItem>
            ))}
          </List>
          <Divider />
        </Box>
      </Drawer>

      {/* Main Content */}
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <Toolbar />
        <Container maxWidth="xl">
          <Typography variant="h4" gutterBottom>
            Trading Dashboard
          </Typography>

          {/* Market Overview Cards */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            {marketData.slice(0, 4).map((data) => (
              <Grid item xs={12} sm={6} md={3} key={data.symbol}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" component="div">
                      {data.symbol}
                    </Typography>
                    <Typography variant="h4" color="text.primary">
                      {formatPrice(data.price)}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      {data.change >= 0 ? (
                        <TrendingUp color="success" />
                      ) : (
                        <TrendingDown color="error" />
                      )}
                      <Typography
                        variant="body2"
                        color={data.change >= 0 ? 'success.main' : 'error.main'}
                        sx={{ ml: 1 }}
                      >
                        {data.change >= 0 ? '+' : ''}{data.change.toFixed(2)} ({data.changePercent.toFixed(2)}%)
                      </Typography>
                    </Box>
                    <Typography variant="caption" color="text.secondary">
                      Volume: {formatVolume(data.volume)}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Chart Section */}
          <Grid container spacing={3}>
            <Grid item xs={12} lg={8}>
              <Paper sx={{ p: 2, height: 400 }}>
                <Typography variant="h6" gutterBottom>
                  {selectedSymbol} Price Chart
                </Typography>
                <ResponsiveContainer width="100%" height="90%">
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="timestamp" 
                      tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                    />
                    <YAxis domain={['dataMin - 10', 'dataMax + 10']} />
                    <Tooltip 
                      labelFormatter={(value) => new Date(value).toLocaleString()}
                      formatter={(value: number) => [formatPrice(value), 'Price']}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="close" 
                      stroke="#2196f3" 
                      strokeWidth={2}
                      dot={false}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Paper>
            </Grid>

            {/* Market Summary */}
            <Grid item xs={12} lg={4}>
              <Paper sx={{ p: 2, height: 400 }}>
                <Typography variant="h6" gutterBottom>
                  Market Summary
                </Typography>
                <Box sx={{ mt: 2 }}>
                  {marketData.map((data) => (
                    <Box
                      key={data.symbol}
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        py: 1,
                        borderBottom: '1px solid #eee',
                      }}
                    >
                      <Typography variant="body2">{data.symbol}</Typography>
                      <Box sx={{ textAlign: 'right' }}>
                        <Typography variant="body2">
                          {formatPrice(data.price)}
                        </Typography>
                        <Typography
                          variant="caption"
                          color={data.change >= 0 ? 'success.main' : 'error.main'}
                        >
                          {data.change >= 0 ? '+' : ''}{data.changePercent.toFixed(2)}%
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </Box>
  );
};

export default Dashboard;
